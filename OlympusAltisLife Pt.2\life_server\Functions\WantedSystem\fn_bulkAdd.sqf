//	File: fn_bulkAdd.sqf
//	Author: Poseidon
//	Description: Adds all the players saved crimes to the wanted list

private["_uid","_crimes","_name","_index","_bounty","_counter","_crimeString","_crimesOnly","_type","_i"];
_uid = param [0,"",[""]];
_name = param [1,"",[""]];
_crimes = param [2,[],[[]]];
if(_uid == "" || _name == "") exitWith {};

//check to see if wanted, then wipe them so you can add back all crimes, or add back none if pardoned on a diff server
_index = [_uid,life_wanted_list] call OEC_fnc_index;

if(_index != -1) then {
	life_wanted_list set[_index,-1];
	life_wanted_list = life_wanted_list - [-1];
};

_bounty  = _crimes select 0;
if(_bounty < 1) exitWith {};

_crimes set[0,-1];
_crimes = _crimes - [-1];
_counter = 1;
_crimeString = [];
_type = "";
{
	_type = switch(_counter) do	{
		case 1: {"Vehicular Manslaughter"};
		case 2: {"Manslaughter"};
		case 3: {"Escaping Jail"};
		case 4: {"Assault"};
		case 5: {"Attempted Rape"};
		case 6: {"Attempted Grand Theft Auto"};
		case 7: {"Use of illegal explosives"};
		case 8: {"Robbery"};
		case 9: {"Kidnapping"};
		case 10: {"Attempted Kidnapping"};
		case 11: {"Grand Theft Auto"};
		case 12: {"Petty Theft"};
		case 13: {"Hit and Run"};
		case 14: {"Possession of Contraband"};
		case 15: {"Drug Possession"};
		case 16: {"Drug Trafficking"};
		case 17: {"Burglary"};
		case 18: {"Organ Dealing"};
		case 19: {"Driving w/o license"};
		case 20: {"Driving w/o lights"};
		case 21: {"Attp. Robbery"};
		case 22: {"Veh. Theft"};
		case 23: {"Attp. Veh. Theft"};
		case 24: {"Attp. Manslaughter"};
		case 25: {"Speeding"};
		case 26: {"Reckless Driving"};
		case 27: {"Pos. of APD Equip."};
		case 28: {"Ilg. Aerial Veh. Landing"};
		case 29: {"Operating an ilg. veh."};
		case 30: {"Hit and Run"};
		case 31: {"Resisting Arrest"};
		case 32: {"Verbal Threats"};
		case 33: {"Verbal Insults"};
		case 34: {"Entering a Police Area"};
		case 35: {"Destruction of property"};
		case 36: {"Pos. of firearms w.o license"};
		case 37: {"Pos. of an ilg. weapon"};
		case 38: {"Use of firearms within city"};
		case 39: {"Hostage Situation"};
		case 40: {"Terrorist Acts"};
		case 41: {"Flying/Hovering below 150m"};
		case 42: {"Aiding in jail break"};
		case 43: {"Flying w/o a pilot license"};
		case 44: {"Aiding in Reserve Robbery"};
		case 45: {"Attp. Reserve Robbery"};
		case 46: {"Insurance Fraud"};
		case 47: {"Disobeying an Officer"};
		case 48: {"Obstruction of Traffic"};
		case 49: {"Weapon Trafficking"};
		case 50: {"Avoiding a Checkpoint"};
		case 51: {"Usage of Drugs in Public"};
		case 52: {"Disturbing the Peace"};
		case 53: {"LEO Manslaughter"};
		case 54: {"Gov't Cyber Attack"};
		case 55: {"Destruction of Gov't Property"};
		case 56: {"Party to a Crime"};
		case 57: {"Obstruction of Justice"};
		case 58: {"Misuse of Emergency System"};
		case 59: {"Aiding in BW Robbery"};
		case 60: {"Gas Station Robbery"};
		case 61: {"Organ Harvesting"};
		case 62: {"Pos. of Illegal Organ"};
		case 63: {"Gang Homicide"};
		case 64: {"Unlawful Taser Usage"};
		case 65: {"Attp. BW Robbery"};
		case 66: {"Attp. Jail Break"};
		case 67: {"Kidnapping Gov't Official"};
		case 68: {"Aiding in Pharm. Robbery"};
		case 69: {"Pos. of Explosives"};
		case 70: {"Flying w/o Collision Lights"};
		case 71: {"Attp. Bank Robbery"};
		case 72: {"Aiding in Bank Robbery"};
		case 73: {"Pos. of Ilg. Equipment"};
		case 74: {"Public Urination"};
		case 75: {"Titan Hit"};
	};

	if (_x > 0) then {
		_crimeString pushBack [_type,_x];
	};
	_counter = _counter + 1;

} forEach _crimes;

life_wanted_list pushBack [_name,_uid,_crimeString,_bounty];
