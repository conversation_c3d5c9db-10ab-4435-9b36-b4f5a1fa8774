#include "..\..\..\macro.h"
//  File: fn_buyGangBldg.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine
//	Modifications: <PERSON> "tk<PERSON>" <PERSON>, Fusah
//	Description: Buys a gang building, based off fn_buyHouse.sqf

params [
	["_building",objNull,[obj<PERSON><PERSON>]]
];
if (oev_houseTransaction) exitWith {hint "You currently have an active transaction, please wait.";};
if (isNull _building) exitWith {};
if !(typeOf _building isEqualTo "Land_i_Shed_Ind_F") exitWith {};
if !(_building isKindOf "House_F") exitWith {};
if (_building getVariable ["restricted_shed",false]) exitWith {};
if !((oev_gang_data select 2) isEqualTo 5) exitWith {};
if (!license_civ_home) exitWith {hint localize "STR_House_License"};

oev_gangfund_ready = false;
oev_gang_funds = -1;
[[0,oev_gang_data select 0,player],"OES_fnc_gangBank",false,false] spawn OEC_fnc_MP;

waitUntil{oev_gangfund_ready};
uiSleep 0.5;

if (life_donation_house) then {

	if (oev_gang_funds < ********) exitWith {hint "You don't have enough money in your gang bank to make this purchase!";};

	private _action = [
		"This building is available for $17,000,000. It supports up to 900 physical storage space and offers other features for you and your gang to use!",
		"Purchase Gang Building",
		localize "STR_Global_Buy",
		localize "STR_Global_Cancel"
	] call BIS_fnc_GUImessage;

	if (_action) then {
		if (oev_gang_funds < ********) exitWith {hint "You don't have enough money in your gang bank to make this purchase!";};
		if (oev_houseTransaction) exitWith {hint "You currently have an active transaction, please wait.";};
		oev_houseTransaction = true;
		oev_action_inUse = true;

		[[player,_building,(typeOf _building),(oev_gang_data select 0),(oev_gang_data select 1)],"OES_fnc_addGangBldg",false,false] spawn OEC_fnc_MP;
		hint "Sending purchase request to realtor...";
	};
	} else {

	if (oev_gang_funds < ********) exitWith {hint "You don't have enough money in your gang bank to make this purchase!";};

	private _action = [
		"This building is available for $20,000,000. It supports up to 900 physical storage space and offers other features for you and your gang to use!",
		"Purchase Gang Building",
		localize "STR_Global_Buy",
		localize "STR_Global_Cancel"
	] call BIS_fnc_GUImessage;

	if (_action) then {
		if (oev_gang_funds < ********) exitWith {hint "You don't have enough money in your gang bank to make this purchase!";};
		if (oev_houseTransaction) exitWith {hint "You currently have an active transaction, please wait.";};
		oev_houseTransaction = true;
		oev_action_inUse = true;

		[[player,_building,(typeOf _building),(oev_gang_data select 0),(oev_gang_data select 1)],"OES_fnc_addGangBldg",false,false] spawn OEC_fnc_MP;
		hint "Sending purchase request to realtor...";
	};

};

