//	File: fn_redeemDepositBox.sqf
//	Author: <PERSON><PERSON><PERSON>
//	Description: Redeem's player's deposit box balance to their bank balance

params [
	["_player",objNull,[objNull]]
];
private ["_queryResult","_query"];

_query = format["SELECT `deposit_box` FROM `players` WHERE `playerid`='%1'", getPlayerUID _player];
_queryResult = [_query,2] call OES_fnc_asyncCall;
// this should never occur
if (count _queryResult != 1) exitWith {
  [false,0,"An error occured."] remoteExec ["OEC_fnc_depositBoxRedeemed",remoteExecutedOwner,false];
};
private _amount = _queryResult select 0;
if (_amount == 0) exitWith {
  [false,0,"You have no money in your deposit box to redeem."] remoteExec ["OEC_fnc_depositBoxRedeemed",remoteExecutedOwner,false];
};

_query = format["UPDATE `players` SET `deposit_box`=0 WHERE `playerid`='%1'", getPlayerUID _player];
_queryResult = [_query,1] call OES_fnc_asyncCall;

[true,_amount,"You have successfully redeemed $%1 from your deposit box!"] remoteExec ["OEC_fnc_depositBoxRedeemed",remoteExecutedOwner,false];
