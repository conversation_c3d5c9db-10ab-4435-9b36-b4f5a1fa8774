//  File: fn_loadingScreenContent
//	Description: fuck

disableSerialization;
params [
	["_display",displayNull,[displayNull]],
	["_masterControlGroup",controlNull,[controlNull]]
];

private _heightShift = safezoneH * 0;
private _controlGroup = [_display, "RscControlsGroup", 820001, _masterControlGroup, [safezoneW * 0.2,(safezoneH * 0.2) + _heightShift,safezoneW * 0.6,(safezoneH * 0.5)], [1, 1, 1, 1], true, ""] call OEC_fnc_ctrlCreate;

//============================ Background And Title ============================
[_display, "RscText", -1, _controlGroup, [0, 0, (safezoneW * 0.6), (safezoneH * 0.5)], [0, 0, 0, 0.7], true, ""] call OEC_fnc_ctrlCreate;


_ctrl = [_display, "RscTree", 830000, _controlGroup, [0.025 * (3/4), 0.025, (safezoneW * 0.2), (safezoneH * 0.475)], [0, 0, 0, 0.45], true, ""] call OEC_fnc_ctrlCreate;
_ctrl ctrlSetFontHeight 0.03;
_ctrl ctrlSetFont "PuristaMedium";


[] spawn{
	while{life_loadingSystemActive} do {
		sleep 0.1;
		if(!isNull findDisplay 38500) then {
			((findDisplay 800000) displayCtrl 830000) ctrlenable False;
			waitUntil{isNull findDisplay 38500};
			((findDisplay 800000) displayCtrl 830000) ctrlenable true;
		};
	};
};


private _colorParent = [1,1,1,1];
private _childOne = [0.9,0.9,0.9,1];
private _childTwo = [0.8,0.8,0.8,1];
private _childThree = [0.7,0.7,0.7,1];
private _directory = "core\contentMenu\";

_ctrl tvAdd [[],"Development Changelog"];
_ctrl tvSetColor [[0], _colorParent];

private _changeLogs = [
	["Changelog 2020-11-01","change_2020_11_01"],
	["Changelog 2020-10-01","change_2020_10_01"],
	["Changelog 2020-09-01","change_2020_09_01"],
	["Changelog 2020-08-01","change_2020_08_01"],
	["Changelog 2020-07-01","change_2020_07_01"],
	["Changelog 2020-06-01","change_2020_06_01"],
	["Changelog 2020-05-01","change_2020_05_01"],
	["Changelog 2020-03-31","change_2020_03_31"],
	["Changelog 2020-02-25","change_2020_02_25"],
	["Changelog 2019-12-31","change_2019_12_31"],
	["Changelog 2019-11-24","change_2019_11_24"],
	["Changelog 10-28-19","change_10_28_19"],
	["Changelog 10-07-19","change_10_07_19"],
	["Changelog 09-28-19","change_09_28_19"],
	["Changelog 08-22-19","change_08_22_19"],
	["Changelog 07-26-19","change_07_26_19"],
	["Changelog 07-14-19","change_07_14_19"],
	["Changelog 05-01-19","change_05_01_19"],
	["Changelog 02-17-19","change_02_17_19"],
	["Changelog 01-26-19","change_01_26_19"],
	["Changelog 01-13-19","change_01_13_19"],
	["Changelog 12-24-18","change_12_24_18"],
	["Changelog 12-01-18","change_12_1_18"],
	["Changelog 11-02-18","change_11_2_18"],
	["Changelog 10-17-18","change_10_17_18"],
	["Changelog 09-14-18","change_09_14_18"],
	["Changelog 08-08-18","change_08_08_18"],
	["Changelog 07-05-18","change_07_05_18"],
	["Changelog 06-06-18","change_06_06_18"],
	["Changelog 05-16-18","change_5_16_18"],
	["Changelog 04-11-18","change_04_11_18"],
	["Changelog 03-09-18","change_03_09_18"],
	["Changelog 01-11-18","change_01_11_18"],
	["Changelog 02-8-18","change_02_08_18"],
	["Changelog 12-26-17","change_12_26_17"],
	["Changelog 12-13-17","change_12_13_17"],
	["Changelog 11-03-17","change_11_03_17"],
	["Changelog 10-06-17","change_10_06_17"],
	["Changelog 9-22-17","change_9_22_17"],
	["Changelog 8-10-17","change_8_10_17"],
	["Changelog 7-19-17","change_7_19_17"],
	["Changelog 7-7-17","change_7_7_17"],
	["Changelog 6-13-17","change_6_13_17"],
	["Changelog 6-2-17","change_6_2_17"],
	["Changelog 5-22-17","change_5_22_17"],
	["Changelog 5-3-17","change_5_7_17"],
	["Changelog 4-15-17","change_4_12_17"],
	["Changelog 4-5-17","change_3_29_17"],
	["Changelog 3-13-17","change_3_13_17"],
	["Changelog 3-5-17","change_3_5_17"],
	["Changelog 2-20-17","change_2_20_17"],
	["Changelog 2-11-17","change_2_11_17"],
	["Changelog 2-3-17","change_2_2_17"],
	["Changelog 1-8-17","change_1_8_17"]
];

	{
		_ctrl tvAdd [[0],(_x select 0)];
		_ctrl tvSetColor [[0,_forEachIndex], _childOne];
		_ctrl tvSetData [[0,_forEachIndex], format["%1%2.txt",_directory,(_x select 1)]];
	} forEach _changeLogs;

_ctrl tvAdd [[],"Server Information"];
_ctrl tvSetColor [[1], _colorParent];

	_ctrl tvAdd [[1],"TeamSpeak and Server Info"];
	_ctrl tvSetColor [[1,0], _childOne];
	_ctrl tvSetData [[1,0], format["%1info_ts_server.txt",_directory]];

_ctrl tvAdd [[],"Server Rules"];
_ctrl tvSetColor [[2], _colorParent];

	_ctrl tvAdd [[2],"General Rules"];
	_ctrl tvSetColor [[2,0], _childOne];
	_ctrl tvSetData [[2,0], format["%1general_rules.txt",_directory]];

	_ctrl tvAdd [[2],"Altis Life Rules"];
	_ctrl tvSetColor [[2,1], _childOne];
	_ctrl tvSetData [[2,1], format["%1rules_server.txt",_directory]];

		/*_ctrl tvAdd [[2,1],"Random Death Match (RDM)"];
		_ctrl tvSetColor [[2,0,0], _childTwo];
		_ctrl tvSetData [[2,0,0], format["%1rules_rdm.txt",_directory]];

		_ctrl tvAdd [[2,1],"Vehicle Death Match (VDM)"];
		_ctrl tvSetColor [[2,1,1], _childTwo];
		_ctrl tvSetData [[2,1,1], format["%1rules_vdm.txt",_directory]];
*/
_ctrl tvAdd [[],"Staff Directory"];
_ctrl tvSetColor [[3], _colorParent];

	_ctrl tvAdd [[3],"Administrators"];
	_ctrl tvSetColor [[3,0], _childOne];
	_ctrl tvSetData [[3,0], format["%1administrators.txt",_directory]];

	_ctrl tvAdd [[3],"Designers"];
	_ctrl tvSetColor [[3,1], _childOne];
	_ctrl tvSetData [[3,1], format["%1designers.txt",_directory]];

	_ctrl tvAdd [[3],"Developers"];
	_ctrl tvSetColor [[3,2], _childOne];
	_ctrl tvSetData [[3,2], format["%1developers.txt",_directory]];

	_ctrl tvAdd [[3],"Moderators"];
	_ctrl tvSetColor [[3,3], _childOne];
	_ctrl tvSetData [[3,3], format["%1moderators.txt",_directory]];

	_ctrl tvAdd [[3],"Support Team"];
	_ctrl tvSetColor [[3,4], _childOne];
	_ctrl tvSetData [[3,4], format["%1supportteam.txt",_directory]];

_ctrl tvAdd [[],"New Player Information"];
_ctrl tvSetColor [[4], _colorParent];
_ctrl tvSetData [[4], format["%1newplayer.txt",_directory]];

	_ctrl tvAdd [[4],"Report a Player"];
	_ctrl tvSetColor [[4,0], _childOne];
	_ctrl tvSetData [[4,0], format["%1reporting.txt",_directory]];

	_ctrl tvAdd [[4],"Run Information"];
	_ctrl tvSetColor [[4,1], _childOne];
	_ctrl tvSetData [[4,1], format["%1runInformation.txt",_directory]];

	_ctrl tvAdd [[4],"Blackwater Info"];
	_ctrl tvSetColor [[4,2], _childOne];
	_ctrl tvSetData [[4,2], format["%1bwInfo.txt",_directory]];

	_ctrl tvAdd [[4],"Jail Info"];
	_ctrl tvSetColor [[4,3], _childOne];
	_ctrl tvSetData [[4,3], format["%1jailInfo.txt",_directory]];

	_ctrl tvAdd [[4],"Federal Reserve Info"];
	_ctrl tvSetColor [[4,4], _childOne];
	_ctrl tvSetData [[4,4], format["%1fedInfo.txt",_directory]];

	_ctrl tvAdd [[4],"Epi-Pens and Dopamine"];
	_ctrl tvSetColor [[4,5], _childOne];
	_ctrl tvSetData [[4,5], format["%1dopamine.txt",_directory]];

	_ctrl tvAdd [[4],"Vehicle Modifications"];
	_ctrl tvSetColor [[4,6], _childOne];
	_ctrl tvSetData [[4,6], format["%1vehInfo.txt",_directory]];

	_ctrl tvAdd [[4],"Vigilante Rules"];
	_ctrl tvSetColor [[4,7], _childOne];
	_ctrl tvSetData [[4,7], format["%1vigiInfo.txt",_directory]];


_ctrl tvAdd [[],"Police Department Information"];
_ctrl tvSetColor [[5], _colorParent];
_ctrl tvSetData [[5], format["%1apd.txt",_directory]];

_ctrl tvAdd [[],"Rescue and Recovery Information"];
_ctrl tvSetColor [[6], _colorParent];
_ctrl tvSetData [[6], format["%1RNR.txt",_directory]];

//tvExpandAll _ctrl;

_controlGroupHtml = [_display, "RscControlsGroup", 830001, _controlGroup, [0.05 * (3/4) + (safezoneW * 0.2), 0.025, (safezoneW * 0.375), (safezoneH * 0.475)], [1, 1, 1, 1], true, ""] call OEC_fnc_ctrlCreate;
_sqf = [_display, "RscStructuredText", 830002, _controlGroupHtml, [0, 0, (safezoneW * 0.370), 4], [0, 0, 0, 0.45], true, ""] call OEC_fnc_ctrlCreate;
_sqf ctrlSetStructuredText parseText([format["%1%2.txt", _directory, _changeLogs select 0 select 1]] call OEC_fnc_processSQF);

_ctrl ctrlAddEventHandler ["TreeSelChanged", {_treePath = tvData [830000,_this select 1]; if(_treePath isEqualTo "") then {_treePath = "core\contentMenu\thanks.txt";}; ((findDisplay 800000) displayCtrl 830002) ctrlSetStructuredText parseText([_treePath] call OEC_fnc_processSQF);}];
