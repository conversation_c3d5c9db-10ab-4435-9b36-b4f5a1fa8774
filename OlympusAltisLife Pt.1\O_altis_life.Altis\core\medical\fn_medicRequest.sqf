//  File: fn_medicRequest.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine

//	Description: Notifies the medics that someone has requested emergency and prompts them
//	if they want to take the request or not.
private["_caller","_callerName"];
_caller = param [0,Ob<PERSON><PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]];
_callerName = param [1,"Unknown Player",[""]];
if(isNull _caller) exitWith {}; //Bad data
if(oev_newsTeam) exitWith {};

["MedicalRequestEmerg",[format[localize "STR_Medic_Request",_callerName]]] call BIS_fnc_showNotification;