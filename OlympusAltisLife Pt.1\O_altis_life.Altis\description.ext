author="Olympus Entertainment and Tonic";
onLoadName = "Olympus Altis Life RPG";
onLoadMission = "An RPG game mode developed by Olympus Entertainment and Tonic.";
overviewText = "An RPG game mode developed by Olympus Entertainment and Tonic.";
overviewTextLocked = "An RPG game mode developed by Olympus Entertainment and Tonic.";
overviewPicture = "sign_billboard_1.paa";
loadScreen = "sign_billboard_1.paa";
OnLoadIntroTime = 0;
OnLoadMissionTime = 0;
allowFunctionsLog = 1;
allowFunctionsRecompile = 0;
briefing = 0;

joinUnassigned = 1;
respawn = BASE;
disabledAI = 1;
disableChannels[]={0,1,2};
enableDebugConsole = 1;
respawnDialog = 0;
scriptedPlayer = 1;
forceRotorLibSimulation = 0;

class Header {
	gameType = RPG;
	minPlayers = 1;
	maxPlayers = 100;
};

respawndelay = 1;
wreckLimit = 3;
wreckRemovalMinTime = 60;
wreckRemovalMaxTime = 320;
corpseLimit = 75;
corpseRemovalMinTime = 600;
corpseRemovalMaxTime = 1200;
unsafeCVL = 1;

showHUD[] = {1,1,0,1,0,0,1,1,1};

#include "dialog\MasterHandler.hpp"

class RscTitles {
	#include "dialog\ui.hpp"
	#include "dialog\progress.hpp"
	#include "dialog\hud_nameTags.hpp"
};

class CfgFunctions {
	#include "Functions.h"
};

class CfgRemoteExec {
	#include "CfgRE.hpp"
};

class CfgSounds {
	sounds[] = {};

	class chokeOne {
		name = "chokeOne";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person0\P0_choke_02.wss", 2, 1};
		titles[] = {};
	};

	class chokeTwo {
		name = "chokeTwo";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person2\P2_choke_05.wss", 2, 1};
		titles[] = {};
	};

	class chokeThree {
		name = "chokeThree";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\P06\Soundbreathinjured_Max_2.wss", 2, 1};
		titles[] = {};
	};

	class chokeFour {
		name = "chokeFour";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person3\P3_choke_02.wss", 2, 1};
		titles[] = {};
	};

	class chokeFive {
		name = "chokeFive";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person2\P2_choke_04.wss", 2, 1};
		titles[] = {};
	};

	class APD_Wail {
		name = "apd_wail";
		sound[] = {"\sounds\APD_Wail.ogg", 5, 1};
		titles[] = {};
	};

	class APD_Yelp {
		name = "apd_yelp";
		sound[] = {"\sounds\APD_Yelp.ogg", 5, 1};
		titles[] = {};
	};

	class APD_Horn {
		name = "apd_horn";
		sound[] = {"\sounds\APD_horn.ogg", 5, 1};
		titles[] = {};
	};

	class Med_Wail {
		name = "med_wail";
		sound[] = {"\sounds\Med_Wail.ogg", 1.8, 1};
		titles[] = {};
	};

	class Med_Yelp {
		name = "med_yelp";
		sound[] = {"\sounds\Med_Yelp.ogg", 1.8, 1};
		titles[] = {};
	};

	class Med_Horn {
		name = "med_horn";
		sound[] = {"\sounds\med_horn.ogg", 1.6, 1};
		titles[] = {};
	};

	class flashbang {
		name = "flashbang";
		sound[] = {"\sounds\flashbang.ogg", 1.1, 1};
		titles[] = {};
	};

	class car_alarm {
		name = "car_alarm";
		sound[] = {"\sounds\caralarm.ogg", 1.5, 1};
		titles[] = {};
	};

	class Cuff {
		name = "cuff";
		sound[] = {"\sounds\cuff.ogg", 0.9, 0.95};
		titles[] = {};
	};

	 class ticket {
		name="ticket";
		sound[] = {"\sounds\ticket.ogg", 0.5, 1};
		titles[] = {0, " "};
	};

	class unlock {
		name = "unlock";
		sound[] = {"\sounds\unlock.ogg", 0.8, 1};
		titles[] = {};
	};

	class car_lock {
		name = "car_lock";
		sound[] = {"\sounds\vehicle_lock.ogg", 0.8, 1};
		titles[] = {};
	};

	class bank_alarm {
		name = "bank_alarm";
		sound[] = {"\sounds\bankalarm.ogg", 1.2, 1};
		titles[] = {};
	};

	class kick_balls {
		name = "kick_balls";
		sound[] = {"\sounds\kick_balls.ogg", 1.5, 1};
		titles[] = {};
	};

	class missileLocking {
		name = "missileLock";
		sound[] = {"\sounds\locking_3.ogg", 1.0, 1};
		titles[] = {};
	};

	class missileLocked {
		name = "missileLock";
		sound[] = {"\sounds\locked_3.ogg", 1.0, 1};
		titles[] = {};
	};

	class noot_horn {
		name = "noots";
		sound[] = {"\sounds\Standard_Noot_2.ogg", 2, 1};
		titles[] = {};
	};
	class doc_horn {
		name = "dochorn";
		sound[] = {"\sounds\docHorn.ogg", 2, 1};
		titles[] = {};
	};
	class dillifu {
		name = "dillifu";
		sound[] = {"\sounds\dillifu.ogg", 2, 1};
		titles[] = {};
    };
	class peter_horn {
		name = "peterhorn";
		sound[] = {"\sounds\peterHorn.ogg", 2, 1};
		titles[] = {};
	};
	class zonda {
		name = "zonda";
		sound[] = {"\sounds\zonda.ogg", 2, 1};
		titles[] = {};
	};
	class rex_horn {
		name = "rexhorn";
		sound[] = {"\sounds\rexHorn.ogg", 2, 1};
		titles[] = {};
	};
	class ryan_horn {
		name = "ryanhorn";
		sound[] = {"\sounds\ryanHorn.ogg", 2, 1};
		titles[] = {};
	};
	class rapid_horn {
		name = "rapidhorn";
		sound[] = {"\sounds\rapidHorn.ogg", 2, 1};
		titles[] = {};
	};
	class destructbark {
		name = "destructbark";
		sound[] = {"\sounds\destructbark.ogg", 2, 1};
		titles[] = {};
	};
	class zahzi_horn {
		name = "zahzihorn";
		sound[] = {"\sounds\zahzihorn.ogg", 2, 1};
		titles[] = {};
	};
	class horizonhorn {
		name = "horizonhorn";
		sound[] = {"\sounds\horizonhorn.ogg", 2, 1};
		titles[] = {};
	};
	class fraalihorn {
		name = "fraalihorn";
		sound[] = {"\sounds\fraaliHorn.ogg", 2, 1};
		titles[] = {};
	};
	class beep {
		name = "beep";
		sound[] = {"@A3\Sounds_f\sfx\beep_target.wss", 1, 1};
		titles[] = {};
	};
	class radiotower {
		name = "radiotower";
		sound[] = {"\sounds\radiotowermed.ogg", 1, 1};
		titles[] = {};
	};
	class trimohorn {
		name = "trimohorn";
		sound[] = {"\sounds\trimohorn.ogg", 1, 1};
		titles[] = {};
	};
	class rayHorn {
		name = "rayHorn";
		sound[] = {"\sounds\rayHorn.ogg", 1, 1};
		titles[] = {};
	};
	class openChest {
		name = "openChest";
		sound[] = {"\sounds\openChest.ogg", 1, 1};
		titles[] = {};
	};
	class gallery_siren {
		name = "galleryAlarm";
		sound[] = {"\sounds\gallery_siren.ogg", 1, 1};
		titles[] = {};
	};
	class cardFlick1 {
		name = "cardFlick1";
		sound[] = {"\sounds\cardFlick1.ogg", 1.5, 1};
		titles[] = {};
	};
	class cardFlick2 {
		name = "cardFlick2";
		sound[] = {"\sounds\cardFlick2.ogg", 1.5, 1};
		titles[] = {};
	};
	class cardSlide1 {
		name = "cardSlide1";
		sound[] = {"\sounds\cardSlide1.ogg", 1.5, 1};
		titles[] = {};
	};
	class cardSlide2 {
		name = "cardSlide2";
		sound[] = {"\sounds\cardSlide2.ogg", 1.5, 1};
		titles[] = {};
	};
	class cardSlide3 {
		name = "cardSlide3";
		sound[] = {"\sounds\cardSlide3.ogg", 1.5, 1};
		titles[] = {};
	};
	class slotsRoll {
		name = "slotsRoll";
		sound[] = {"\sounds\slots_roll.ogg", 1.5, 1};
		titles[] = {};
	};
	class gamblingWin {
		name = "gamblingWin";
		sound[] = {"\sounds\gambling_win.ogg", 1.5, 1};
		titles[] = {};
	};
	class gamblingLose {
		name = "gamblingLose";
		sound[] = {"\sounds\gambling_lose.ogg", 1.5, 1};
		titles[] = {};
	};
};

class CfgDebriefing {
	class NotWhitelisted {
		title = "Mission Failed";
		subtitle = "You are not white-listed to use this slot";
		description = "You are not allowed to use this slot because you do not have the appropriate permissions, try another slot.";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {0,0.3,0.6,1};
	};

	class Blacklisted {
		title = "Mission Failed";
		subtitle = "You are blacklisted from cops";
		description = "You are not allowed to be a cop due to previous actions and the admins have removed you from being a cop.";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {0,0.3,0.6,1};
	};

	class HackedMoney {
		title = "Mission Failed";
		subtitle = "Your money is suspicious.";
		description = "You have been detected with hacked money. Contact an admin if this is a mistake. Website: olympus-entertainment.com/support";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {0,0.3,0.6,1};
	};

	class SpyGlass {
		title = "The SpyGlass sees you";
		subTitle = "You were detected by SpyGlass AntiCheat";
		description = "If you were not cheating, then you have nothing to worry about. Innocent players are sometimes caught by the anticheat. If you believe this is a mistake report this to an admin.";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {0,0.3,0.6,1};
	};

	class SetupError {
		title = "An error has occurred during setup";
		subTitle = "This happens every once in a while, don't worry.";
		description = "The server was unable to setup your character, this happens every once in a while and it's nothing to be alarmed about. Just join back and everything should be fine.";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {0,0.3,0.6,1};
	};

	class BadName {
		title = "Mission Failed";
		subTitle = "Illegal characters in player name";
		description = "Please remove the special characters in your player name. Most ASCII characters are supported.";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {0,0.3,0.6,1};
	};

	class ReturnToLobby {
		title = "Returning to Lobby";
		subTitle = ". . .";
		description = "";
		pictureBackground = "";
		picture = "";
		pictureColor[] = {1,1,1,1};
	};
};

class CfgNotifications {
	class MedicalRequestEmerg {
		title = "EMS Requested";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskIcon_ca.paa";
		description = "%1";
		duration = 5;
		priority = 7;
	};

	class MedDeliveryAssigned {
		title = "Supply Delivery Accepted";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskIcon_ca.paa";
		description = "%1";
		duration = 10;
		priority = 7;
	};

	class MedDeliveryFailed {
		title = "Supply Delivery Failed";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskiconfailed_ca.paa";
		description = "%1";
		duration = 7;
		priority = 7;
	};

	class MedDeliverySucceeded {
		title = "Medical Supplies Delivered";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskIcondone_ca.paa";
		description = "%1";
		duration = 6;
		priority = 6;
	};

	class DeliveryAssigned {
		title = "Delivery Job Accepted";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskIcon_ca.paa";
		description = "%1";
		duration = 10;
		priority = 7;
	};

	class DeliveryFailed {
		title = "Delivery Job Failed";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskiconfailed_ca.paa";
		description = "%1";
		duration = 7;
		priority = 7;
	};

	class DeliverySucceeded {
		title = "Delivery Job Completed";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskIcondone_ca.paa";
		description = "%1";
		duration = 6;
		priority = 6;
	};

	class TextMessage {
		title = "Received A Text Message";
		iconPicture = "images\icons\messagenew.paa";
		description = "%1";
		duration = 10;
		priority = 6;
	};

	class PoliceDispatch {
		title = "911 Dispatch Center";
		iconPicture = "images\icons\messagepolice.paa";
		description = "%1";
		duration = 10;
		priority = 6;
	};

	class AdminDispatch {
		title = "Admin Dispatch Center";
		iconPicture = "images\icons\messageadmin.paa";
		description = "%1";
		duration = 10;
		priority = 6;
	};

	class AdminMessage {
		title = "Admin Message";
		iconPicture = "images\icons\messageadmin.paa";
		description = "%1";
		duration = 10;
		priority = 5;
	};

	class WarKillConfirm {
		title = "Kill Confirmed";
		iconPicture = "\A3\ui_f\data\map\mapcontrol\taskIconCanceled_ca.paa";
		description = "%1";
		duration = 7;
		priority = 5;
	};

	class ConquestAlert {
		title = "Conquest Alert";
		iconPicture = "images\icons\spawn_shed.paa";
		description = "%1";
		duration = 7;
		priority = 5;
	};

	class AirdropAlert {
		title = "Airdrop Alert";
		iconPicture = "images\icons\hexIcons\parachute.paa";
		description = "%1";
		duration = 7;
		priority = 5;
	};
};

// Keep this to prevent hackers doing var dumps - Can only be used on server
// Use {utils 1} in debug to get format https://i.imgur.com/W46cCiy.pngs for each command
// class COMMAND {targets[] = {enableServer,enableClient,enableHC}; args[] = {{"ARRAY"},{"STRING"}};
class CfgDisabledCommands {
	class DIAG_ACTIVESQFSCRIPTS {targets[] = {0,0,0};};
	class DIAG_ACTIVESQSSCRIPTS {targets[] = {0,0,0};};
	class DIAG_ACTIVEMISSIONFSMS {targets[] = {0,0,0};};
	class DIAG_ALLMISSIONEVENTHANDLERS {targets[] = {0,0,0};};
	class EXPORTJIPMESSAGES {targets[] = {0,0,0};};
	class DIAG_DUMPCALLTRACETOLOG {targets[] = {0,0,0};};
	class ADDFORCE {targets[] = {0,0,0};};
	class TEAMSWITCH {targets[] = {0,0,0};};
	class ENABLEAI {targets[] = {0,0,0};};
	class ENABLEAIFEATURE {targets[] = {0,0,0};};
	class FORCEWEATHERCHANGE {targets[] = {1,0,1};};
};

#include "Config_Gather.hpp"
#include "Config_physWeights.hpp"
#include "Config_Titles.hpp"
#include "Config_Skins.hpp"
