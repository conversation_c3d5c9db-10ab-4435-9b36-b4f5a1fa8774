/*%FSM<COMPILE "C:\Program Files (x86)\Steam\steamapps\common\Arma 3 Tools\FSMEditor\scriptedFSM.cfg, Server-Side Cleanup">*/
/*%FSM<HEAD>*/
/*
item0[] = {"init",0,250,-65.004578,-391.651611,24.995417,-341.651672,0.000000,"init"};
item1[] = {"true",8,218,-62.976639,-315.185364,27.023363,-265.185364,0.000000,"true"};
item2[] = {"Share__Work_load",2,250,-64.183350,-224.681931,25.816656,-174.681931,0.000000,"Share " \n "Work-load"};
item3[] = {"true",8,218,-54.709698,75.189262,35.290302,125.189262,0.000000,"true"};
item4[] = {"Time_Check",4,218,-219.425827,-133.310532,-129.425964,-83.310455,0.000000,"Time Check"};
item5[] = {"Delete_Dead_Cars",2,4346,-220.186951,-29.248400,-130.187195,20.751413,0.000000,"Delete" \n "Dead" \n "Cars"};
item6[] = {"",7,210,-312.538239,95.295059,-304.538239,103.295059,0.000000,""};
item7[] = {"",7,210,-311.750000,-203.033707,-303.750000,-195.033707,0.000000,""};
link0[] = {0,1};
link1[] = {1,2};
link2[] = {2,4};
link3[] = {3,6};
link4[] = {4,5};
link5[] = {5,3};
link6[] = {6,7};
link7[] = {7,2};
globals[] = {0.000000,0,0,0,0,640,480,1,46,6316128,1,-369.873444,351.635406,166.661316,-469.964172,697,588,1};
window[] = {0,-1,-1,-1,-1,942,182,1825,182,1,715};
*//*%FSM</HEAD>*/
class FSM
{
        fsmName = "Server-Side Cleanup";
        class States
        {
                /*%FSM<STATE "init">*/
                class init
                {
                        name = "init";
                        itemno = 0;
                        init = /*%FSM<STATEINIT""">*/"private[""_impound"",""_cars"",""_objs"",""_totCars"",""_thread"",""_dbInfo"",""_isInsured"",""_color"",""_uid"",""_plate"",""_query""];" \n
                         "_impound = time;" \n
                         "_cars = time;" \n
                         "_objs = time;"/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "true">*/
                                class true
                                {
                                        itemno = 1;
                                        priority = 0.000000;
                                        to="Share__Work_load";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"true"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "Share__Work_load">*/
                class Share__Work_load
                {
                        name = "Share__Work_load";
                        itemno = 2;
                        init = /*%FSM<STATEINIT""">*/""/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "Time_Check">*/
                                class Time_Check
                                {
                                        itemno = 4;
                                        priority = 0.000000;
                                        to="Delete_Dead_Cars";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"((time - _cars) > (3 * 60))"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "Delete_Dead_Cars">*/
                class Delete_Dead_Cars
                {
                        name = "Delete_Dead_Cars";
                        itemno = 5;
                        init = /*%FSM<STATEINIT""">*/"{" \n
                         "	if(!alive _x && (_x isKindOf ""LandVehicle"" || _x isKindOf ""Air"" || _x isKindOf ""Ship"")) then" \n
                         "	{" \n
                         "		_dbInfo = _x getVariable[""dbInfo"",[]];" \n
                         "		_isInsured = _x getVariable[""insured"",1];" \n
                         "		_color = _x getVariable[""oev_veh_color"",[0,0]];" \n
                         "		_color = _color select 0;" \n
                         "		if(count _dbInfo > 0) then" \n
                         "		{" \n
                         "			_uid = _dbInfo select 0;" \n
                         "			_plate = _dbInfo select 1;" \n
                         "" \n
                         "			switch (_isInsured) do {" \n
                         "				case 0: {" \n
                         "					_query = format[""UPDATE %3 SET alive='0' WHERE pid='%1' AND plate='%2'"",_uid,_plate,dbColumVehicle];" \n
                         "					_query spawn {" \n
                         "						_thread = [_this,1] spawn OES_fnc_asyncCall;" \n
                         "					};" \n
                         "				};" \n
                         "				case 1: {" \n
                         "					_query = format[""UPDATE %4 SET active='0', insured='0', modifications='""""[0,0,0,0,0,0,0,0]""""', inventory='""""[]""""', color='""""[%3,0]""""' WHERE pid='%1' AND plate='%2'"",_uid,_plate,_color,dbColumVehicle];" \n
                         "					_query spawn {" \n
                         "						_thread = [_this,1] spawn OES_fnc_asyncCall;" \n
                         "					};" \n
                         "				};" \n
                         "				case 2: {" \n
                         "					_query = format[""UPDATE %3 SET active='0', insured='0', inventory='""""[]""""' WHERE pid='%1' AND plate='%2'"",_uid,_plate,dbColumVehicle];" \n
                         "					_query spawn {" \n
                         "						_thread = [_this,1] spawn OES_fnc_asyncCall;" \n
                         "					};" \n
                         "				};" \n
                         "			};" \n
                         "		};" \n
                         "		if(!isNil ""_x"" && {!isNull _x}) then {" \n
                         "			if(!(_x getVariable [""isBlackwater"", false])) then {" \n
                         "				deleteVehicle _x;" \n
                         "			};" \n
                         "		};" \n
                         "	};" \n
                         "}foreach vehicles;" \n
                         "" \n
                         "_cars = time;" \n
                         "" \n
                         "//Group cleanup." \n
                         "{" \n
                         "	if(count units _x == 0 && local _x) then {" \n
                         "		deleteGroup _x;" \n
                         "	};" \n
                         "} foreach allGroups;"/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "true">*/
                                class true
                                {
                                        itemno = 3;
                                        priority = 0.000000;
                                        to="Share__Work_load";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"true"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
        };
        initState="init";
        finalStates[] =
        {
        };
};
/*%FSM</COMPILE>*/