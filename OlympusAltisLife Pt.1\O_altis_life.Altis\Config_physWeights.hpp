#define true 1
#define false 0

class CfgWeights {
	class U_B_CombatUniform_mcam {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CombatUniform_mcam_tshirt {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CombatUniform_mcam_vest {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CombatUniform_mcam_worn {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_2 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_3 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_survival_uniform {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_Soldier_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_Soldier_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_Soldier_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_Soldier_urb_1_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_Soldier_urb_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CTRG_Soldier_urb_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_FullGhillie_ard {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_FullGhillie_lsh {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_FullGhillie_sard {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_GEN_Soldier_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_GhillieSuit {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_Protagonist_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_T_FullGhillie_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_Soldier_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_Soldier_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_Soldier_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Soldier_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_T_Sniper_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_T_Soldier_AR_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_T_Soldier_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_T_Soldier_SL_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_Wetsuit {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_BG_Guerilla1_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_BG_Guerilla2_2 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_BG_Guerilla2_3 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_BG_leader {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_ConstructionCoverall_Black_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_ConstructionCoverall_Blue_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_ConstructionCoverall_Red_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_black {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_blue {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_green {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_orange {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_red {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_white {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_1_yellow {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_2 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_3 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Driver_4 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_HunterBody_grn {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_IDAP_Man_cargo_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_IDAP_Man_casual_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_IDAP_Man_Jeans_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_IDAP_Man_Tee_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_IDAP_Man_TeeShorts_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Journalist {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Man_casual_1_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Man_casual_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Man_casual_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Man_casual_4_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Man_casual_5_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Man_casual_6_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_man_sport_1_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_man_sport_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_man_sport_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Mechanic_01_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poloshirt_blue {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poloshirt_redwhite {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poloshirt_salmon {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poloshirt_burgundy {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poloshirt_stripped {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poloshirt_tricolour {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Commoner1_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_IG_Guerilla3_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Scientist {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poor_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Poor_2 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_C_Protagonist_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_Competitor {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Bandit_1_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Bandit_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Bandit_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Bandit_4_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Bandit_5_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Para_1_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Para_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Para_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Para_4_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_C_Soldier_Para_5_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_CombatUniform {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_CombatUniform_shortsleeve {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_CombatUniform_tshirt {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_FullGhillie_ard {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_FullGhillie_lsh {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_FullGhillie_sard {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_FullGhillie_lsh {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_G_resistanceLeader_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_G_Story_Protagonist_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_GhillieSuit {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_OfficerUniform {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_pilotCoveralls {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_Protagonist_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_Wetsuit {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_IG_Guerilla2_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_IG_Guerilla2_2 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_IG_Guerilla2_3 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_Marshal {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_NikosAgedBody {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_NikosBody {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_CombatUniform_ocamo {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_CombatUniform_oucamo {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_GhillieSuit {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_OfficerUniform_ocamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_Protagonist_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_SpecopsUniform_ocamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_T_FullGhillie_tna_F {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_T_Officer_F {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_T_Sniper_F {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_T_Soldier_F {
		weight = 1;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_Wetsuit {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_OG_Guerilla3_1 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_OG_Guerilla3_2 {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_OrestesBody {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_Rangemaster {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_GEN_Commander_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_AssaultPack_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_AssaultPack_cbr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_AssaultPack_khk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_AssaultPack_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_AssaultPack_sgg {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Bergen_dgtl_F {
		weight = 3;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Bergen_hex_F {
		weight = 3;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Bergen_mcamo_F {
		weight = 3;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Bergen_tna_F {
		weight = 3;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_cbr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_ghex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_khk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_mcamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_ocamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Carryall_oucamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_black_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_eaf_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_digi_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_ghex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_oucamo_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_mtp_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_RadioBag_01_tropic_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_cbr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_ghex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_khk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_ocamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_FieldPack_oucamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Kitbag_cbr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Kitbag_mcamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Kitbag_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Kitbag_sgg {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Kitbag_tan {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_LegStrapBag_black_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_LegStrapBag_coyote_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_LegStrapBag_olive_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Messenger_Black_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Messenger_Coyote_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Messenger_Gray_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Messenger_Olive_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_Parachute {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_TacticalPack_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_TacticalPack_mcamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_TacticalPack_ocamo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_TacticalPack_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_TacticalPack_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperHarness_blk_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperHarness_ghex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperHarness_hex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperHarness_khk_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperHarness_oli_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperLightHarness_blk_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperLightHarness_ghex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperLightHarness_hex_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperLightHarness_khk_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class B_ViperLightHarness_oli_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_BandollierB_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_BandollierB_cbr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_BandollierB_khk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_BandollierB_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_BandollierB_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Chestrig_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Chestrig_khk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Chestrig_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Chestrig_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_blue_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_brown_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_green_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_red_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_violet_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_white_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_DeckCrew_yellow_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_EOD_blue_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_EOD_coyote_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_HarnessO_brn {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_HarnessOGL_brn {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_I_G_resistanceLeader_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_LegStrapBag_black_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierH_CTRG {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_LegStrapBag_coyote_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_LegStrapBag_olive_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrier1_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrier1_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrier1_rgr_noflag_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrier1_tna_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrier2_blk {
		weight = 2;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierGL_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierGL_mtp {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierGL_rgr {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierGL_tna_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierIA1_dgtl {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierIA2_dgtl {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierIAGL_dgtl {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierIAGL_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierL_CTRG {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierSpec_blk {
		weight = 2;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class V_Pocketed_black_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Pocketed_coyote_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Pocketed_olive_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Press_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Rangemaster_belt {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_RebreatherB {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_RebreatherIA {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Safety_blue_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Safety_orange_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_Safety_yellow_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVest_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVest_blk_POLICE {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVest_brn {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVest_camo {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVest_khk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVest_oli {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_TacVestIR_blk {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Aviator {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_B_Diving {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Balaclava_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Balaclava_combat {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Balaclava_lowprofile {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Balaclava_oli {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_aviator {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_beast {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_khk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_oli {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_shades {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_sport {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandanna_tan {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandana_Sport {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandana_Beast {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandana_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Bandana_Shades {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_EarProtectors_black_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_EarProtectors_white_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Watchcap_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_usblack {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_grn_BI {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_oli_hs {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetCrew_I {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_mcamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_surfer_grn {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_camo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_PASGT_basic_olive_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Helmet_plain_wdl {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetSpec_B {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetSpec_B_wdl {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_AirPurifyingRespirator_02_olive_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};

	class H_Beret_gen_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Combat {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Diving {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Goggles_VR {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Lady_Blue {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Lady_Dark {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Lady_Mirror {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Lowprofile {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Respirator_white_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Respirator_yellow_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Shades_Blue {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Spectacles_Tinted {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Sport_Blackred {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Sport_BlackWhite {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Sport_Blackyellow {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Sport_Checkered {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Sport_Greenblack {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Sport_Red {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Squares {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Tactical_Black {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Tactical_Clear {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_WirelessEarpiece_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_cbr {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_gry {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_khk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_sgg {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Bandanna_surfer {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Beret_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Beret_blk_POLICE {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Beret_Colonel {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_dgtl {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_dirty {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_grn {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_khk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_khk_hs {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_mcamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_oli {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_tan {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_Black_IDAP_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_blk_CMMG {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_blk_ION {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_blk_Raven {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_blu {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_brn_SPECOPS {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_grn {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_headphones {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_khaki_specops_UK {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_marshal {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_oli {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_Orange_IDAP_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_police {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_press {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_red {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_surfer {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_tan {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_tan_specops_US {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Cap_White_IDAP_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Construction_basic_black_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Construction_basic_orange_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Construction_basic_red_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Construction_basic_white_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Construction_basic_yellow_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_CrewHelmetHeli_B {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_EarProtectors_orange_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_EarProtectors_red_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_EarProtectors_yellow_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_blue {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_brown {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_camo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_checker {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_grey {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_Safari_olive_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_Safari_sand_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HeadSet_black_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HeadSet_orange_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HeadSet_red_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HeadSet_white_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HeadSet_yellow_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Helmet_Skate {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_black {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_camo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_desert {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_Enh_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_grass {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_light {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_light_desert {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_light_sand {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_light_snakeskin {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_Light_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_paint {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_plain_mcamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_sand {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_snakeskin {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_TI_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetB_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetCrew_O_ghex_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetIA {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetO_ghex_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetSpecB_blk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetO_ViperSP_hex_F{
		weight = 4;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_blue {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_dgtl {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_ghex_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_gry {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_mcamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_ocamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_oucamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_rucamo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_MilCap_tna_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_PilotHelmetFighter_B {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_PilotHelmetFighter_I {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_PilotHelmetHeli_B {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_PilotHelmetHeli_O {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_black_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_blue_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_green_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_red_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_white_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_1_yellow_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_2_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_3_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_RacingHelmet_4_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Shemag_khk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Shemag_olive {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Shemag_olive_hs {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Shemag_tan {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_ShemagOpen_khk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_ShemagOpen_tan {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_StrawHat {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_StrawHat_dark {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Watchcap_camo {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Watchcap_khk {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Watchcap_sgg {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Aco {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Holosight_smg_blk_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Holosight_blk_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_ACO_grn {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_ACO_grn_smg {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Aco_smg {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Arco {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Arco_blk_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Arco_AK_blk_F : optic_Arco_blk_F{};
	class optic_Arco_arid_F : optic_Arco_blk_F{};
	class optic_Arco_lush_F : optic_Arco_blk_F{};
	class optic_Arco_AK_arid_F : optic_Arco_blk_F{};
	class optic_Arco_AK_lush_F : optic_Arco_blk_F{};
	class optic_DMS {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_ERCO_blk_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_ERCO_khk_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Hamr {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Hamr_khk_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Holosight {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_Holosight_smg {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_MRCO {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_MRD {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_MRD_black : optic_MRD{};
	class optic_ico_01_black_f {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class optic_ico_01_f : optic_ico_01_black_f{};
	class optic_ico_01_camo_f : optic_ico_01_black_f{};
	class optic_ico_01_sand_f : optic_ico_01_black_f{};
	class hgun_ACPC2_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class hgun_P07_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class hgun_Pistol_Signal_F {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class hgun_PDW2000_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class hgun_Pistol_01_F {
	    weight = 2;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class hgun_Pistol_heavy_01_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class hgun_Pistol_heavy_02_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class hgun_Rook40_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_AKM_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_AKS_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_ARX_blk_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_ARX_ghex_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_ARX_hex_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_CTAR_blk_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_CTARS_blk_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_Katiba_C_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_Katiba_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_Mk20C_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_Mk20C_plain_F {
			weight = 3;
			illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MX_Black_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MX_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MX_khk_F : arifle_MX_F{};
	class arifle_MXC_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MXC_Black_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MXM_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MX_SW_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MX_SW_khk_F : arifle_MX_F{};
	class arifle_MX_GL_Black_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MX_SW_Black_F {
	    weight = 4;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MXM_Black_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MXM_khk_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SDAR_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_01_blk_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_01_GL_blk_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_01_khk_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_02_khk_F {
		weight = 4;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_01_snd_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_01_GL_snd_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_02_snd_F {
	    weight = 4;
	    illegal = false;
			compVal = 0;
			varOne = 0;
	};
	class arifle_SPAR_02_blk_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_03_blk_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_SPAR_03_khk_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_TRG20_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_TRG21_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_01_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_07_hex_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_02_camo_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_02_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_03_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_03_khaki_F : srifle_DMR_03_F{};
	class srifle_DMR_03_tan_F : srifle_DMR_03_F{};
	class srifle_DMR_03_woodland_F : srifle_DMR_03_F{};
	class srifle_DMR_03_multicam_F : srifle_DMR_03_F{};
	class srifle_DMR_04_Tan_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_06_camo_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_06_olive_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_07_blk_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_07_ghex_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class srifle_EBR_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class srifle_EBR_ACO_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class 1Rnd_SmokeOrange_Grenade_shell {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 1Rnd_SmokeBlue_Grenade_shell: 1Rnd_SmokeOrange_Grenade_shell {};
	class 3Rnd_SmokeOrange_Grenade_shell {
		weight = 2;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 3Rnd_SmokeBlue_Grenade_shell: 3Rnd_SmokeOrange_Grenade_shell {};
	class acc_flashlight {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class acc_flashlight_pistol {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class acc_pointer_IR {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class APERSTripMine_Wire_Mag {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class Binocular {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class bipod_01_F_blk {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class bipod_01_F_mtp {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class bipod_01_F_snd {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class bipod_01_F_khk {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class bipod_02_F_blk {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class bipod_03_F_blk {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class Chemlight_blue {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class Chemlight_green {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class Chemlight_red {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class Chemlight_yellow {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class ClaymoreDirectionalMine_Remote_Mag {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class DemoCharge_Remote_Mag {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class FirstAidKit {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class G_Shades_Black {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_CrewHelmetHeli_I {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_CrewHelmetHeli_O {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetLeaderO_ghex_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_PilotHelmetFighter_O {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class HandGrenade {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class HandGrenade_Stone {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class I_UAV_01_backpack_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class I_UavTerminal {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class ItemCompass {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class ItemGPS {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class ItemMap {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class ItemWatch {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class launch_RPG7_F {
	    weight = 5;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class launch_Titan_F {
	    weight = 5;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class launch_B_Titan_olive_F : launch_Titan_F{};
	class launch_I_Titan_F : launch_Titan_F{};
	class LMG_03_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class LMG_03_Vehicle_F {
		weight = 4;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class LMG_Mk200_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class Medikit {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class MineDetector {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class MiniGrenade {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_338_black {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_B {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_acp : muzzle_snds_B{};
	class muzzle_snds_H {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_L {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_M {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_58_blk_F {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class muzzle_snds_65_TI_blk_F {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class NVGoggles {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class NVGoggles_INDEP {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class NVGoggles_OPFOR {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class O_NVGoggles_grn_F : NVGoggles_OPFOR{};
	class O_NVGoggles_hex_F : NVGoggles_OPFOR{};
	class O_NVGoggles_ghex_F : NVGoggles_OPFOR{};
	class O_NVGoggles_urb_F : NVGoggles_OPFOR{};
	class NVGogglesB_gry_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class Rangefinder {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SLAMDirectionalMine_Wire_Mag {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class IEDUrbanSmall_Remote_Mag {
		weight = 2;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class IEDUrbanBig_Remote_Mag {
		weight = 2;
		illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class SMG_01_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SMG_02_ACO_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SMG_02_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SMG_05_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShell {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShellBlue {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShellGreen {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShellOrange {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShellPurple {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShellRed {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class SmokeShellYellow {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class ToolKit {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_O_PilotCoveralls {
	    weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class V_PlateCarrierSpec_rgr {
	    weight = 2;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class 100Rnd_580x42_Mag_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 100Rnd_580x42_Mag_Tracer_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 100Rnd_65x39_caseless_mag {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 100Rnd_65x39_caseless_mag_Tracer {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 100Rnd_65x39_caseless_black_mag: 100Rnd_65x39_caseless_mag {};
	class 100Rnd_65x39_caseless_black_mag_tracer: 100Rnd_65x39_caseless_mag {};
	class 10Rnd_127x54_Mag {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 10Rnd_338_Mag {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 10Rnd_50BW_Mag_F {
	    weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 10Rnd_762x54_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 10Rnd_9x21_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 11Rnd_45ACP_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 150Rnd_556x45_Drum_Mag_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 150Rnd_556x45_Drum_Mag_Tracer_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 16Rnd_9x21_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 200Rnd_556x45_Box_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 200Rnd_556x45_Box_Tracer_Red_F {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 200Rnd_65x39_cased_Box {
	    weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 20Rnd_556x45_UW_mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 20Rnd_650x39_Cased_Mag_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 20Rnd_762x51_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_45ACP_Mag_SMG_01 {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_545x39_Mag_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_545x39_Mag_Tracer_Green_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_556x45_Stanag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_556x45_Stanag_Tracer_Red {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_580x42_Mag_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_580x42_Mag_Tracer_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_65x39_caseless_mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_65x39_caseless_mag_Tracer: 30Rnd_65x39_caseless_mag {
		// Cop tracers are APD equipment and should be seizable from houses
		illegal = true;
	};
	class 30Rnd_65x39_caseless_green: 30Rnd_65x39_caseless_mag {};
	class 30Rnd_65x39_caseless_green_mag_Tracer: 30Rnd_65x39_caseless_mag {};
	class 30Rnd_65x39_caseless_black_mag_Tracer: 30Rnd_65x39_caseless_mag {};
	class 30Rnd_65x39_caseless_black_mag: 30Rnd_65x39_caseless_mag {};

	class 30Rnd_762x39_Mag_F {
		weight = 1;
		illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_Mag_Tracer_Green_F {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_9x21_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_9x21_Mag_SMG_02 {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_9x21_Mag_SMG_02_Tracer_Green {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 6Rnd_45ACP_Cylinder {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 9Rnd_45ACP_Mag {
	    weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 6Rnd_RedSignal_F {
    	weight = 1;
    	illegal = false;
    	compVal = 0;
		varOne = 0;
	};
	class 6Rnd_GreenSignal_F {
    	weight = 1;
    	illegal = false;
    	compVal = 0;
		varOne = 0;
	};
	class RPG7_F {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class Titan_AA {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class LMG_Zafir_F {
	    weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class 150Rnd_762x54_Box_Tracer {
	    weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class SMG_03_TR_hex {
		weight = 2;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class SMG_03_TR_camo: SMG_03_TR_hex {};
	class SMG_03_TR_black: SMG_03_TR_hex {};
	class SMG_03C_TR_camo: SMG_03_TR_hex {};
	class 50Rnd_570x28_SMG_03 {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	// Contact DLC Stuff
	class U_C_Uniform_Farmer_01_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_I_L_Uniform_01_tshirt_black_F : U_C_Uniform_Farmer_01_F{};
	class U_I_L_Uniform_01_tshirt_olive_F : U_C_Uniform_Farmer_01_F{};
	class U_I_L_Uniform_01_tshirt_skull_F : U_C_Uniform_Farmer_01_F{};
	class U_I_L_Uniform_01_tshirt_sport_F : U_C_Uniform_Farmer_01_F{};
	class U_C_Uniform_Scientist_01_formal_F : U_C_Uniform_Farmer_01_F{};
	class U_C_Uniform_Scientist_01_F : U_C_Uniform_Farmer_01_F{};
	class U_C_Uniform_Scientist_02_formal_F : U_C_Uniform_Farmer_01_F{};
	class U_C_Uniform_Scientist_02_F : U_C_Uniform_Farmer_01_F{};
	class H_Beret_EAF_01_F {
		weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class G_Respirator_blue_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Hat_Tinfoil_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class H_Booniehat_mgrn : H_Hat_Tinfoil_F{};
	class H_Booniehat_taiga : H_Hat_Tinfoil_F{};
	class H_Booniehat_wdl : H_Hat_Tinfoil_F{};
	class H_Booniehat_eaf : H_Hat_Tinfoil_F{};
	class H_MilCap_grn : H_Hat_Tinfoil_F{};
	class H_MilCap_taiga : H_Hat_Tinfoil_F{};
	class H_MilCap_wdl : H_Hat_Tinfoil_F{};
	class H_MilCap_eaf : H_Hat_Tinfoil_F{};
	class H_Tank_black_F : H_Hat_Tinfoil_F{};
	class H_HelmetB_light_wdl : H_Hat_Tinfoil_F{};
	class H_HelmetSpecB_wdl : H_Hat_Tinfoil_F{};
	class H_HelmetHBK_headset_F : H_Hat_Tinfoil_F{};
	class H_HelmetHBK_chops_F : H_Hat_Tinfoil_F{};
	class H_HelmetHBK_ear_F : H_Hat_Tinfoil_F{};
	class H_HelmetB_plain_wdl : H_Hat_Tinfoil_F{};
	class H_HelmetHBK_F : H_Hat_Tinfoil_F{};
	class G_AirPurifyingRespirator_02_black_F : G_Respirator_blue_F{};
	class G_AirPurifyingRespirator_01_F : G_Respirator_blue_F{};
	class G_RegulatorMask_F : G_Respirator_blue_F{};
	class U_C_CBRN_Suit_01_Blue_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class U_B_CBRN_Suit_01_MTP_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_B_CBRN_Suit_01_Tropic_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_C_CBRN_Suit_01_White_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_B_CBRN_Suit_01_Wdl_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_I_CBRN_Suit_01_AAF_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_I_E_CBRN_Suit_01_EAF_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_I_E_Uniform_01_sweater_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_B_CombatUniform_mcam_wdl_f : U_C_CBRN_Suit_01_Blue_F{};
	class U_B_CombatUniform_tshirt_mcam_wdL_f : U_C_CBRN_Suit_01_Blue_F{};
	class U_I_L_Uniform_01_camo_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_I_L_Uniform_01_deserter_F : U_C_CBRN_Suit_01_Blue_F{};
	class U_B_CombatUniform_vest_mcam_wdl_f : U_C_CBRN_Suit_01_Blue_F{};
	class U_Tank_green_F : U_C_CBRN_Suit_01_Blue_F{};
	class V_SmershVest_01_F {
		weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_SmershVest_01_radio_F : V_SmershVest_01_F{};
	class V_CarrierRigKBT_01_Olive_F {
		weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_CarrierRigKBT_01_EAF_F : V_CarrierRigKBT_01_Olive_F{};
	class V_PlateCarrier1_wdl : V_CarrierRigKBT_01_Olive_F{};
	class V_PlateCarrier2_grn : V_CarrierRigKBT_01_Olive_F{};
	class V_PlateCarrier2_wdl : V_CarrierRigKBT_01_Olive_F{};
	class V_CarrierRigKBT_01_light_Olive_F : V_CarrierRigKBT_01_Olive_F{};
	class V_CarrierRigKBT_01_light_EAF_F : V_CarrierRigKBT_01_Olive_F{};
	class V_CarrierRigKBT_01_Heavy_Olive_F {
		weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class V_CarrierRigKBT_01_Heavy_EAF_F : V_CarrierRigKBT_01_Heavy_Olive_F{};
	class V_PlateCarrierGL_grn : V_CarrierRigKBT_01_Heavy_Olive_F{};
	class V_PlateCarrierGL_wdl : V_CarrierRigKBT_01_Heavy_Olive_F{};
	class U_O_R_Gorka_01_F {
		weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class U_O_R_Gorka_01_brown_F : U_O_R_Gorka_01_F{};
	class U_O_R_Gorka_01_camo_F : U_O_R_Gorka_01_F{};
	class U_O_R_Gorka_01_black_F : U_O_R_Gorka_01_F{};
	class H_HelmetSpecO_blk {
		weight = 1;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class H_HelmetSpecO_ghex_F : H_HelmetSpecO_blk{};
	class H_HelmetSpecO_ocamo : H_HelmetSpecO_blk{};
	class H_HelmetAggressor_F : H_HelmetSpecO_blk{};
	class H_HelmetAggressor_cover_F : H_HelmetSpecO_blk{};
	class H_HelmetAggressor_cover_taiga_F : H_HelmetSpecO_blk{};
	class hgun_Pistol_heavy_01_green_F : hgun_Pistol_heavy_01_F{};
	class arifle_MSBS65_black_F {
		weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MSBS65_GL_black_F : arifle_MSBS65_black_F{};
	class arifle_MSBS65_Mark_black_F : arifle_MSBS65_black_F{};
	class arifle_AK12U_F : arifle_MSBS65_black_F{};
	class arifle_AK12_F : arifle_MSBS65_black_F{};
	class arifle_AK12_GL_F : arifle_MSBS65_black_F{};
	class arifle_RPK12_F {
		weight = 4;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MSBS65_camo_F {
		weight = 3;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class arifle_MSBS65_sand_F : arifle_MSBS65_camo_F{};
	class arifle_MSBS65_Mark_camo_F : arifle_MSBS65_camo_F{};
	class arifle_MSBS65_Mark_sand_F : arifle_MSBS65_camo_F{};
	class arifle_MSBS65_UBS_camo_F : arifle_MSBS65_camo_F{};
	class arifle_AK12_arid_F {
		weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class arifle_AK12U_arid_F : arifle_AK12_arid_F{};
	class arifle_AK12_lush_F : arifle_AK12_arid_F{};
	class arifle_AK12U_lush_F : arifle_AK12_arid_F{};
	class arifle_RPK12_arid_F : arifle_RPK12_F{};
	class arifle_RPK12_lush_F : arifle_RPK12_F{};
	class sgun_HunterShotgun_01_F {
		weight = 3;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class hgun_P07_khk_F {
		weight = 2;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class sgun_HunterShotgun_01_sawedoff_F {
		weight = 2;
	    illegal = true;
		compVal = 0;
		varOne = 0;
	};
	class 2Rnd_12Gauge_Pellets {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 2Rnd_12Gauge_Slug {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 6Rnd_12Gauge_Pellets {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 6Rnd_12Gauge_Slug {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class srifle_DMR_06_hunter_F : srifle_DMR_06_olive_F{};
	class LMG_Mk200_black_F : LMG_Mk200_F{};
	class G_Blindfold_01_black_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_65x39_caseless_msbs_mag {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_65x39_caseless_msbs_mag_Tracer {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_AK12_Mag_Tracer_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_AK12_Mag_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_AK12_Arid_Mag_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_AK12_Arid_Mag_Tracer_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_AK12_Lush_Mag_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 30Rnd_762x39_AK12_Lush_Mag_Tracer_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 10Rnd_762x51_Mag {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 75Rnd_762x39_Mag_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
	class 75Rnd_762x39_Mag_Tracer_F {
		weight = 1;
	    illegal = false;
		compVal = 0;
		varOne = 0;
	};
};
