//  File: fn_intro.sqf
//	Original Author: HellsGate
//	Date Created: 12/14/2013
//	Description: Creates an intro on the left hand corner of the screen.
//	Notice: Heavily modified by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> for www.olympus-entertainment.com

private ["_onScreenTime","_role1","_role1names","_role2","_role2names","_role3","_role3names","_role4","_role4names","_role5","_role5names","_role6","_role6names","_role7","_role7names","_role8","_role8names","_role9","_role9names","_role10","_role10names","_role11","_role11names","_role12","_role12names","_role13","_role13names","_role16","_role16names","_memberFunction","_memberNames","_finalText","_role17","_role17names"];

waitUntil{!life_loadingSystemActive};

// FORMAT:
// _ROLE# = ["ROLE NAME","HTML COLOR CODE"];
// _ROLE#NAMES = ["NAME1","NAME2"..ECT];

_onScreenTime = 4;
_role1 = ["Welcome to","#ffd400"];
_role1names = ["Olympus Altis Life RPG"];
_role2 = ["Founder:","#ff0000"];
_role2names = ["Poseidon"];
_role14 = ["Owner:","#daa520"];
_role14names = ["Ryan"];
_role18 = ["Lead Developer:","#00fffa"];
_role18names = ["Zahzi"];
_role3 = ["Senior Developers:","#00fffa"];
_role3names = ["Civak","Fraali","Fusah"];
_role4 = ["Developers:","#00fffa"];
_role4names = ["Horizon","Raykazi","Tech","Trimorphious"];
_role5 = ["Head Admin:","#ff0000"];
_role5names = ["Grandma Gary"];
_role6 = ["Senior Admins:","#ff0000"];
_role6names = ["Destruct","Doc","Hylos"];
_role7 = ["Admins:","#ff0000"];
_role7names = ["Creepy","Cyanide","Headless","Kamikaze","Panda","ThatNerdyGuy"];
_role8 = ["Moderators:","#15ff00"];
_role8names = ["Claysive","Drama","Mike Lit","Noahhh!","Slumberjack"];
_role17= ["Lead Designers:","#6c00d0"];
_role17names = ["Mako","Secret Agent", "zoomzooooooom"];
_role16 = ["Senior Designers:","#6c00d0"];
_role16names = ["Tyronne Littlehands"];
_role9 = ["Designers:","#6c00d0"];
_role9names = ["Brolaf","Headless","Jig","Masoooooooooon","Stelar","Strae","SystemChips","Zeuse"];
//_role15 = ["Support Member of the Month:","#ff7700"];
//_role15names = ["Corps"];
_role10 = ["Website:","#0050ff"];
_role10names = ["www.olympus-entertainment.com"];
_role11 = ["TeamSpeak / Discord:","#0050ff"];
_role11names = ["ts.olympus-entertainment.com","discord.gg/cqf5m8F"];
//_role12 = ["Discord:","#0050ff"];
//_role12names = ["discord.gg/cqf5m8F"];
_role13 = ["IMPORTANT NOTICE:","#ff0000"];
_role13names = ["Please press M and read the server rules","Not knowing the rules does not make you exempt from them."];

{
	uiSleep 2;
	_memberFunction = _x select 0;
	_memberNames = _x select 1;
	_finalText = format ["<t size='1.5' font='PuristaSemiBold' color='%2' align='left'>%1<br /></t>", _memberFunction select 0,_memberFunction select 1];
	_finalText = _finalText + "<t size='1.1' font='EtelkaMonospacePro' color='#FFFFFF' align='left'>";
	{
		_finalText = _finalText + format ["%1<br />", _x];
		} forEach _memberNames;
	_finalText = _finalText + "</t>";
	_onScreenTime + (((count _memberNames) - 1) * 0.9);
	[
		parseText _finalText,
		[safezoneX + 0.05,safezoneY + safezoneH - 1,1,1],
		nil,
		_onScreenTime,
		0.7,
		0
		] spawn BIS_fnc_textTiles;
	uiSleep (_onScreenTime);
} forEach [
	[_role1, _role1names],
	[_role14, _role14names],
	[_role18, _role18names],
	[_role3, _role3names],
	[_role4, _role4names],
	[_role5, _role5names],
	[_role6, _role6names],
	[_role7, _role7names],
	[_role8, _role8names],
	[_role17, _role17names],
	[_role16, _role16names],
	[_role9, _role9names],
	[_role2, _role2names],
//	[_role15, _role15names],
	[_role10, _role10names],
	[_role11, _role11names],
//	[_role12, _role12names],
	[_role13, _role13names]
];
