//------------------------------------------------------------------------------------------------
//! Altis Life HUD Component
//! Main HUD system for displaying player information, cash, bank balance, and notifications
//! Converted from Olympus Altis Life HUD system
//------------------------------------------------------------------------------------------------

class AltisLifeHUDComponent : SCR_InfoDisplayExtended
{
	// UI Elements
	protected Widget m_wRoot;
	protected TextWidget m_wCashDisplay;
	protected TextWidget m_wBankDisplay;
	protected TextWidget m_wPlayerNameDisplay;
	protected TextWidget m_wRoleDisplay;
	protected Widget m_wNotificationPanel;
	protected Widget m_wInteractionPanel;
	
	// Data references
	protected PlayerDataComponent m_PlayerData;
	protected SessionManagerComponent m_SessionManager;
	protected ConfigurationManager m_ConfigManager;
	
	// HUD state
	protected bool m_bHUDVisible = true;
	protected bool m_bInitialized = false;
	
	// Notification system
	protected ref array<ref NotificationData> m_aNotifications = {};
	protected const int MAX_NOTIFICATIONS = 5;
	protected const float NOTIFICATION_DURATION = 5.0;
	
	// Update timers
	protected float m_fLastUpdate = 0;
	protected const float UPDATE_INTERVAL = 1.0; // Update every second
	
	//------------------------------------------------------------------------------------------------
	//! Initialize HUD component
	override void OnInit(Widget w)
	{
		super.OnInit(w);
		
		m_wRoot = w;
		
		// Get UI elements
		InitializeUIElements();
		
		// Get player components
		InitializePlayerComponents();
		
		// Set up event listeners
		SetupEventListeners();
		
		// Initial update
		UpdateHUD();
		
		m_bInitialized = true;
		Print("[AltisLifeHUD] HUD initialized successfully");
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize UI elements
	protected void InitializeUIElements()
	{
		m_wCashDisplay = TextWidget.Cast(m_wRoot.FindAnyWidget("CashDisplay"));
		m_wBankDisplay = TextWidget.Cast(m_wRoot.FindAnyWidget("BankDisplay"));
		m_wPlayerNameDisplay = TextWidget.Cast(m_wRoot.FindAnyWidget("PlayerNameDisplay"));
		m_wRoleDisplay = TextWidget.Cast(m_wRoot.FindAnyWidget("RoleDisplay"));
		m_wNotificationPanel = m_wRoot.FindAnyWidget("NotificationPanel");
		m_wInteractionPanel = m_wRoot.FindAnyWidget("InteractionPanel");
		
		// Validate critical elements
		if (!m_wCashDisplay || !m_wBankDisplay)
		{
			Print("[AltisLifeHUD] ERROR: Critical UI elements not found");
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize player components
	protected void InitializePlayerComponents()
	{
		// Get player entity and components
		IEntity playerEntity = GetGame().GetPlayerController().GetControlledEntity();
		if (playerEntity)
		{
			m_PlayerData = PlayerDataComponent.Cast(playerEntity.FindComponent(PlayerDataComponent));
			m_SessionManager = SessionManagerComponent.Cast(playerEntity.FindComponent(SessionManagerComponent));
		}
		
		// Get configuration manager
		m_ConfigManager = ConfigurationManager.GetInstance();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Set up event listeners
	protected void SetupEventListeners()
	{
		if (m_PlayerData)
		{
			m_PlayerData.m_OnCashChanged.Insert(OnCashChanged);
			m_PlayerData.m_OnBankBalanceChanged.Insert(OnBankBalanceChanged);
		}
		
		if (m_SessionManager)
		{
			m_SessionManager.m_OnPlayerInitialized.Insert(OnPlayerInitialized);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Update HUD display
	override void Update(float timeSlice)
	{
		super.Update(timeSlice);
		
		if (!m_bInitialized)
			return;
			
		float currentTime = GetGame().GetWorld().GetWorldTime();
		
		// Update HUD at regular intervals
		if (currentTime - m_fLastUpdate >= UPDATE_INTERVAL)
		{
			UpdateHUD();
			m_fLastUpdate = currentTime;
		}
		
		// Update notifications
		UpdateNotifications(timeSlice);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Update HUD information
	protected void UpdateHUD()
	{
		if (!m_PlayerData)
			return;
			
		// Update cash display
		if (m_wCashDisplay)
		{
			string cashText = string.Format("Cash: $%1", FormatMoney(m_PlayerData.GetCash()));
			m_wCashDisplay.SetText(cashText);
		}
		
		// Update bank display
		if (m_wBankDisplay)
		{
			string bankText = string.Format("Bank: $%1", FormatMoney(m_PlayerData.GetBankBalance()));
			m_wBankDisplay.SetText(bankText);
		}
		
		// Update player name
		if (m_wPlayerNameDisplay)
		{
			PlayerController pc = GetGame().GetPlayerController();
			if (pc)
				m_wPlayerNameDisplay.SetText(pc.GetPlayerName());
		}
		
		// Update role display
		if (m_wRoleDisplay)
		{
			PlayerRole role = m_PlayerData.GetPlayerRole();
			string roleText = GetRoleDisplayName(role);
			m_wRoleDisplay.SetText(roleText);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Format money with commas
	protected string FormatMoney(int amount)
	{
		string amountStr = amount.ToString();
		string formatted = "";
		int len = amountStr.Length();
		
		for (int i = 0; i < len; i++)
		{
			if (i > 0 && (len - i) % 3 == 0)
				formatted += ",";
			formatted += amountStr[i];
		}
		
		return formatted;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get display name for player role
	protected string GetRoleDisplayName(PlayerRole role)
	{
		switch (role)
		{
			case PlayerRole.CIVILIAN: return "Civilian";
			case PlayerRole.POLICE: return "Police Officer";
			case PlayerRole.MEDICAL: return "Paramedic";
			case PlayerRole.ADMIN: return "Administrator";
			default: return "Unknown";
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Show notification
	void ShowNotification(string message, string type = "info", float duration = 5.0)
	{
		// Remove oldest notification if at max capacity
		if (m_aNotifications.Count() >= MAX_NOTIFICATIONS)
		{
			m_aNotifications.RemoveOrdered(0);
		}
		
		// Create new notification
		ref NotificationData notification = new NotificationData();
		notification.m_sMessage = message;
		notification.m_sType = type;
		notification.m_fDuration = duration;
		notification.m_fStartTime = GetGame().GetWorld().GetWorldTime();
		
		m_aNotifications.Insert(notification);
		
		// Update notification display
		UpdateNotificationDisplay();
		
		Print(string.Format("[AltisLifeHUD] Notification: %1 (%2)", message, type));
	}
	
	//------------------------------------------------------------------------------------------------
	//! Update notifications
	protected void UpdateNotifications(float timeSlice)
	{
		float currentTime = GetGame().GetWorld().GetWorldTime();
		bool needsUpdate = false;
		
		// Remove expired notifications
		for (int i = m_aNotifications.Count() - 1; i >= 0; i--)
		{
			NotificationData notification = m_aNotifications[i];
			if (currentTime - notification.m_fStartTime >= notification.m_fDuration)
			{
				m_aNotifications.RemoveOrdered(i);
				needsUpdate = true;
			}
		}
		
		// Update display if notifications changed
		if (needsUpdate)
			UpdateNotificationDisplay();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Update notification display
	protected void UpdateNotificationDisplay()
	{
		if (!m_wNotificationPanel)
			return;
			
		// Clear existing notifications
		Widget child = m_wNotificationPanel.GetChildren();
		while (child)
		{
			Widget next = child.GetSibling();
			child.RemoveFromHierarchy();
			child = next;
		}
		
		// Add current notifications
		foreach (NotificationData notification : m_aNotifications)
		{
			CreateNotificationWidget(notification);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Create notification widget
	protected void CreateNotificationWidget(NotificationData notification)
	{
		// TODO: Create actual notification widget
		// For now, just print to console
		Print(string.Format("[Notification] %1", notification.m_sMessage));
	}
	
	//------------------------------------------------------------------------------------------------
	//! Toggle HUD visibility
	void ToggleHUD()
	{
		m_bHUDVisible = !m_bHUDVisible;
		
		if (m_wRoot)
			m_wRoot.SetVisible(m_bHUDVisible);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Set HUD visibility
	void SetHUDVisible(bool visible)
	{
		m_bHUDVisible = visible;
		
		if (m_wRoot)
			m_wRoot.SetVisible(m_bHUDVisible);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Event handlers
	protected void OnCashChanged(int oldAmount, int newAmount)
	{
		// Immediate update for cash changes
		if (m_wCashDisplay)
		{
			string cashText = string.Format("Cash: $%1", FormatMoney(newAmount));
			m_wCashDisplay.SetText(cashText);
		}
		
		// Show notification for significant changes
		int difference = newAmount - oldAmount;
		if (Math.AbsInt(difference) >= 100)
		{
			string message;
			string type;
			
			if (difference > 0)
			{
				message = string.Format("Received $%1", FormatMoney(difference));
				type = "success";
			}
			else
			{
				message = string.Format("Lost $%1", FormatMoney(-difference));
				type = "warning";
			}
			
			ShowNotification(message, type);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	protected void OnBankBalanceChanged(int oldAmount, int newAmount)
	{
		// Immediate update for bank changes
		if (m_wBankDisplay)
		{
			string bankText = string.Format("Bank: $%1", FormatMoney(newAmount));
			m_wBankDisplay.SetText(bankText);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	protected void OnPlayerInitialized(SessionManagerComponent sessionManager)
	{
		ShowNotification("Welcome to Altis Life!", "info", 3.0);
		UpdateHUD();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Cleanup
	override void OnDelete()
	{
		// Remove event listeners
		if (m_PlayerData)
		{
			m_PlayerData.m_OnCashChanged.Remove(OnCashChanged);
			m_PlayerData.m_OnBankBalanceChanged.Remove(OnBankBalanceChanged);
		}
		
		if (m_SessionManager)
		{
			m_SessionManager.m_OnPlayerInitialized.Remove(OnPlayerInitialized);
		}
		
		super.OnDelete();
	}
}

//------------------------------------------------------------------------------------------------
//! Notification data structure
class NotificationData
{
	string m_sMessage;
	string m_sType;
	float m_fDuration;
	float m_fStartTime;
}
