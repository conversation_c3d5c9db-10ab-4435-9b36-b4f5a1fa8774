// File: fn_availableHouse.sqf
// Author: <PERSON> "tkcje<PERSON>" <PERSON>
params [
	["_house",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]]
];

if (isNull _house) exitWith {closeDialog 0;};
if (playerSide isEqualTo independent) exitWith {};

//house is in conquest zone and conquest is active or in pregame
if (oev_conquestData select 0 && position player inPolygon (oev_conquestData select 1 select 1)) exitWith {
	hint "There's no time for real estate during Conquest!";
};

if !(dialog) then {
	["houseAvailable"] call OEC_fnc_createDialog;
};

disableSerialization;

private _display = findDisplay 109000;
private _purchaseBtn = _display displayCtrl 109008;
private _adminCheckKeys = _display displayCtrl 109009;
_adminCheckKeys ctrlEnable false;
_adminCheckKeys ctrlSetText "Check Keys";
_purchaseBtn ctrlEnable false;
_purchaseBtn ctrlSetText "Purchase";

life_pInact_curTarget = _house;
if((!(_house in oev_vehicles) && !(getPlayerUID player in (_house getVariable["keyPlayers",[]]))) || isNil {_house getVariable "house_owner"}) then {
	if(_house in oev_vehicles) then {oev_vehicles = oev_vehicles - [_house];};

	private _houseCfg = [(typeOf _house)] call OEC_fnc_houseConfig;
	if (count _houseCfg isEqualTo 0) exitWith {};

	ctrlSetText[109001,format["%1",getText(configFile >> "CfgVehicles" >> (typeOf _house) >> "displayName")]];

	if (!isNil {_house getVariable "house_owner"} && ((_house getVariable ["for_sale",""]) isEqualTo "")) then {
		if((call life_adminlevel) >= 3) then {
			_adminCheckKeys ctrlEnable true;
			_adminCheckKeys buttonSetAction "[life_pInact_curTarget] call OEC_fnc_copHouseOwner;";
		};
		ctrlSetText[109002,"Price: Unavailable"];
		ctrlSetText[109003,"This property is already owned."];
	} else {
		// If house is listed for sale by a player
		if !((_house getVariable ["for_sale",""]) isEqualTo "") then {
			ctrlSetText[109002,format["Price: $%1",([(_house getVariable ["for_sale",""]) select 1] call OEC_fnc_numberText)]];
			ctrlSetText[109003,format["This property is available for purchase from %1",((_house getVariable ["house_owner",""]) select 1)]];
			ctrlSetText[109005,format["Current virtual space: %1. Current physical Space %2.",(_house getVariable ["storageCapacity",10000]),(_house getVariable ["physicalStorageCapacity",10000])]];
			if ((typeOf _house) in ["Land_i_Garage_V1_F","Land_i_Garage_V2_F"]) then {
				ctrlSetText[109006,"You can pull vehicles from this location."];
				ctrlSetText[109007,"You can spawn at this location after death."];
			} else {
				ctrlSetText[109006,"You can upgrade this building to allow for oil storage."];
				ctrlSetText[109007,"You can spawn at this location after death."];
			};
			_purchaseBtn ctrlEnable true;
			_purchaseBtn buttonSetAction "[life_pInact_curTarget] spawn OEC_fnc_buyHouse; closeDialog 0;";
			if((call life_adminlevel) >= 3) then {
				_adminCheckKeys ctrlEnable true;
				_adminCheckKeys buttonSetAction "[life_pInact_curTarget] call OEC_fnc_copHouseOwner;";
			};
		} else {
			// If house is just available (NOT LISTED BY PLAYER)
			ctrlSetText[109002,format["Price: $%1",[(_houseCfg select 0)] call OEC_fnc_numberText]];
			ctrlSetText[109003,"This property is available for purchase."];
			ctrlSetText[109005,format["Maximum virtual space: %1. Maximum physical Space %2.",((_houseCfg select 1) * 700),((_houseCfg select 1) * 200) + 100]];
			if ((typeOf _house) in ["Land_i_Garage_V1_F","Land_i_Garage_V2_F"]) then {
				ctrlSetText[109006,"You can pull vehicles from this location."];
				ctrlSetText[109007,"You can spawn at this location after death."];
			} else {
				ctrlSetText[109006,"You can upgrade this building to allow for oil storage."];
				ctrlSetText[109007,"You can spawn at this location after death."];
			};
			_purchaseBtn ctrlEnable true;
			_purchaseBtn buttonSetAction "[life_pInact_curTarget] spawn OEC_fnc_buyHouse; closeDialog 0;";
		};
	};
};
