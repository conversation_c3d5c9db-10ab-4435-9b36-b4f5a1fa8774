	IgiLoad v0.9.10_RC_d
	Version info: This is not official version of IgiLoad it is only WIP (RC)
	Author: Igi_PL
	Web: http:www.igipl.net/
	Version date: 2014.06.22

	USE:
	1. In mission "init.sqf" add line: "0 = execVM"IgiLoad\IgiLoadInit.sqf";".
	2. In vehicles "INITIALIZATION" field type: "0 = [this] execVM"IgiLoad\IgiLoad.sqf";"
	3. Unload from script or trigger:
		a) Unloading cargo from script. Force unload: "0 = [Car, true, "L"] spawn IL_Do_Unload;"
		b) Unloading cargo from script. Force unload: "0 = [Car, true] spawn IL_Do_Unload;"
		c) Unloading cargo from script. Force unload: "0 = [Car] spawn IL_Do_Unload;"
	4. Loading cargo from script. Force load: "0 = [Car, [typeOf Box], "B", true, Box] spawn IL_Do_Load;"

	Ways from points 1 and 2 can not be used simultaneously!!!


	Changelog v0.9.10_RC_d:
	New: Change in mass of vehicle. IgiLoad add mass of ALL atached objects to vehicle mass.
	New: Variable for enable or disable change of vehicle mass IL_Mass.
	New: New cargoes non vehicle: "C_supplyCrate_F", "Box_mas_all_rifle_Wps_F", "Box_mas_us_rifle_Wps_F", "Box_mas_ru_rifle_Wps_F", "Box_mas_mar_NATO_equip_F", "Box_mas_mar_NATO_Wps_F"
	New: New cargoes vehicle: "C_Hatchback_01_sport_F", "B_mas_mar_MRAP_01_F", "B_mas_mar_MRAP_01_F", "B_mas_mar_MRAP_01_med_F", "B_mas_mar_MRAP_01_gmg_F", "B_mas_mar_MRAP_01_hmg_F", "B_mas_mar_Quadbike_01_F", "B_mas_mar_Offroad_01_F", "B_mas_mar_Offroad_01_armed_F", "B_mas_mar_Boat_Transport_01_F", "B_mas_mar_SDV_01_F"
	New: New transport vehicles: "B_mas_mar_Truck_01_covered_F", "B_mas_mar_Truck_01_transport_F", "B_mas_mar_Heli_Light_01_F", "B_mas_mar_Offroad_01_F"
	New: New transport vehicles: "kyo_MH47E_HC", "kyo_MH47E_Ramp", "kyo_MH47E_base"
	New: Variables to control the parachute opening height: IL_Para_Drop_Open_ATL, IL_Para_Jump_Open_ATL. Set to 0 to open parachute immediately.
	New: Parachute will not open immediately. The default limit of height is 150m for player and 200m for cargo.
	New: Parachutes get player and cargo velocity.
	New: Score is added to player after loading and unloading cargo.
	New: Variables to control added score: IL_Load_Score and IL_Unload_Score.
	New: Variables: IL_Para_Drop_Velocity, IL_Para_Jump_Velocity. Set to false do do not set velocity to parachutes.
	New: Land Cargo20 are supported cargoes non vehicle for HEMTT, ZAMAK and TEMPEST (damaging C-17 and C-130): "Land_Cargo20_blue_F", "Land_Cargo20_brick_red_F", "Land_Cargo20_cyan_F", "Land_Cargo20_grey_F", "Land_Cargo20_light_blue_F", "Land_Cargo20_light_green_F", "Land_Cargo20_military_green_F", "Land_Cargo20_orange_F", "Land_Cargo20_red_F", "Land_Cargo20_sand_F", "Land_Cargo20_white_F", "Land_Cargo20_yellow_F"
	New: IgiLoad set new mass for Land Cargo20 to 2400. Arma default was set to 20000.
	Change: Cargoes zload is from boundingBoxReal.
	Change: Mohawks can load UGV.
	Change: Longer unload path for unload with parachute in C-17 and C-130.
	Fix: Cargo drop at 000000 by attaching chute to cargo after create chute.
	Fix: Rotation of attached objects.

	Changelog v0.9.9:
	New: New vehicles - Boeing C-17 by randomslap: "globemaster_c17_altus", "globemaster_c17_701", "globemaster_c17_703", "globemaster_c17_704",
												 "globemaster_c17_705", "globemaster_c17_dover", "globemaster_c17_edwards", "globemaster_c17_Elmendorf",
												 "globemaster_c17", "globemaster_c17_hickam", "globemaster_c17_IAF", "globemaster_c17_March",
												 "globemaster_c17_mcchord", "globemaster_c17_McGuire", "globemaster_c17_Mississipi", "globemaster_c17_NATO",
												 "globemaster_c17_natoPAPA", "globemaster_c17_Qatar", "globemaster_c17_RAAF", "globemaster_c17_ZZ172_RAF",
												 "globemaster_c17_RCAF", "globemaster_c17_Stewart", "globemaster_c17_therock", "globemaster_c17_travis",
												 "globemaster_c17_UAE", "globemaster_c17_wright_patt".
	New: New vehicles - Arma 3 by Bohemia Interactive Studio: "O_Truck_03_transport_F", "O_Truck_03_covered_F".
	New: New vehicles - US Marine Corp and MARSOC units by massi: "Marinir_Truck_01_box_FG", "Marinir_Truck_01_transport_FG", "Marinir_Truck_01_covered_FG", "Marinir_CH49_Mohawk_FG".
	New: New cargoes - US Marine Corp and MARSOC units by massi: "Marinir_B_MRAP_01_FG", "Marinir_MRAP_01_gmg_FG", "Marinir_MRAP_01_hmg_FG", "Marinir_duck_base_F".
	New: New cargoes - NATO Strider by Nightmare515: "Night_B_MRAP_03_F", "Night_B_MRAP_03_gmg_F", "Night_B_MRAP_03_hmg_F".
	Change: C130J Transport by theebu: Now can load cargo (7 slots).
	Change: C130J by theebu: No open/close cargo ramp option from IgiLoad.
	Change: New loading offset for "C_SUV_01_F".
	Fix: Bad color of smoke and chemical lights for not vehicles.
	Fix: Message about not supported vehicle.

	Changelog v0.9.8:
	New: IL_Para_Jump_ATL - Separated parachute jump altitude.
	New: IL_Can_Inside - Disable loading for driver, pilot and co-pilot.
	New: New vehicles:	"caf_HLVW_open_AR", "caf_HLVW_open", "caf_HLVW_covered_ar", "caf_HLVW_covered", "CH_147F", "CH47F", "CH49_Mohawk_FG"
	New: New cargoes:	"JTF2_Offroad_armed_01", "CAF_Quadbike_OD", "CAF_Quadbike_AR", "rc_hmmwv", "HMMWV2", "HMMWV_M1035", "M1114_AGS_ACR",
						"HMMWV_M1151_M2", "HMMWV2_M2", "HMMWV2_MK19", "HMMWV2_TOW", "HMMWV", "HMMWV_M2", "HMMWV_MK19", "HMMWV_TOW"
	Change: If you have a parachute you do not get a second. Parachute, which you have, you have to open yourself.
	Fix: Damage during parachute jump.
	Fix: Bad color of smoke and chemical lights for vehicles.

	Changelog v0.9.7:
	New: The script can be run from "init.sqf". By adding line "0 = execVM"IgiLoad\IgiLoadInit.sqf";" to init.sqf. The archive contains a sample init.sqf file.
	Attention! The script can not be called in this way when it is called in the "INITIALIZATION" field.
	New: The script itself will launch in existing vehicles and vehicles that will be created during the game. Only with init.sqf.
	New: Changes in the code to improve performance of script.
	New: A separate action in the action menu for loading vehicles and separate for loading other stuff. Allows you to load the vehicle loaded with cargo. This can be a problem when trying to load the vehicle with loaded quad.
	New: Initialization thread does not start immediately. Delay takes about 2-3 minutes. Values are random for each player.
		 Control of delay time is done with the help of two variables: "IL_Check_Veh_Min" and "IL_Check_Veh_Max".
		 Attention, these variables also control the frequency of thread to check whether the vehicle has been added during the game.
	New: C-130J by theebu (http:forums.bistudio.com/showthread.php?173431-C-130J-Port-Release) is now supported.
		 Attention! Loading, unloading and unloading cargo on parachute are possible when the ramp is fully open.
	New: New vehicles:	C-130J - "C130J_Cargo", "C130J";
						Offroad - "C_Offroad_01_F", "B_G_Offroad_01_F";
						VAN - "C_Van_01_box_F", "B_G_Van_01_transport_F", "C_Van_01_transport_F".
	New: New cargoes:	MRAP (C-130J) - "I_MRAP_03_F", "I_MRAP_03_gmg_F", "I_MRAP_03_hmg_F", "B_MRAP_01_F", "B_MRAP_01_gmg_F", "B_MRAP_01_hmg_F", "O_MRAP_02_F", "O_MRAP_02_gmg_F", "O_MRAP_02_hmg_F";
						UGV (C-130J, ZAMAK, HEMTT) - "B_UGV_01_rcws_F", "B_UGV_01_F", "O_UGV_01_rcws_F", "O_UGV_01_F", "I_UGV_01_rcws_F", "I_UGV_01_F";
						VAN (C-130J, ZAMAK, HEMTT) - "C_Van_01_box_F", "B_G_Van_01_transport_F", "C_Van_01_transport_F";
						Offroad (C-130J, ZAMAK, HEMTT) - "C_Offroad_01_F", "B_G_Offroad_01_F", "B_G_Offroad_01_armed_F";
						SUV (C-130J, HEMTT) - "C_SUV_01_F";
						Hatchback (C-130J, ZAMAK, HEMTT, Mohawk) - "C_Hatchback_01_F";
	Known Issue: Attention! The problem of exploding helicopters also occurs with the C-130J (http:feedback.arma3.com/view.php?id=17310). It also occurs when the boxes causing it will be loaded onto a vehicle, and the vehicle on the C-130J.
	Known Issue: C-130J Transport and IgiLoad usable ramp: Currently, only works with a closed bottom part of the ramp (you can also jump with a fully closed ramp). After solving the problem, I make appropriate changes in IgiLoad.
	Fix: SDV back on supported cargoes for CH-49 Mohawk.
	Fix: Parachutes collisions with CH-49 Mohawk in stationary flight.
	Change: Cargo parachutes for cargo.
	Change: Minimum altitude for the drop with parachute changed from 30 to 50 meters. The change forced by eliminating collisions and change parachute. Without changing this limit some players and cargo landed to the ground without a parachute.
	Change: Procedure "IL_Do_Load" has been modified in order to facilitate the preparation of vehicles loaded with cargo by scripts.

	Changelog v0.9.6:
	New: Useble ramp added. Ramp allows parachute jumping without losing the backpack. To disable set variable "IL_Ramp" to false.
	New: MH-9 is now supported.
	New: Added variable "IL_LU_Alt" to control the maximum height for loading and unloading without a parachute. Controls also use of ramp to the boarding and exiting (non para).
	Change: SDV has been excluded from the list of supported cargoes for CH-49 Mohawk. Probably it were merely to periscope, which causes damage to the helicopter
	Change: Cargoes excluded from the list of supported for CH-49 Mohawk (causes damage to the helicopter): "Box_NATO_Wps_F", "Box_East_Wps_F", "Box_IND_Wps_F", "Box_East_WpsLaunch_F", "Box_NATO_WpsLaunch_F", "Box_IND_WpsLaunch_F", "Box_IND_WpsSpecial_F", "Box_East_WpsSpecial_F", "Box_NATO_WpsSpecial_F", "Box_NATO_AmmoOrd_F", "Box_East_AmmoOrd_F", "Box_IND_AmmoOrd_F", "Box_NATO_Grenades_F", "Box_East_Grenades_F", "Box_IND_Grenades_F", "Box_NATO_Ammo_F", "Box_East_Ammo_F", "Box_IND_Ammo_F", "Box_IND_Support_F", "Box_East_Support_F", "Box_NATO_Support_F".

	Changelog v0.9.5:
	New: To supported cargoes have been added: "Land_CargoBox_V1_F".

	Changelog v0.9.4:
	New: To supported cargoes have been added (all): SDV and rubber boats.
	New: To supported cargoes have been added: "Land_BarrelEmpty_F", "Land_BarrelSand_F", "Land_BarrelTrash_F", "Land_BarrelWater_F" and "Land_MetalBarrel_F".
	New: To supported cargoes have been added: "Land_WaterBarrel_F" and "Land_WaterTank_F".
	Change: Init vehicle in separate procedure.
	Change: Certain variables are back global.
	Fix: After loading cargo direct from vehicle A to vehicle B, vehicle A had blocked the ability to completely unloading or loading a new cargo.
	Known Issue: "Land_WaterTank_F" do not rotate in MP (dedicated server). Bug in the game with AttachTo and setDir.
	Note: "Land_MetalBarrel_empty_F" and "MetalBarrel_burning_F" not added to supported cargoes. Physics does not work on them. It was invented by the BI, they are from different class than "Land_MetalBarrel_F".

	Changelog v0.9.3:
	New: Changelog is now in a separate file.
	New: Message about loading or unloading the cargo contains information about the amount of free space in the cargo hold.
	New: Easier to control the priorities of different types of action.
	New: Load and unload (not para) max speed (km/h) in variable LU_Speed.
	New: The minimum altitude for parachute drop in variable Para_Drop_ATL.
	Change: The script runs also on the dedicated server side. This allows to set variables in vehicles before first player joining the server.
	Change: Increased altitude for the parachute drop to 30m.
	Change: Altitude for parachute drop calculated in relation to the terrain, not to the object under the vehicle (getPos replaced with getPosATL).
	Change: Info Hints replaced with vehicleChat.
	Change: DevMode Hints replacet with globalChat.
	Change: Variables moved up in script.
	Change: Some global variables converted to local variables.
	Change: Variable Action_Priority was changed to three variables: _Action_LU_Priority, _Action_O_Priority and _Action_S_Priority. The purpose of the change is easier to control the priorities of different types of action.
	Fix: Known Issue "If a particular type of box is loaded at a specific position in the CH-49 Mohawk, the getPos returns incorrect altitude." from v0.9.2 partially solves through replace setPosATL with getPosATL for parachute drop.
	Known Issue: Change in v0.9.2 "Quads do not generate dust during transport." - It seems that the change does not solve the problem in single player.

	Changelog v0.9.2:
	New: Quads do not generate dust during transport. It can be problematic for AI and when the carrier is destroyed, and the quad will survive.
	New: Pilot can block usage of script for the copilot and from outside. Copilot and the driver can block usage of script from outside.
	New: The variables in the script to control from where you can use the script, if set to false, you can not allow to control script by copilot or outside from action menu (Can_CoPilot, Can_Outside).
	New: Parachute drop: Different color of smoke and chemical lights for vehicles during the parachute drop.
	New: Parachute drop: Parameters to control the color of smoke or chemical lights. Separate for vehicles and other cargo (Para_Smoke_Veh, Para_Light_Veh, Para_Smoke_Default, Para_Light_Default).
	New: Added parameter to control the priority of actions (menu item relative to other items) (Action_Priority).
	New: Action load appears on the screen if cargo is supported in the area of load and there is no other action with a higher priority in the menu. This means you can load using the MMB without using the menu.
	Change: Parachute drop: You can turn on/off separately smoke/chemical lights during the falling/after landing (Para_Smoke, Para_Light, Para_Smoke_Add, Para_Light_Add).
	Change: Quads are no longer rotated after loading. I changed the offset and it looks quite ok (a little tight).
	Change: Some changes in order of actions in menu.
	Change: Param: Outside replace with Can_Outside.
	Fix: Player, loaded with/on quad, should no longer see what is above CH-49 Mohawk. Lowered quad position in the cargo hold.
	Fix: JIP and Issue.
	Fix: For Known Issue from v0.9.1 - Parachute and small boxes.
	Known Issue: MH-9 Hummingbird - Attached objects collide with it - hideObject tested on MP, do not fix it - Temporarily disable a support for MH-9!!!
	Known Issue: If a particular type of box is loaded at a specific position in the CH-49 Mohawk, the getPos returns incorrect altitude. I use getPos because it allows to detect buildings under helicopter and I do not want to block the possibility of unloading on the roof of the building.
	Known Issue: If cargo is in a certain position then landing gear does not hide CH-49 Mohawk.

	Changelog v0.9.1:
	New: The ability to control from loading area (outside of the vehicle). Param: Outside
	Fix: Known Issue in v0.9.0 Minor changes are needed to: placing objects during loading and unloading, rotate objects when loaded and unloaded.

	Changelog v0.9.0:
	New: DevMod - Show hints for debug (todo: more hints to add).
	New: The ability to control damage to cargo (in script).
	New: Quads added to the list cargoes for HMETT, KAMAZ and CH-49. They can not carry anything yet, but it's probably a matter of time.
	New: MH-9 Humminbird is now supported. But - more in "Known Issues".
	New: Animation loading ramp "CargoRamp" in the CH-49. Now, before loading or unloading must be open.
	New: The co-pilot can perform loading and unloading (MH-9 and CH-49).
	New: I prepared a list of cargoes for backpacks. Not yet supported - more in "Known Issues".
	Change: Large modernization of code. Reduced the number of lines of code (about 200), and at the same time I added a lot of new things.
	Change: I think I've added all the "boxes" from ArmA.
	Change: Now you can easily control the acceptable loads for different types of vehicles.
	Known Issue: The parachute does not work with small crates. Small crates are "falling" upwards :D. It seems that the parachute has too much lifting force for such small loads. This caused the parachute drop of cargo from MH-9 is not possible yet.
	Known Issue: I can not find a backpacks near the vehicle using the "nearestObjects".
	Known Issue: Minor changes are needed to: placing objects during loading and unloading, rotate objects when loaded and unloaded.

	Changelog v0.8.1:
	Change: Code tweak and fix.
	New: Parachute cargo drop from CH-49 Mohawk. All cargo drop by one click.
	New: Levitating boxes eliminated by kept damage at "0".

	Changelog v0.7.1:
	Fix: Menu option show 2 times.
	Fix: CH-49 Mohawk unload tweak.

	Changelog v0.7.0:
	Change: CH-49 Mohawk unload script in function.
	Change: Reduced by 1, the number of slots for each of the supported vehicle.
	Increased load search area for helicopters (SDistL_Heli_offset).
	New: Limit height for unloading from helicopters (without parachute). ALT <= 2m over an object eg the roof of the building (not counted from the ground level).
	New: Parachute cargo drop from CH-49 Mohawk (ALT >= 20m). The minimum height is not safe for the load (parachute opening time).
	New: Load on parachute can have light and/or smoke (Para_Light, Para_Smoke).
	New: Maximum capacity for vehicles in the variables. It can be set up without major changes in the script (Num_Slots_MOHAWK, Num_Slots_KAMAZ and Num_Slots_HEMTT).

	Changelog v0.6.2:
	Change - cargo can be loaded from another vehicle (the problem with destroyed vehicles).
	New: Deleted notice that the vehicle is not supported by the script. With respawn script - information appeared on respawn every vehicle that is not supported.

	Changelog v0.6.1:
	Fixes and improvements in the variables.
	New: Color menu.
	New: Icons in action menu.

	Changelog v0.6.0:
	Box (cargo) initialization is not needed (auto init if cargo in range and can be load).
	Change search metod. Small area in defined place.
	KAMAZ supported.
	New: CH-49 Mohawk support.
	New: Speed limit for load and unload.

	Changelog v0.5.0
	Base version - no changelog.*/
