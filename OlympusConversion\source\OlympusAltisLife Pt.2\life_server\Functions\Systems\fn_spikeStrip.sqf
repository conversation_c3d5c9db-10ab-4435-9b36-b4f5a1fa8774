//	File: fn_spikeStrip.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine
//	Description:
//	This is the server-side part of it which constantly monitors the spike strip and vehicles near it.
//	First originally tried triggers but I was never any good at those nor do I like them as they
//	have a global effect.

private["_nearVehicles","_spikeStrip"];
_spikeStrip = param [0,Obj<PERSON><PERSON>,[Obj<PERSON><PERSON>]];
if(isNull _spikeStrip) exitWith {};

waitUntil {_nearVehicles = nearestObjects[getPos _spikeStrip,["Car"],5]; count _nearVehicles > 0 || isNull _spikeStrip};

if(isNull _spikeStrip) exitWith {};
_vehicle = _nearVehicles select 0;

if(isNil "_vehicle") exitWith {deleteVehicle _spikeStrip;};
[_vehicle] remoteExec ["OEC_fnc_spike1StripEffect",_vehicle,false];

deleteVehicle _spikeStrip;