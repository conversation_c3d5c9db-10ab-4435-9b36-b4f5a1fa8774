
//  File: fn_gangRanks.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//  Modifications: <PERSON>,<PERSON><PERSON><PERSON>
//	Description: Manage Ranks

private["_rank"];
_rank = _this param [0,0,[0]];
oev_gang_data set [2,_rank];
hint format["Your gang rank is now: %1",_rank];
player setVariable["gang_data",oev_gang_data,true];

switch(_rank) do {
	case -1: {
		if(!isNull (findDisplay 37000)) then {['yMenuCreateGang'] spawn OEC_fnc_createDialog;};
		hint "You are no longer a member of your gang.";		
		if (count oev_gang_data isEqualTo 4) then {
			[1] call OEC_fnc_gangBldgDraw;
			oev_gangShedPos = [];	
		};
		if(!isNil {(group player) getVariable "gang_id"}) then {
			[player] joinSilent (createGroup civilian);
		};
		oev_gang_data = [];
		player setVariable["gang_data",nil,true];
	};
	case 3: {systemChat "You can now invite other players to your gang.";};
	case 4: {systemChat "You can now promote, demote, withdraw funds, and kick members from within your gang.";};
	case 5: {
		hint "You are now the leader of your gang!";
		if(!isNil {(group player) getVariable "gang_id"}) then {
			player setRank "COLONEL";
			group player selectLeader player;
		};
	};
};