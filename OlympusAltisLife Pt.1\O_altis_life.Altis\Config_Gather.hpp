#define true 1
#define false 0

class CfgGather {
	class Resources {
		class apple {
			zones[] = { "apple_1", "apple_2", "apple_3", "apple_4", "grape_1", "grape_2", "grape_3" };
			zoneSize = 30;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class peach {
			zones[] = { "peaches_1", "peaches_2", "peaches_3", "peaches_4" };
			zoneSize = 30;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class heroinu {
			zones[] = { "heroin_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class cocaine {
			zones[] = { "cocaine_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class cannabis {
			zones[] = { "weed_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class copperore {
			zones[] = { "lead_1" };
			zoneSize = 30;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class ironore {
			zones[] = { "iron_1" };
			zoneSize = 30;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class salt {
			zones[] = { "salt_1" };
			zoneSize = 120;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class sand {
			zones[] = { "sand_1" };
			zoneSize = 75;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class diamond {
			zones[] = { "diamond_1" };
			zoneSize = 50;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class oilu {
			zones[] = { "oil_1" };
			zoneSize = 40;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class rock {
			zones[] = { "rock_1" };
			zoneSize = 50;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class lithium {
			zones[] = { "lithium_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class phosphorous {
			zones[] = { "phosphorous_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class ephedra {
			zones[] = { "ephedra_2" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class sugar {
			zones[] = { "sugar_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class corn {
			zones[] = { "corn_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class yeast {
			zones[] = { "yeast_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class frog {
			zones[] = { "frog_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class mushroom {
			zones[] = { "mushroom_1" };
			zoneSize = 40;
			requiredItem = "";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = true;
		};

		class platinum {
			zones[] = { "platinum_1" };
			zoneSize = 40;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};

		class silver {
			zones[] = { "silver_1" };
			zoneSize = 40;
			requiredItem = "pickaxe";
			secondaryItems[] = {};
			amount = 1;
			delay = 2.5;
			restricted = false;
		};
	};
};
