class IFLOAD_Core {
	tag = "IG";

	class IGLOADCLASS1 {
		file = "IgiLoad";
		class IgiLoad {};
		class IgiLoadInit {};
	};
};

class Life_Client_Core {
	tag = "OEC";

	class OlympusClass14 {
		file = "core";
		class briefing {};
		class fuckShit {preInit=1;};
		class initTimedMarkers {
			preInit = 1;
		};
		class initCiv {};
		class initCop {};
		class initMedic {};
		class initSurvival {};
		class intro {};
		class setupActions {};
		class setupEVH {};
		class loadingScreenSystem {};
		class loadingScreenIcon {};
		class loadingScreenContent {};
		class logIt {};
	};

	class OlympusClass120 {
		file = "core\actions";
		class arrestAction {};
		class beatdown {};
		class buyLicense {};
		class buyLotteryTicket {};
		class betMoney {};
		class captureBlackMarket {};
		class captureHideout {};
		class catchFish {};
		class catchTurtle {};
		class claimVehicle {};
		class claimIllegal {};
		class claimGangVeh {};
		class closeMap {};
		class dpFinish {};
		class dropFishingNet {};
		class escortAction {};
		class flipAction {};
		class gather {};
		class newGather {};
		class getDPMission {};
		class gutAnimal {};
		class hackAntiAir {};
		class hackRadioTower {};
		class healHospital {};
		class handleAnim {};
		class handleVehicles {};
		class handleGangVehicles {};
		class handleCopIsland {};
		class impoundAction {};
		class loadLoadout {};
		class medicInvoiceAction {};
		class packupSpikes {};
		class pickupItem {};
		class pickupMoney {};
		class postBail {};
		class processAction {};
		class pulloutAction {};
		class pulloutDeadAction {};
		class pushVehicle {};
		class putInCar {};
		class refillMags {};
		class repairTruck {};
		class rest1rainAction {};
		class robAction {};
		class robShops {};
		class saveLoadout {};
		class searchAction {};
		class searchShipWreck {};
		class searchVehAction {};
		class seizeAction {};
		class sellLicense {};
		class serviceChopper {};
		class stripGearCl {};
		class stopEscorting {};
		class storeVehicle {};
		class suicideBomb {};
		class surrender {};
		class ticketAction {};
		class unrest1rain {};
		class repairClientObject {};
		class removeKidney {};
		class conquestClient {};
		class buyDopamineCrate {};
		class dopamineCrateAction {};
		class dopeMagRefill {};
		class neuterAction {};
		class neuterActionCl {};
		class handleGlow {};
		class handleSkywriteColors {};
		class handleTitleColors {};
		class peeOnCommand {};
		class clearCapLocal {};
		class donoDance {};
		class markVehicle {};
		class syncLoadout {};
	};

	class OlympusClass12 {
		file = "core\admin";
		class adminChat {};
		class admingetID {};
		class adminGiveMoney {};
		class adminid {};
		class admininfo {};
		class adminItemComp {};
		class adminMenu {};
		class adminPlayerQuery {};
		class adminQuery {};
		class adminRestrain {};
		class adminSpawnVehicle {};
		class adminEnhancedSpec {};
		class adminTakeKeys {};
		class adminTpHere {};
		class adminTpMap {};
		class adminTpPos {};
		class adminTpTo {};
		class adminUnrestrain {};
		class adminIsland {};
		class adminLogging {};
		class adminVehComp {};
		class adminVehValidate {};
		class compActions {};
		class compCrate {};
		class adminCycleAdjust {};
	};

	class OlympusClass121 {
		file = "core\civilian";
		class civInteractionMenu {};
		class civLoadout {};
		class helpHints {};
		class newsLoadout {};
		class civOpener {};
		class civRestrain {};
		class civUnrestrain {};
		class checkCivGear {};
		class demoChargeTimer {};
		class civMarkers {};
		class newsMarkers {};
		class jaill {};
		class jailMe {};
		class removeLicenses {};
		class robInteractionMenu {};
		class robPerson {};
		class robReceive {};
		class storeVigilanteArrests {};
		class handleGoKartRace {};
		class threeToOne {};
		class trashJail {};
		class obtainPrice {};
		class payBounty {};
		class giveContraband {};
		class vigiBuddy {};
		class vigiNotify {};
		class robPainting {};
		class conqVoteMenu {};
	};

	class OlympusClass17 {
		file = "core\config";
		class blackwaterLootConfig {};
		class clothing_bruce {};
		class clothing_cop {};
		class clothing_dive {};
		class clothing_medic {};
		class clothing_parachute {};
		class clothing_reb {};
		class clothing_war {};
		class clothing_vig {};
		class clothing_news {};
		class clothingSkins {};
		class houseConfig {};
		class iconConfig {};
		class impoundPrice {};
		class itemWeight {};
		class KRON_Strings {};
		class licensePrice {};
		class licenseType {};
		class altisMapData {};
		class taxRate {};
		class varHandle {};
		class varToStr {};
		class vehicleAnimate {};
		class vehicleConfig {};
		class vehicleSkins {};
		class vehShopLicenses {};
		class virt_shops {};
		class weaponShopCfgAltis {};
	};

	class OlympusClass15 {
		file = "core\cop";
		class bountyReceive {};
		class clearBWCrate {};
		class copEnter {};
		class copInteractionMenu {};
		class searchInteractionMenu {};
		class seizeInteractionMenu {};
		class revokeInteractionMenu {};
		class revokePlayerLicense {};
		class checkCopGear {};
		class copLights {};
		class copLoadout {};
		class copMarkers {};
		class copOpener {};
		class copSearch {};
		class copTrainingMenu {};
		class copTrainSearch {};
		class copTrainSeize {};
		class copTrainRevoke {};
		class doorAnimate {};
		class handleDome {};
		class fedCamDisplay {};
		class licenseCheck {};
		class licensesRead {};
		class localPardon {};
		class payDeathFee {};
		class questionDealer {};
		class radar {};
		class repairDoor {};
		class restrain {};
		class searchClient {};
		class seizeMoneyConfirmation {};
		class seizeObjects {};
		class seizePlayerItems {};
		class sirenLights {};
		class spike1StripEffect {};
		class splitPay {};
		class ticketGive {};
		class ticketPaid {};
		class ticketPay {};
		class ticketPrompt {};
		class vehInvSearch {};
		class toggleLethals {};
		class martialLaw {};
		class poLethals {};
		class reportStolen {};
		class updateStolenVehicles {};
		class updateStolenVehicleInfo {};
		class deployVehicleTeargas {};
		class airdropClient {};
		class apdEscortClient {};
	};

	class OlympusClass51 {
		file = "core\events";
		class changePlayerStatus {};
		class eventGiveMoney {};
		class eventInventory {};
		class eventMenu {};
		class executeEventProcedure {};
		class executeOnOwner {};
		class getEventInfo {};
		class loadEventActions {};
		class loadEventTypes {};
		class updateEventLocations {};
		class updateEventPlayers {};
		class updateEventVehicles {};
		class eventMessages {};
		class roulette {};
	};

	class OlympusClass18 {
		file = "core\functions";
		class accType {};
		class actionKeyHandler {};
		class ambientAnim {};
		class ambientAnim_getParams {};
		class ambientAnim_monitor {};
		class ambientAnim_playAnim {};
		class ambientAnim_terminate {};
		class animateDoors {};
		class animSync {};
		class calWeightDiff {};
		class cameraStandby {};
		class cheatMenuFix {};
		class checkFunds {};
		class checkName {};
		class checkMap {};
		class checkRealtor {};
		class clearVehicleAmmo {};
		class clientMessage {};
		class clothingBugFix {};
		class crimeName2Number {};
		class earplugsToggle {};
		class escortTimer {};
		class clientWireTransfer {};
		class ClnearestDoor {};
		class clothingMonitor {};
		class colorAdjustments {};
		class createMarkerLocal {};
		class createVehicleLocal {};
		class ctrlCreate {};
		class cursorMarker {};
		class doorManager {};
		class dropItems {};
		class dynamicMapSystem {};
		class equipTitle {};
		class emptyFuel {};
		class escInterupt {};
		class fetchCfgDetails {};
		class fetchDeadGear {};
		class fetchStats {};
		class fetchVehInfo {};
		class fireMissile {};
		class formatColumn {};
		class garageRefund {};
		class getMags {};
		class giveDiff {};
		class handleBeatdown {};
		class handleDamage {};
		class handleDowned {};
		class handleHit {};
		class handlePlaneDelivery {};
		class handleInv {};
		class handleItem {};
		class handleVehDamage {};
		class hudSetup {};
		class hudUpdate {};
		class index {};
		class initStats {};
		class inventoryClosed {};
		class inventoryOpened {};
		class isNumeric {};
		class isRunningCheck {};
		class issueHit {};
		class isUIDActive {};
		class packMags {};
		class keyGiveV2 {};
		class keyHandler {};
		class loadDeadGear {};
		class loadGear {};
		class managePrisonWall {};
		class markerMonitor {};
		class moveIn {};
		class nearATM {};
		class nearUnits {};
		class numberText {};
		class onFired {};
		class onPutItem {};
		class onTakeItem {};
		class payPlayer {};
		class physicalInventoryWeight {};
		class planeDeliveryMarker {};
		class playerCount {};
		class playerSpotter {};
		class playerTags {};
		class processSQF {};
		class pullOutVeh {};
		class pushObject {};
		class randomRound {};
		class robInventory {};
		class receiveItem {};
		class receiveMoney {};
		class resetEVH {};
		class respawnConfirm {};
		class revealVeh {};
		class revealObjects {};
		class saveGear {};
		class setupMap {};
		class clientWarkillIncrement {};
		class sideChatMonitor {};
		class simDisable {};
		class skinUniform {};
		class skipBriefing {};
		class startingMusic {};
		class statArrUp {};
		class titleCheck {};
		class titleNotification{};
		class targetLocking {};
		class stripDownPlayer {};
		class updateStat {};
		class updateWanted {};
		class updateWantedCharge {};
		class vehicleMonitor {};
		class zoneCreator {};
		class applyBlindfold {};
		class removeBlindfold {};
		class robShopReset {};
		class textureBugTryFix {};
		class forceRagdoll {};
		class fpsFixMonitor {};
		class dispatchNotify {};
		class setDispatchData {};
		class teargas {};
		class setKidneyStatus {};
		class medicPlaceables {};
		class objectCollides {};
		class handlePlayerHeal {};
		class xmasTrees {};
		class validateGear {};
		class vigilanteCheck {};
		class interruptableAction {};
		class autoRun {};
		class serviceHeli {};
		class invPrice {};
		class factionItems {};
		class checkStartEscort {};
		class rollMenuUpdate {};
		class realCollide {};
		class nlrCircle {};
		class fixDead {};
	};

	class OlympusClass19 {
		file = "core\functions\network";
		class broadcast {};
		class corpse {};
		class jumpFnc {};
		class MP {};
		class MPexec {};
		class netSetVar {};
		class say3D {};
		class setHitPointDamage {};
		class setFuel {};
		class setTexture {};
	};

	class OlympusClass124 {
		file = "core\gangs";
		class captureTerritory {};
		class clientGangLeader {};
		class createGang {};
		class gang1Disbanded {};
		class gangNotifyMember {};
		class gang1InvitePlayer {};
		class gangCreated {};
		class gangDisband {};
		class gangInvite {};
		class gangKick {};
		class gangLeave {};
		class gangMenu {};
		class gangNewLeader {};
		class gangRanks {};
		class getOnlineMembers {};
		class initGang {};
		class territoryGarage {};
		class manageRank {};
		class populateInfo {};
		class updateGangVehicleUsers {};
	};

	class OlympusClass135 {
		file = "core\gangs\war";
		class isAtWar {};
		class warSendInv {};
		class warRecieveInv {};
		class warStart {};
		class warLoadActive {};
		class calcGearScore {};
		class warMenu {};
		class warTerminate {};
		class warEnd {};
		class warGetStats {};
		class warDeclined {};
	};

	class OlympusClass54 {
		file = "core\gangs\sheds";
		class addGangInv {};
		class addGangOil {};
		class buyGangBldg {};
		class copGangBldgMenu {};
		class gangBldgDraw {};
		class gangBldgMenu {};
		class gangBldgMembers {};
		class gangBldgOwnership {};
		class gangBldgSearch {};
		class gangSellBldg {};
		class gangSellOff {};
		class payRent {};
		class unlockInv {};
		class repairShed {};
	};

	class OlympusClass46 {
		file = "core\groups";
		class clientGroupKick {};
		class clientGroupLeader {};
		class createGroup {};
		class groupBrowser {};
		class groupManagement {};
		class groupMenu {};
		class joinGroup {};
		class kickGroup {};
		class leaveGroup {};
		class lockGroup {};
		class setGroupLeader {};
		class unlockGroup {};
		class changeGroupPassword {};
	};

	class OlympusClass13 {
		file = "core\housing";
		class availableHouse {};
		class buyHouse {};
		class ClinitHouses {};
		class ClinitHouseKeys {};
		class ClsellHouse {};
		class copBreakDoor {};
		class copHouseOwner {};
		class getBuildingPositions {};
		class houseInventory {};
		class adminHouseInv {};
		class houseMenu {};
		class houseV3Menu {};
		class houseMoveItem {};
		class houseOwnership {};
		class houseTakeItem {};
		class houseStoreItem {};
		class light1HouseAction {};
		class lightHouse {};
		class listHouse {};
		class lockHouse {};
		class lockupHouse {};
		class managePropertyKeys {};
		class manageHouseMarkers {};
		class openHouseInventory {};
		class raidHouse {};
		class upgradeProperty {};
		class ClhouseComp {};
		class calcHouseTax {};
		class payHouseTax {};
	};

	class OlympusClass127 {
		file = "core\items";
		class baitCar {};
		class blastingCharge {};
		class bloodBag {};
		class boltcutter {};
		class fireaxe {};
		class ClspikeStrip {};
		class takeoverTerminal {};
		class defuseKit {};
		class eatOrDrink {};
		class epiPen {};
		class dopeShot {};
		class firework {};
		class flashbang {};
		class gpsEnhanced {};
		class gpsJamDevice {};
		class gpsTracker {};
		class heliTowHook {};
		class itemEffects {};
		class jerryRefuel {};
		class lockpick {};
		class openPresent {};
		class pickaxeUse {};
		class speedBomb {};
		class blindfold {};
		class vehicleAmmo {};
		class slimJim {};
		class baitCarRemote {};
		class lethalInjector {};
		class refillFuelCan {};
		class pocketGoKart {};
	};

	class OlympusClass11 {
        file = "core\market";
        class openMarketView;
        class refreshMarketView;
	};

	class OlympusClass16 {
		file = "core\medical";
		class checkMedGear {};
		class checkMedVehicle {};
		class deathScreen {};
		class epiRevived {};
		class getMDMission {};
		class giveDopamine {};
		class mdFinish {};
		class medicInteractionMenu {};
		class medicInvoiceGive {};
		class medicInvoicePay {};
		class medicInvoicePrompt {};
		class medicBuddy {};
		class medicLights {};
		class medicLoadout {};
		class medicMarkers {};
		class medicRequest {};
		class requestDenial {};
		class medicSirenLights {};
		class medicQuickGive {};
		class medMarkers {};
		class onPlayerKilled {};
		class onPlayerRespawn {};
		class requestMedic {};
		class respawned {};
		class revived {};
		class revivePlayer {};
		class allowRevive {};
		class replaceKidney {};
		class medicParseRank {};
	};

	class OlympusClass123 {
		file = "core\pmenu";
		class clientGetKey {};
		class giveItem {};
		class giveMoney {};
		class initSettings {};
		class keyDrop {};
		class keyGive {};
		class newMsg {};
		class pardon {};
		class removeItem {};
		class reply {};
		class saveSettings {};
		class services {};
		class settingsOnSliderChange {};
		class showMsg {};
		class smartphone {};
		class timeUntilRestart {};
		class toggleSidechat {};
		class updateViewDistance {};
		class useItem {};
		class wanted2add {};
		class wanted3 {};
		class wantedInfo {};
		class wantedList {};
		class wantedMenu {};
		class dispatchMenu {};
		class dispatchInfo {};
		class hexIconMaster {};
	};

	class OlympusClass126 {
		file = "core\shops";
		class atmMenu {};
		class buyClothes {};
		class changeClothes {};
		class chopShopBoatMenu {};
		class chopShopBoatSelection {};
		class chopShopMenu {};
		class chopShopSelection {};
		class ClchopShopBoatSell {};
		class ClchopShopSell {};
		class ClimpoundYardImpound {};
		class clothingFilter {};
		class clothingMenu {};
		class gangAtmMenu {};
		class impoundYardMenu {};
		class impoundYardSelection {};
		class vehicleShopBuy {};
		class vehicleShopLBChange {};
		class vehicleShopMenu {};
		class virt_buy {};
		class virt_menu {};
		class virt_sell {};
		class virt_update {};
		class weaponShopBuySell {};
		class weaponShopFilter {};
		class weaponShopMenu {};
		class weaponShopSelection {};
		class loadoutMenu {};
		class depositBoxRedeemed {};
		class clRedeemDepositBox {};
		//class craftingMenu {};
		//class craftingProcMenu {};
	};

	class OlympusClass122 {
		file = "core\vehicle";
		class addVehicle2Chain {};
		class colorVehicle {};
		class deviceMine {};
		class handleGlowLights {};
		class installTracker {};
		class isUnitCopilot {};
		class isUnitGunner {};
		class lockVehicle {};
		class seizeEscort {};
		class sellEscVeh {};
		class openInventory {};
		class vehicle1Weight {};
		class vehicleOwners {};
		class vehInventory {};
		class vehStoreItem {};
		class vehTakeItem {};
		class vInteractionMenu {};
		class updateLastSeen {};
		class skinName {};
		class hasKeys {};
	};

	class OlympusClass125 {
		file = "dialog\function";
		class animateDialog {};
		class bankSafeFix {};
		class bankSafeInventory {};
		class bankSafeOpen {};
		class bankSafeStore {};
		class bankSafeTake {};
		class bankDeposit {};
		class bankTransfer {};
		class bankWithdraw {};
		class bankWarPointToggle {};
		class createDialog {};
		class displayHandler {};
		class gangDeposit {};
		class gangWithdraw {};
		class garageLBChange {};
		class vehicleTransfer {};
		class giveMenu {};
		class handleNlr {};
		class houseMarketScan {};
		class impoundMenu {};
		class modShopBuy {};
		class modShopMenu {};
		class modShopUpdate {};
		class nlrInfo {};
		class openTitleView {};
		class onKeyGiveDialog {};
		class progressBar {};
		class refreshTitleView {};
		class safeFix {};
		class safeInventory {};
		class safeOpen {};
		class safeTake {};
		class sellGarage {};
		class shedGarage {};
		class setMapPosition {};
		class setupInteractionMenu {};
		class spawnConfirm {};
		class spawnMenu {};
		class titleSelect {};
		class openHouseMarket {};
		class spawnPointCfg {};
		class spawnPointSelected {};
		class unimpound {};
		class unblockVehSpawn {};
		class updateInventoryTab {};
		class updateKeyChainTab {};
		class updateMainMenuTab {};
		class updateSettingsTab {};
		class updateStatsTab {};
		class updateVehicleSelect {};
		class vehicleGarage {};
		class pickupSelected {};
		class pickupAll {};
		class setupPickupMenu {};
		class gangBankHist {};
		class doubleClickItem {};
		class roadKit {};
		class votingBooth {};
		class hitmanContract {};
		class casinoUI {};
		class casinoRoulette {};
		class casinoSlotsCreate {};
		class casinoSlots {};
		class casinoBlackjack {};
		class upgradePlane {};
		class setCarName {};
	};

	class OlympusClass1 {
		file = "core\session";
		class ClupdatePartial {};
		class ClupdateRequest {};
		class dataQuery {};
		class insertPlayerInfo {};
		class requestReceived {};
		class syncData {};
	};

	class OlympusClass2 {
		file = "SpyGlass";
		class cmdMenuCheck{};
		class cookieJar{};
		class initSpy {};
		class menuCheck{};
		class notifyAdmins{};
		class observe{};
		class variableCheck{};
	};
};

class NPC_Core {
	tag = "NPC";

	class NPCs {
		file = "npcs";
		class processors {};
		class traders {};
		//class civCrafting {};
		class civDMV {};
		class civGas {};
		class civGun {};
		class civBlackMarket {};
		class civGarages {};
		class civRebelClothing {};
		class civRebelMarket {};
		class civRebelWeapon {};
		class civRebelWarShop {};
		class civRebelVehicles {};
		class conqShops {};
		class copMarket {};
		class copWeapon {};
		class copVehicles {};
		class copDummy {};
		class medAssistance {};
		class medDesk {};
		class medMarket {};
		class medVehicles {};
	};
};
