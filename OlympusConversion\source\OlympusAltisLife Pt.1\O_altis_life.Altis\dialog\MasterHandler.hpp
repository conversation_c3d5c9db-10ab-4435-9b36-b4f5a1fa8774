/*
	Master UI Resource File
*/

//Pre-requisite includes, these need to be included before the rest as other classes inherit these
#include "common.hpp"
#include "common_EditorWrapper.hpp"
#include "yMenuBase.hpp"
#include "ui.hpp"
#include "blankDialog.hpp"

//Shops
#include "vehicleShop.hpp"
#include "modShop.hpp"
#include "shopGear.hpp"
#include "shopItems.hpp"
#include "shopClothing.hpp"
#include "chopShop.hpp"

//Other stuff
#include "adminComp.hpp"
#include "adminMenu.hpp"
#include "altisBank.hpp"
#include "bank.hpp"
#include "customWanted.hpp"
#include "deathScreen.hpp"
#include "eventMenu.hpp"
#include "federalReserve.hpp"
#include "gbank.hpp"
#include "hitmanContract.hpp"
#include "houseMarketLocation.hpp"
#include "impound_yard.hpp"
#include "pInteraction.hpp"
#include "houseMenu.hpp"
#include "trunkWithPhysical.hpp"
#include "spawnSelection.hpp"
#include "ticket.hpp"
#include "ticketTrain.hpp"
#include "trunk.hpp"
#include "vehicleGarage.hpp"
#include "depositBox.hpp"
#include "propertyKeys.hpp"
#include "pickup_menu.hpp"
#include "gangBldgsMenu.hpp"
#include "gangBankHistory.hpp"
#include "medic_roadKit.hpp"
#include "votingBooth.hpp"
#include "houseInteraction.hpp"
#include "betMoney.hpp"
#include "buyLotteryTickets.hpp"
#include "newHomeMenu.hpp"
#include "loadouts.hpp"
#include "escMenu.hpp"
#include "conquestVote.hpp"
#include "setCustomCarName.hpp"

//----- Casino -----
#include "casinoRouletteUI.hpp"
#include "casinoSlotsUI.hpp"
#include "casinoBlackjackUI.hpp"

//----- Y-menus -----
#include "yMenuMain.hpp"
#include "yMenuInventory.hpp"
#include "yMenuStats.hpp"
#include "yMenuKeyChain.hpp"
#include "yMenuPhone.hpp"
#include "yMenuSettings.hpp"
#include "yMenuGroups.hpp"
#include "yMenuGangs.hpp"
#include "yMenuMarket.hpp"
#include "yMenuWanted.hpp"
#include "yMenuTitles.hpp"
#include "yMenuAdmin.hpp"
#include "yMenuDispatch.hpp"
#include "yMenuStolenVehicles.hpp"
#include "yMenuIcons.hpp"
