
	//Allowed Targets:
		//0: can target all machines (default)
		//1: can target only clients, execution on server is denied
		//2: can target only server, execution on clients is denied

	//JIP:
		//0: JIP flag cannot be set
		//1: JIP flag can be set (default)

	//Modes:
		//0: remote execution is blocked
		//1: only whitelisted functions/commands are allowed for remote execution
		//2: remote execution fully opened

	//Whitelist Debug: class BIS_fnc_debugConsoleExec {allowedTargets = 0;};


class Commands {
	mode = 0;
	jip = 0;
};

class Functions {
	mode = 1;
	jip = 0;

	class BIS_fnc_debugConsoleExec {allowedTargets = 0;};
	class OEC_fnc_broadcast {allowedTargets = 0;};
	class OEC_fnc_clientGangLeader {allowedTargets = 0;};
	class OEC_fnc_obtainPrice {allowedTargets = 0;};
	class OEC_fnc_clientMessage {allowedTargets = 0;};
	class OEC_fnc_createMarkerLocal {allowedTargets = 0;};
	class OEC_fnc_colorVehicle {allowedTargets = 0;};
	class OEC_fnc_copSearch {allowedTargets = 0;};
	class OEC_fnc_executeOnOwner {allowedTargets = 0;};
	class OEC_fnc_houseOwnership {allowedTargets = 0;};
	class OEC_fnc_gangBldgOwnership {allowedTargets = 0;};
	class OEC_fnc_gangNotifyMember {allowedTargets = 0;};
	class OEC_fnc_impoundMenu {allowedTargets = 0;};
	class OEC_fnc_lightHouse {allowedTargets = 0;};
	class OEC_fnc_populateInfo {allowedTargets = 0;};
	class OEC_fnc_restrain {allowedTargets = 0;};
	class OEC_fnc_closeMap {allowedTargets = 0;};
	class OEC_fnc_setFuel {allowedTargets = 0;};
	class OEC_fnc_setHitPointDamage {allowedTargets = 0;};
	class OEC_fnc_ticketPaid {allowedTargets = 0;};
	class OEC_fnc_updateWanted {allowedTargets = 0;};
	class OEC_fnc_vehicleAnimate {allowedTargets = 0;};
	class OEC_fnc_dataQuery {allowedTargets = 0;};
	class OEC_fnc_insertPlayerInfo {allowedTargets = 0;};
	class OEC_fnc_lockVehicle {allowedTargets = 0;};
	class OEC_fnc_bountyReceive {allowedTargets = 0;};
	class OEC_fnc_robShopReset {allowedTargets = 0;};
	class OEC_fnc_handleHit {allowedTargets = 0;};
	class OEC_fnc_reportStolen {allowedTargets = 0;};

	class OES_fnc_gangBHistory {allowedTargets = 0;};

	class OEC_fnc_medicBuddy {allowedTargets = 1;};
	class OEC_fnc_vigiBuddy {allowedTargets = 1;};
	class OEC_fnc_addVehicle2Chain {allowedTargets = 1;};
	class OEC_fnc_adminid {allowedTargets = 1;};
	class OEC_fnc_vigilanteCheck {allowedTargets = 1;};
	class OEC_fnc_admininfo {allowedTargets = 1;};
	class OEC_fnc_adminPlayerQuery {allowedTargets = 1;};
	class OEC_fnc_animSync {allowedTargets = 1;};
	class OEC_fnc_clientGetKey {allowedTargets = 1;};
	class OEC_fnc_clientGroupKick {allowedTargets = 1;};
	class OEC_fnc_clientGroupLeader {allowedTargets = 1;};
	class OEC_fnc_clientWireTransfer {allowedTargets = 1;};
	class OEC_fnc_checkFunds {allowedTargets = 1;};
	class OEC_fnc_clientWarkillIncrement {allowedTargets = 1;};
	class OEC_fnc_copLights {allowedTargets = 1;};
	class OEC_fnc_handleGlowLights {allowedTargets = 1;};
	class OEC_fnc_warDeclined {allowedTargets = 1;};
	class OEC_fnc_copMedicRequest {allowedTargets = 1;};
	class OEC_fnc_corpse {allowedTargets = 1;};
	class OEC_fnc_demoChargeTimer {allowedTargets = 1;};
	class OEC_fnc_flashbang {allowedTargets = 1;};
	class OEC_fnc_gangBldgMembers {allowedTargets = 1;};
	class OEC_fnc_gang1Disbanded {allowedTargets = 1;};
	class OEC_fnc_gangCreated {allowedTargets = 1;};
	class OEC_fnc_gangInvite {allowedTargets = 1;};
	class OEC_fnc_gangRanks {allowedTargets = 1;};
	class OEC_fnc_garageRefund {allowedTargets = 1;};
	class OEC_fnc_giveDiff {allowedTargets = 1;};
	class OEC_fnc_handleBeatdown {allowedTargets = 1;};
	class OEC_fnc_heliTowHook {allowedTargets = 1;};
	class OEC_fnc_jaill {allowedTargets = 1;};
	class OEC_fnc_jailMe {allowedTargets = 1;};
	class OEC_fnc_jumpFnc {allowedTargets = 1;};
	class OEC_fnc_licensesRead {allowedTargets = 1;};
	class OEC_fnc_licenseCheck {allowedTargets = 1;};
	class OEC_fnc_medicInvoicePrompt {allowedTargets = 1;};
	class OEC_fnc_medicLights {allowedTargets = 1;};
	class OEC_fnc_medicRequest {allowedTargets = 1;};
	class OEC_fnc_requestDenial {allowedTargets = 1;};
	class OEC_fnc_moveIn {allowedTargets = 1;};
	class OEC_fnc_netSetVar {allowedTargets = 1;};
	class OEC_fnc_payPlayer {allowedTargets = 1;};
	class OEC_fnc_pullOutVeh {allowedTargets = 1;};
	class OEC_fnc_receiveItem {allowedTargets = 1;};
	class OEC_fnc_receiveMoney {allowedTargets = 1;};
	class OEC_fnc_removeLicenses {allowedTargets = 1;};
	class OEC_fnc_revived {allowedTargets = 1;};
	class OEC_fnc_robPerson {allowedTargets = 1;};
	class OEC_fnc_robInventory {allowedTargets = 1;};
	class OEC_fnc_hudUpdate {allowedTargets = 1;};
	class OEC_fnc_statArrUp {allowedTargets = 1;};
	class OEC_fnc_stripGearCl {allowedTargets = 1;};
	class OEC_fnc_robReceive {allowedTargets = 1;};
	class OEC_fnc_say3D {allowedTargets = 1;};
	class OEC_fnc_searchClient {allowedTargets = 1;};
	class OEC_fnc_seizePlayerItems {allowedTargets = 1;};
	class OEC_fnc_setTexture {allowedTargets = 1;};
	class OEC_fnc_smartphone {allowedTargets = 1;};
	class OEC_fnc_spike1StripEffect {allowedTargets = 1;};
	class OEC_fnc_stripDownPlayer {allowedTargets = 1;};
	class OEC_fnc_ticketPrompt {allowedTargets = 1;};
	class OEC_fnc_updateStat {allowedTargets = 1;};
	class OEC_fnc_wantedList {allowedTargets = 1;};
	class OEC_fnc_requestReceived {allowedTargets = 1;};
	class OEC_fnc_notifyAdmins {allowedTargets = 1;};
	class OEC_fnc_epiRevived {allowedTargets = 1;};
	class OEC_fnc_replaceKidney {allowedTargets = 1;};
	class OEC_fnc_setKidneyStatus {allowedTargets = 1;};
	class OEC_fnc_ClupdatePartial {allowedTargets = 1;};
	class OEC_fnc_blindfold {allowedTargets = 1;};
	class OEC_fnc_applyBlindfold {allowedTargets = 1;};
	class OEC_fnc_removeBlindfold {allowedTargets = 1;};
	class OEC_fnc_revokePlayerLicense {allowedTargets = 1;};
	class OEC_fnc_setDispatchData {allowedTargets = 1;};
	class OEC_fnc_teargas {allowedTargets = 1;};
	class OEC_fnc_roulette {allowedTargets = 1;};
	class OEC_fnc_conquestClient {allowedTargets = 1;};
	class OEC_fnc_dopamineCrateAction {allowedTargets = 1;};
	class OEC_fnc_warLoadActive {allowedTargets = 1;};
	class OEC_fnc_warRecieveInv {allowedTargets = 1;};
	class OEC_fnc_warStart {allowedTargets = 1;};
	class OEC_fnc_warEnd {allowedTargets = 1;};
	class OEC_fnc_votingBooth {allowedTargets = 1;};
	class OEC_fnc_compActions {allowedTargets = 1;};
	class OEC_fnc_betMoney {allowedTargets = 1;};
	class OEC_fnc_managePropertyKeys {allowedTargets = 1;};
	class OEC_fnc_manageHouseMarkers {allowedTargets = 1;};
	class OEC_fnc_peeOnCommand {allowedTargets = 1;};
	class OEC_fnc_donoDance {allowedTargets = 1;};
	class OEC_fnc_revealVeh {allowedTargets = 1;};
	class OEC_fnc_installTracker {allowedTargets = 1;};
	class OEC_fnc_neuterAction {allowedTargets = 1;};
	class OEC_fnc_robPainting {allowedTargets = 1;};
	class OEC_fnc_cookieJar {allowedTargets = 2;};
	class OEC_fnc_observe {allowedTargets = 2;};
	class OES_fnc_sellEscort {allowedTargets = 2;};
	class OES_fnc_startEscort {allowedTargets = 2;};
	class OES_fnc_handleBombTimer {allowedTargets = 2;};
	class OES_fnc_pickupHandler {allowedTargets = 2;};
	class OES_fnc_createServVeh {allowedTargets = 2;};
	class OES_fnc_statTableUp {allowedTargets = 2;};
	class OES_fnc_updateTitle {allowedTargets = 2;};
	class OES_fnc_droppedItemCleanupHandler {allowedTargets = 2;};
	class OES_fnc_checkVehicleLimit {allowedTargets = 2;};
	class OES_fnc_checkGangVehicleLimit {allowedTargets = 2;};
	class OES_fnc_repairObject {allowedTargets = 2;};
	class OES_fnc_SpyGlassResponse {allowedTargets = 2;};
	class OES_fnc_manageCycle {allowedTargets = 2;};
	class OES_fnc_changeWeather {allowedTargets = 2;};
	class OES_fnc_insertRequest {allowedTargets = 2;};
	class OES_fnc_insertVehicle {allowedTargets = 2;};
	class OES_fnc_newPlayerVeh {allowedTargets = 2;};
	class OES_fnc_queryRequest {allowedTargets = 2;};
	class OES_fnc_updatePartial {allowedTargets = 2;};
	class HC_fnc_updatePartial {allowedTargets = 2;};
	class OES_fnc_updateRequest {allowedTargets = 2;};
	class HC_fnc_updateRequest {allowedTargets = 2;};
	class OES_fnc_owner2Player {allowedTargets = 2;};
	class OES_fnc_bulkAdd {allowedTargets = 2;};
	class OES_fnc_jipRequestTimer {allowedTargets = 2;};
	class OES_fnc_jailSys {allowedTargets = 2;};
	class OES_fnc_wantedAdd {allowedTargets = 2;};
	class OES_fnc_wantedBounty {allowedTargets = 2;};
	class OES_fnc_wantedFetch {allowedTargets = 2;};
	class OES_fnc_wantedPardon {allowedTargets = 2;};
	class OES_fnc_updateVehOwnership {allowedTargets = 2;};
	class OES_fnc_wantedRemove {allowedTargets = 2;};
	class OES_fnc_wantedRemoveCharge {allowedTargets = 2;};
	class OES_fnc_handleLottery {allowedTargets = 2;};
	class OES_fnc_runLottery {allowedTargets = 2;};
	class OES_fnc_rentPay {allowedTargets = 2;};
	class OES_fnc_lethalPay {allowedTargets = 2;};
	class OES_fnc_addHouse {allowedTargets = 2;};
	class OES_fnc_houseForSale {allowedTargets = 2;};
	class OES_fnc_addGangBldg {allowedTargets = 2;};
	class OES_fnc_updateGangOil {allowedTargets = 2;};
	class OES_fnc_adminInvis {allowedTargets = 2;};
	class OES_fnc_chopShopSell {allowedTargets = 2;};
	class OES_fnc_copSeizeVeh {allowedTargets = 2;};
	class OES_fnc_createItem {allowedTargets = 2;};
	class OES_fnc_createAdminVeh {allowedTargets = 2;};
	class OES_fnc_enableVehicleSling {allowedTargets = 2;};
	class OES_fnc_executeEventAction {allowedTargets = 2;};
	class OES_fnc_gangBank {allowedTargets = 2;};
	class OES_fnc_gangClaim {allowedTargets = 2;};
	class OES_fnc_getGangInfo {allowedTargets = 2;};
	class OES_fnc_getID {allowedTargets = 2;};
	class OES_fnc_getVehicles {allowedTargets = 2;};
	class OES_fnc_handleMessages {allowedTargets = 2;};
	class OES_fnc_handleAntiAir {allowedTargets = 2;};
	class OES_fnc_seizePlayerItemsCiv {allowedTargets = 2;};
	class OES_fnc_setGetHit {allowedTargets = 2;};
	class OES_fnc_insertGang {allowedTargets = 2;};
	class OES_fnc_jailCombatLogger {allowedTargets = 2;};
	class OES_fnc_keyManagement {allowedTargets = 2;};
	class OES_fnc_handleTerror {allowedTargets = 2;};
	class OES_fnc_handleLoadouts {allowedTargets = 2;};
	class OES_fnc_handleDisc {allowedTargets = 2;};
	class OES_fnc_logIt {allowedTargets = 2;};
	class HC_fnc_logIt {allowedTargets = 2;};
	class OES_fnc_managesc {allowedTargets = 2;};
	class OES_fnc_updateGangBldg {allowedTargets = 2;};
	class OES_fnc_spawnDeletedAmmoOnLoad {allowedTargets = 2;};
	class OES_fnc_msgRequest {allowedTargets = 2;};
	class OES_fnc_propertyUpdateKeys {allowedTargets = 2;};
	class OES_fnc_payload {allowedTargets = 2;};
	class OES_fnc_removeGang {allowedTargets = 2;};
	class OES_fnc_sellHouse {allowedTargets = 2;};
	class OES_fnc_realtorCash {allowedTargets = 2;};
	class OES_fnc_setObjVar {allowedTargets = 2;};
	class OES_fnc_pulloutDead {allowedTargets = 2;};
	class OES_fnc_simSerDisable {allowedTargets = 2;};
	class OES_fnc_spaw1nVehicle {allowedTargets = 2;};
	class OES_fnc_spawnEventCrates {allowedTargets = 2;};
	class OES_fnc_spawnEventObjects {allowedTargets = 2;};
	class OES_fnc_spawnEventVehicles {allowedTargets = 2;};
	class OES_fnc_spikeStrip {allowedTargets = 2;};
	class OES_fnc_updateGangTrunk {allowedTargets = 2;};
	class OES_fnc_sellGangBldg {allowedTargets = 2;};
	class OES_fnc_updateHouseTrunk {allowedTargets = 2;};
	class OES_fnc_updateMember {allowedTargets = 2;};
	class OES_fnc_updateProperty {allowedTargets = 2;};
	class OES_fnc_updateVehicleMods {allowedTargets = 2;};
	class OES_fnc_vehicleDelete {allowedTargets = 2;};
	class OES_fnc_vehicleStore {allowedTargets = 2;};
	class OES_fnc_spawnBlackwaterLoot {allowedTargets = 2;};
	class OES_fnc_internetCheck {allowedTargets = 2;};
	class OES_fnc_updateMarker {allowedTargets = 2;};
	class OES_fnc_handleComplexMarker {allowedTargets = 2;};
	class OES_fnc_rouletteServer {allowedTargets = 2;};
	class OES_fnc_adminInsertVeh {allowedTargets = 2;};
	class OES_fnc_spawnDopamineCrate {allowedTargets = 2;};
	class OES_fnc_spawnMedicPlaceable {allowedTargets = 2;};
	class OES_fnc_warInsertGang {allowedTargets = 2;};
	class OES_fnc_warRemoveGang {allowedTargets = 2;};
	class OES_fnc_warGetEnemy {allowedTargets = 2;};
	class OES_fnc_warAwardPts {allowedTargets = 2;};
	class OES_fnc_zoneKillPts {allowedTargets = 2;};
	class OES_fnc_copZoneKillPts {allowedTargets = 2;};
	class OES_fnc_declareMartial {allowedTargets = 2;};
	class OES_fnc_votingBoothServer {allowedTargets = 2;};
	class OES_fnc_illegalClaim {allowedTargets = 2;};
	class OES_fnc_warGetData {allowedTargets = 2;};
	class OES_fnc_marketCache {allowedTargets = 2;};
	class OES_fnc_warGetSetPts {allowedTargets = 2;};
	class OES_fnc_vigiGetSetArrests {allowedTargets = 2;};
	class OES_fnc_adminCreateComp {allowedTargets =2;};
	class OES_fnc_jipRequestVar {allowedTargets = 2;};
	class OES_fnc_deletedVehStore {allowedTargets = 2;};
	class OES_fnc_conquestServer {allowedTargets = 2;};
	class OES_fnc_clearCap {allowedTargets = 2;};
	class OES_fnc_redeemDepositBox {allowedTargets = 2;};
	class OES_fnc_hexMasterServ {allowedTargets = 2;};
	class OES_fnc_airdropServer {allowedTargets = 2;};
	class OES_fnc_eventPlayers {allowedTargets = 2;};
	class OES_fnc_hqTakeover {allowedTargets = 2;};
	class OES_fnc_conquestVoteServ {allowedTargets = 2;};
	class OES_fnc_bankDeaths {allowedTargets = 2;};
	class OES_fnc_artGallery {allowedTargets = 2;};
	class OES_fnc_updateCarName {allowedTargets = 2;};
	class OES_fnc_houseComp {allowedTargets = 2;};
	class OES_fnc_handleDrugSellers {allowedTargets = 2;};
	class OES_fnc_updateHouseDeed {allowedTargets = 2;};
	class OES_fnc_apdEscortServer {allowedTargets = 2;};
};
