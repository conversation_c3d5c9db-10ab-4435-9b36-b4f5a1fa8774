//------------------------------------------------------------------------------------------------
//! Configuration Manager
//! Manages server configuration settings converted from Olympus configuration.sqf
//! Handles economy settings, item configurations, and server parameters
//------------------------------------------------------------------------------------------------

class ConfigurationManager
{
	private static ref ConfigurationManager s_pInstance;
	
	// Economy settings (converted from Olympus oev_ variables)
	protected int m_iPaycheckAmount = 450;
	protected int m_iTotalCrimes = 75;
	protected float m_fEconomyMultiplier = 1.0;
	protected int m_iMaxBankBalance = *********;
	protected int m_iMaxCashAmount = *********;
	
	// Server settings
	protected int m_iMaxPlayers = 60;
	protected bool m_bAntiCheatEnabled = true;
	protected int m_iAutoSaveInterval = 30;
	protected bool m_bDebugMode = false;
	
	// Item configurations
	protected ref array<string> m_aIllegalItems = {};
	protected ref array<string> m_aTaserWeapons = {};
	protected ref array<string> m_aPoliceWeapons = {};
	protected ref array<string> m_aMedicalItems = {};
	
	// Vehicle configurations
	protected ref array<string> m_aCivilianVehicles = {};
	protected ref array<string> m_aPoliceVehicles = {};
	protected ref array<string> m_aMedicalVehicles = {};
	
	// License configurations
	protected ref map<int, ref LicenseConfig> m_mLicenseConfigs = new map<int, ref LicenseConfig>();
	
	// Job configurations
	protected ref map<string, ref JobConfig> m_mJobConfigs = new map<string, ref JobConfig>();
	
	// Zone configurations
	protected ref array<ref ZoneConfig> m_aZoneConfigs = {};
	
	//------------------------------------------------------------------------------------------------
	//! Get singleton instance
	static ConfigurationManager GetInstance()
	{
		if (!s_pInstance)
			s_pInstance = new ConfigurationManager();
		return s_pInstance;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Constructor
	void ConfigurationManager()
	{
		LoadDefaultConfiguration();
		LoadConfigurationFromFile();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Load default configuration values
	protected void LoadDefaultConfiguration()
	{
		Print("[ConfigurationManager] Loading default configuration...");
		
		// Initialize illegal items (converted from Olympus configuration)
		m_aIllegalItems = {
			"optic_tws", "optic_tws_mg", "optic_nightstalker", "optic_thermal",
			"srifle_dmr_02_camo_f", "srifle_dmr_02_f", "srifle_dmr_02_sniper_f",
			"srifle_dmr_03_f", "srifle_dmr_03_khaki_f", "srifle_dmr_03_tan_f",
			"srifle_dmr_03_multicam_f", "srifle_dmr_03_woodland_f"
		};
		
		// Initialize taser weapons
		m_aTaserWeapons = {
			"hgun_p07_snds_f", "hgun_pistol_heavy_01_f", "hgun_pistol_heavy_02_f"
		};
		
		// Initialize police weapons
		m_aPoliceWeapons = {
			"arifle_sdar_f", "arifle_mx_f", "arifle_mxc_f", "arifle_mx_sw_f",
			"smg_02_f", "hgun_p07_f", "hgun_pistol_heavy_01_f"
		};
		
		// Initialize medical items
		m_aMedicalItems = {
			"medikit", "firstaidkit", "bloodbag_o_pos", "bloodbag_a_pos",
			"bloodbag_b_pos", "bloodbag_ab_pos", "defibrillator"
		};
		
		// Initialize vehicle arrays
		InitializeVehicleConfigurations();
		
		// Initialize license configurations
		InitializeLicenseConfigurations();
		
		// Initialize job configurations
		InitializeJobConfigurations();
		
		// Initialize zone configurations
		InitializeZoneConfigurations();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize vehicle configurations
	protected void InitializeVehicleConfigurations()
	{
		// Civilian vehicles
		m_aCivilianVehicles = {
			"C_Offroad_01_F", "C_Hatchback_01_F", "C_Hatchback_01_sport_F",
			"C_SUV_01_F", "C_Van_01_transport_F", "C_Van_01_box_F",
			"C_Truck_02_transport_F", "C_Truck_02_covered_F"
		};
		
		// Police vehicles
		m_aPoliceVehicles = {
			"B_MRAP_01_F", "C_Offroad_01_F", "B_Heli_Light_01_F",
			"I_MRAP_03_F", "B_APC_Wheeled_01_cannon_F"
		};
		
		// Medical vehicles
		m_aMedicalVehicles = {
			"C_Van_01_box_F", "B_Heli_Light_01_stripped_F", "C_Offroad_01_F"
		};
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize license configurations
	protected void InitializeLicenseConfigurations()
	{
		// Driver's license
		ref LicenseConfig driverLicense = new LicenseConfig();
		driverLicense.m_iId = 1;
		driverLicense.m_sName = "Driver's License";
		driverLicense.m_sCategory = "civilian";
		driverLicense.m_iCost = 500;
		driverLicense.m_bRequiresTest = true;
		m_mLicenseConfigs.Set(1, driverLicense);
		
		// Pilot license
		ref LicenseConfig pilotLicense = new LicenseConfig();
		pilotLicense.m_iId = 2;
		pilotLicense.m_sName = "Pilot License";
		pilotLicense.m_sCategory = "civilian";
		pilotLicense.m_iCost = 25000;
		pilotLicense.m_bRequiresTest = true;
		m_mLicenseConfigs.Set(2, pilotLicense);
		
		// Weapon license
		ref LicenseConfig weaponLicense = new LicenseConfig();
		weaponLicense.m_iId = 3;
		weaponLicense.m_sName = "Weapon License";
		weaponLicense.m_sCategory = "civilian";
		weaponLicense.m_iCost = 10000;
		weaponLicense.m_bRequiresTest = false;
		m_mLicenseConfigs.Set(3, weaponLicense);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize job configurations
	protected void InitializeJobConfigurations()
	{
		// Apple picking job
		ref JobConfig appleJob = new JobConfig();
		appleJob.m_sId = "apple_picking";
		appleJob.m_sName = "Apple Picking";
		appleJob.m_sCategory = "gathering";
		appleJob.m_iBaseReward = 65;
		appleJob.m_fGatherTime = 3.0;
		appleJob.m_bRequiresLicense = false;
		m_mJobConfigs.Set("apple_picking", appleJob);
		
		// Peach picking job
		ref JobConfig peachJob = new JobConfig();
		peachJob.m_sId = "peach_picking";
		peachJob.m_sName = "Peach Picking";
		peachJob.m_sCategory = "gathering";
		peachJob.m_iBaseReward = 75;
		peachJob.m_fGatherTime = 3.5;
		peachJob.m_bRequiresLicense = false;
		m_mJobConfigs.Set("peach_picking", peachJob);
		
		// Iron mining job
		ref JobConfig ironJob = new JobConfig();
		ironJob.m_sId = "iron_mining";
		ironJob.m_sName = "Iron Mining";
		ironJob.m_sCategory = "mining";
		ironJob.m_iBaseReward = 120;
		ironJob.m_fGatherTime = 5.0;
		ironJob.m_bRequiresLicense = true;
		ironJob.m_iRequiredLicenseId = 4; // Mining license
		m_mJobConfigs.Set("iron_mining", ironJob);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize zone configurations
	protected void InitializeZoneConfigurations()
	{
		// Drug processing zone
		ref ZoneConfig drugZone = new ZoneConfig();
		drugZone.m_sName = "Drug Processing";
		drugZone.m_eType = ZoneType.ILLEGAL_PROCESSING;
		drugZone.m_vPosition = Vector(16019.5, 17.5963, 12722.1);
		drugZone.m_fRadius = 150.0;
		drugZone.m_bPoliceAlert = true;
		m_aZoneConfigs.Insert(drugZone);
		
		// Police station safe zone
		ref ZoneConfig policeZone = new ZoneConfig();
		policeZone.m_sName = "Police Station";
		policeZone.m_eType = ZoneType.SAFE_ZONE;
		policeZone.m_vPosition = Vector(3020.99, 5.3203, 3031.04);
		policeZone.m_fRadius = 100.0;
		policeZone.m_bPoliceAlert = false;
		m_aZoneConfigs.Insert(policeZone);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Load configuration from file
	protected void LoadConfigurationFromFile()
	{
		// TODO: Implement file-based configuration loading
		Print("[ConfigurationManager] File-based configuration loading not yet implemented");
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get paycheck amount
	int GetPaycheckAmount()
	{
		return m_iPaycheckAmount;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get economy multiplier
	float GetEconomyMultiplier()
	{
		return m_fEconomyMultiplier;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if item is illegal
	bool IsItemIllegal(string itemClass)
	{
		return m_aIllegalItems.Find(itemClass) != -1;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if weapon is taser
	bool IsTaserWeapon(string weaponClass)
	{
		return m_aTaserWeapons.Find(weaponClass) != -1;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get license configuration
	LicenseConfig GetLicenseConfig(int licenseId)
	{
		return m_mLicenseConfigs.Get(licenseId);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get job configuration
	JobConfig GetJobConfig(string jobId)
	{
		return m_mJobConfigs.Get(jobId);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get all zones of specific type
	array<ref ZoneConfig> GetZonesByType(ZoneType type)
	{
		ref array<ref ZoneConfig> zones = {};
		
		foreach (ref ZoneConfig zone : m_aZoneConfigs)
		{
			if (zone.m_eType == type)
				zones.Insert(zone);
		}
		
		return zones;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if position is in zone
	ZoneConfig GetZoneAtPosition(vector position)
	{
		foreach (ref ZoneConfig zone : m_aZoneConfigs)
		{
			float distance = vector.Distance(position, zone.m_vPosition);
			if (distance <= zone.m_fRadius)
				return zone;
		}
		
		return null;
	}
}

//------------------------------------------------------------------------------------------------
//! License configuration structure
class LicenseConfig
{
	int m_iId;
	string m_sName;
	string m_sCategory;
	int m_iCost;
	bool m_bRequiresTest;
	array<int> m_aPrerequisites = {};
}

//------------------------------------------------------------------------------------------------
//! Job configuration structure
class JobConfig
{
	string m_sId;
	string m_sName;
	string m_sCategory;
	int m_iBaseReward;
	float m_fGatherTime;
	bool m_bRequiresLicense;
	int m_iRequiredLicenseId;
	string m_sRequiredItem = "";
}

//------------------------------------------------------------------------------------------------
//! Zone configuration structure
class ZoneConfig
{
	string m_sName;
	ZoneType m_eType;
	vector m_vPosition;
	float m_fRadius;
	bool m_bPoliceAlert;
}

//------------------------------------------------------------------------------------------------
//! Zone type enumeration
enum ZoneType
{
	SAFE_ZONE = 0,
	ILLEGAL_PROCESSING = 1,
	JOB_LOCATION = 2,
	GANG_TERRITORY = 3,
	POLICE_STATION = 4,
	HOSPITAL = 5
}
