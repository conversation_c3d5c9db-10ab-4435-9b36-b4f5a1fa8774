//Ranks - <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Donor
class CfgSkins {
  class VehicleSkins {
    //Littlebird
    class B_Heli_Light_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\apd_hummingbird.jpg"};
        special[] = {""};
      };
      class AHP {
        name = "AHP";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\AHP_Hummingbird.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,3,0,0};
        path[] = {"images\rr_m900.jpg"};
        special[] = {""};
      };
      class Black {
      	name = "Black";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\heli_light_01_ext_ion_co.paa"};
      	special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\heli_light_01_ext_blue_co.paa"};
      	special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\heli_light_01_ext_co.paa"};
      	special[] = {""};
      };
      class Blueline {
      	name = "Blueline";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_blueline_co.paa"};
      	special[] = {""};
      };
      class Furious {
      	name = "Furious";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_furious_co.paa"};
      	special[] = {""};
      };
      class JeansBlue {
      	name = "Jeans Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_jeans_co.paa"};
      	special[] = {""};
      };
      class SpeedyRedline {
      	name = "Speedy Redline";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_speedy_co.paa"};
      	special[] = {""};
      };
      class Waves {
      	name = "Waves";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_wave_co.paa"};
      	special[] = {""};
      };
      class Elliptical {
      	name = "Elliptical";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_elliptical_co.paa"};
      	special[] = {""};
      };
      class GrayWatcher {
      	name = "Gray Watcher";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_graywatcher_co.paa"};
      	special[] = {""};
      };
      class DigiGreen {
      	name = "Digi Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\heli_light_01_ext_indp_co.paa"};
      	special[] = {""};
      };
      class RebelDigital {
      	name = "Rebel Digital";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_digital_co.paa"};
      	special[] = {""};
      };
      class Light {
      	name = "Light";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_light_co.paa"};
      	special[] = {""};
      };
      class Shadow {
      	name = "Shadow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_shadow_co.paa"};
      	special[] = {""};
      };
      class Sheriff {
      	name = "Sheriff";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_sheriff_co.paa"};
      	special[] = {""};
      };
      class Sunset {
      	name = "Sunset";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_sunset_co.paa"};
      	special[] = {""};
      };
      class Vrana {
      	name = "Vrana";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_vrana_co.paa"};
      	special[] = {""};
      };
      class Wasp {
      	name = "Wasp";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_01\Data\Skins\heli_light_01_ext_wasp_co.paa"};
      	special[] = {""};
      };
      class America {
        name = "America";
        faction[] = {"civ"};
        rank[] = {0,0,0,50};
        path[] = {"images\civ_heli_donor_1.jpg"};
        special[] = {"tex"};
      };
      class Vanguard {
        name = "Vanguard";
        faction[] = {"civ"};
        rank[] = {0,0,0,100};
        path[] = {"images\civ_heli_donor_2.jpg"};
        special[] = {"tex"};
      };
      class EliteForcesGangHummingbird {
        name = "Elite Force's Gang Hummingbird";
        faction[] = {"civ"};
        rank[] = {0,0,0,99999};
        path[] = {"images\gang_vehicle_30840_0.jpg"};
        special[] = {30840};
      };
      class BrightYellow {
      	name = "Bright Yellow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(1,1,0,1)"};
      	special[] = {""};
      };
      class Pink {
      	name = "Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(1,0.2,1,1)"};
      	special[] = {""};
      };
      class NeonPink {
      	name = "Neon Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(1,0,0.7,1)"};
      	special[] = {""};
      };
      class Purple {
      	name = "Purple";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(0.2,0,1,1)"};
      	special[] = {""};
      };
      class LimeGreen {
      	name = "Lime Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(0,1,0,1)"};
      	special[] = {""};
      };
      class DarkGreen {
      	name = "Dark Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(0,0.2,0,1)"};
      	special[] = {""};
      };
      class NeonBlue {
      	name = "Neon Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(0,0,1,1)"};
      	special[] = {""};
      };
      class LightBlue {
      	name = "Light Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(0,0.1,0.3,1)"};
      	special[] = {""};
      };
      class NeonRed {
      	name = "Neon Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(1,0,0,1)"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,9999};
      	path[] = {"#(rgb,128,128,3)color(0.8,0.15,0,1)"};
      	special[] = {""};
      };
      class News {
      	name = "News";
      	faction[] = {"med"};
      	rank[] = {0,0,1,0};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
    };
    //M-900
    class C_Heli_Light_01_civil_F : B_Heli_Light_01_F {};
    //Orca
    class O_Heli_Light_02_unarmed_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\APD_Orca.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,4,0,0};
        path[] = {"images\RR_Orca.jpg"};
        special[] = {""};
      };
      class RescueDonor {
        name = "Rescue Donor";
        faction[] = {"med"};
        rank[] = {0,4,0,100};
        path[] = {"images\RR_Donor_Orca.jpg"};
        special[] = {"tex"};
      };
      class Black {
      	name = "Black";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_02\Data\heli_light_02_ext_co.paa"};
      	special[] = {""};
      };
      class WhiteBlue {
      	name = "White / Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_02\Data\heli_light_02_ext_civilian_co.paa"};
      	special[] = {""};
      };
      class DigiGreen {
      	name = "Digi Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_02\Data\heli_light_02_ext_indp_co.paa"};
      	special[] = {""};
      };
      class DesertDigi {
      	name = "Desert Digi";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f\Heli_Light_02\Data\heli_light_02_ext_opfor_co.paa"};
      	special[] = {""};
      };
      class Elite {
      	name = "Elite";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,100};
      	path[] = {"images\civ_orca_donor_2.jpg"};
      	special[] = {"tex"};
      };
      class EgyptianGreenGold {
      	name = "Egyptian Green Gold";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,250};
      	path[] = {"images\donor_orca_champ.jpg"};
      	special[] = {"tex"};
      };
      class SkylineOrca {
      	name = "Skyline Orca";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,500};
      	path[] = {"images\civ_orca_donor_r1.jpg"};
      	special[] = {"tex"};
      };
      class DisconfiguredGold {
      	name = "Disconfigured Gold";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,50};
      	path[] = {"images\donor_orca_vip.jpg"};
      	special[] = {"tex"};
      };
      class Bape {
      	name = "Bape";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_orca_1.jpg"};
      	special[] = {""};
      };
      class APDVandal {
      	name = "APD Vandal";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_orca_vandal.jpg"};
      	special[] = {""};
      };
      class Redgull {
      	name = "Redgull";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,50};
      	path[] = {"images\civ_orca_donor_1.jpg"};
      	special[] = {"tex"};
      };
      class VigilanteOrca {
      	name = "Vigilante Orca";
      	faction[] = {"civ"};
      	rank[] = {0,0,4,0};
      	path[] = {"images\vig_orca_red.jpg"};
      	special[] = {"vigi"};
      };
      class OrcaOrca {
      	name = "Orca Orca";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_light_02\data\heli_light_02_ext_opfor_v2_co.paa"};
      	special[] = {""};
      };
      class Asiimov {
      	name = "Asiimov";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,100};
      	path[] = {"images\civ_orca_donor_3.jpg"};
      	special[] = {"tex"};
      };
      class NobleGangOrca {
      	name = "Noble Gang Orca";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,999999};
      	path[] = {"images\gang_vehicle_22887_0.jpg"};
      	special[] = {22887};
      };
    };
    //GHawk
    class B_Heli_Transport_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {6,0,0,0};
        path[] = {"images\apd_ghosthawk_0.jpg","images\apd_ghosthawk_1.jpg"};
        special[] = {""};
      };
      class PoliceDonor {
        name = "Ghost Hawk (Donor)";
        faction[] = {"cop"};
        rank[] = {6,0,0,250};
        path[] = {"images\APD_Ghosthawkdonor_0.jpg","images\APD_Ghosthawkdonor_1.jpg"};
        special[] = {"tex"};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,5,0,0};
        path[] = {"images\RR_Ghosthawk_0.jpg","images\RR_Ghosthawk_1.jpg"};
        special[] = {""};
      };
      class Tropical {
      	name = "Tropical";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_exp\heli_transport_01\data\heli_transport_01_ext01_tropic_co.paa","\a3\air_f_exp\heli_transport_01\data\heli_transport_01_ext02_tropic_co.paa"};
      	special[] = {""};
      };
      class Vanguard {
      	name = "Vanguard";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,100};
      	path[] = {"images\civ_ghosthawk_donor_1.jpg","images\civ_ghosthawk_donor_1a.jpg"};
      	special[] = {"tex"};
      };
      class DazzleCamo {
      	name = "Dazzle Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_exp\heli_transport_01\data\heli_transport_01_ext01_tropic_co.paa","\a3\air_f_exp\heli_transport_01\data\heli_transport_01_ext02_tropic_co.paa"};
      	special[] = {""};
      };
      class SandCamo {
      	name = "Sand Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_exp\heli_transport_01\data\heli_transport_01_ext01_sand_co.paa","\a3\air_f_exp\heli_transport_01\data\heli_transport_01_ext02_sand_co.paa"};
      	special[] = {""};
      };
      class Green {
      	name = "Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_beta\heli_transport_01\data\heli_transport_01_ext01_blufor_co.paa","\a3\air_f_beta\heli_transport_01\data\heli_transport_01_ext02_blufor_co.paa"};
      	special[] = {""};
      };
    };
    class B_Heli_Transport_01_camo_F : B_Heli_Transport_01_F {};
    //Huron
    class B_Heli_Transport_03_F {
      class Police {
      	name = "Police";
      	faction[] = {"cop"};
      	rank[] = {7,0,0,0};
      	path[] = {"images\apd_huron.jpg","images\apd_huron1.jpg"};
      	special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,6,0,0};
        path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
        special[] = {""};
      };
      class Admin {
      	name = "Admin";
      	faction[] = {"civ"};
      	rank[] = {0,0,5,0};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
    };
    class B_Heli_Transport_03_unarmed_F : B_Heli_Transport_03_F{};
    class B_Heli_Transport_03_unarmed_green_F : B_Heli_Transport_03_F{};
    //Hellcat
    class I_Heli_light_03_unarmed_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {5,0,0,0};
        path[] = {"images\cop_hellcat.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,7,0,0};
        path[] = {"images\rr_hellcat.jpg"};
        special[] = {""};
      };
      class Green {
      	name = "Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_epb\Heli_Light_03\Data\heli_light_03_base_co.paa"};
      	special[] = {""};
      };
      class EAF {
      	name = "EAF";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"a3\air_f_enoch\heli_light_03\data\heli_light_03_base_eaf_co.paa"};
      	special[] = {""};
      };
    };
    class I_Heli_light_03_dynamicLoadout_F : I_Heli_light_03_unarmed_F {};
    //Mohawk
    class I_Heli_Transport_02_F {
      class Ion {
      	name = "Ion";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_beta\Heli_Transport_02\Data\Skins\heli_transport_02_1_ion_co.paa","\a3\air_f_beta\Heli_Transport_02\Data\Skins\heli_transport_02_2_ion_co.paa","\a3\air_f_beta\Heli_Transport_02\Data\Skins\heli_transport_02_3_ion_co.paa"};
      	special[] = {""};
      };
      class Dahoman {
      	name = "Dahoman";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_beta\Heli_Transport_02\Data\Skins\heli_transport_02_1_dahoman_co.paa","\a3\air_f_beta\Heli_Transport_02\Data\Skins\heli_transport_02_2_dahoman_co.paa","\a3\air_f_beta\Heli_Transport_02\Data\Skins\heli_transport_02_3_dahoman_co.paa"};
      	special[] = {""};
      };
      class Camo {
      	name = "Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_beta\Heli_Transport_02\Data\heli_transport_02_1_indp_co.paa","\a3\air_f_beta\Heli_Transport_02\Data\heli_transport_02_2_indp_co.paa","\a3\air_f_beta\Heli_Transport_02\Data\heli_transport_02_3_indp_co.paa"};
      	special[] = {""};
      };
    };
    //Taru
    class O_Heli_Transport_04_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {0,0,0,0};
        path[] = {"images\APD_Taru_0.jpg","images\APD_Taru_1.jpg","images\APD_Taru_2.jpg","images\APD_Taru_3.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,4,0,0};
        path[] = {"images\RR_Taru_0.jpg","images\RR_Taru_1.jpg"};
        special[] = {""};
      };
      class Black {
        name = "Black";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_black_co.paa"};
        special[] = {""};
      };
      class Camo {
      	name = "Hex Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_co.paa"};
      	special[] = {""};
      };
    };
    class O_Heli_Transport_04_Covered_F : O_Heli_Transport_04_F {
      class Black {
        name = "Black";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_pod_ext01_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_pod_ext02_black_co.paa"};
        special[] = {""};
      };
      class Camo {
        name = "Hex Camo";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_pod_ext01_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_pod_ext02_co.paa"};
      	special[] = {""};
      };
    };
    class O_Heli_Transport_04_Fuel_F : O_Heli_Transport_04_F {
      class Black {
        name = "Black";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_fuel_black_co.paa"};
        special[] = {""};
      };
      class Camo {
        name = "Hex Camo";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_fuel_black_co.paa"};
        special[] = {""};
      };
    };
    class O_Heli_Transport_04_Bench_F : O_Heli_Transport_04_F {
      class Black {
        name = "Black";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_black_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_bench_black_co.paa"};
        special[] = {""};
      };
      class Camo {
        name = "Hex Camo";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_01_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_base_02_co.paa","\a3\air_f_heli\heli_transport_04\data\heli_transport_04_bench_co.paa"};
        special[] = {""};
      };
    };
    class O_Heli_Transport_04_repair_F : O_Heli_Transport_04_F {};
    //Hatchbacks
    class C_Hatchback_01_sport_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {2,0,0,0};
        path[] = {"images\apd_sport.jpg"};
        special[] = {""};
      };
      class DonorPolice {
        name = "Donor Police";
        faction[] = {"cop"};
        rank[] = {2,0,0,100};
        path[] = {"images\apd_donor_sport.jpg"};
        special[] = {"tex"};
      };
      class AHP {
        name = "AHP";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\AHP_Hatch.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,1,0,0};
        path[] = {"images\rr_hatchback_1.jpg"};
        special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_sport01_co.paa"};
      	special[] = {""};
      };
      class DarkBlue {
      	name = "Dark Blue";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_sport02_co.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_sport03_co.paa"};
      	special[] = {""};
      };
      class BlackWhite {
      	name = "Black / White";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_sport04_co.paa"};
      	special[] = {""};
      };
      class Tan {
      	name = "Tan";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_sport05_co.paa"};
      	special[] = {""};
      };
      class Green {
      	name = "Green";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_sport06_co.paa"};
      	special[] = {""};
      };
      class Static {
        name = "Static";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,99999};
        path[] = {"#(ai,2048,2048,1)perlinNoise(768,1024,0,0.4)"};
        special[] = {"76561198071078342"};
      };
      class Vigilante {
      	name = "Vigilante";
      	faction[] = {"civ"};
      	rank[] = {0,0,4,0};
      	path[] = {"images\vig_hatch_1.jpg"};
      	special[] = {"vigi"};
      };
      class Supreme {
        name = "Supreme";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\civ_hatch_green_light.jpg"};
        special[] = {""};
      };
      class LightningMcQueen {
        name = "Lightning McQueen";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\civ_hatch_green.jpg"};
        special[] = {""};
      };
      class MonsterGreen {
        name = "Monster Green";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\civ_hatchback_sport.jpg"};
        special[] = {""};
      };
      class AltisRacer {
      	name = "Altis Racer";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_hatch_racer.jpg"};
      	special[] = {""};
      };
      class VIP {
      	name = "VIP";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,50};
      	path[] = {"images\donor_sporty.jpg"};
      	special[] = {"tex"};
      };
      class AmongUs {
      	name = "Among Us";
      	faction[] = {"cop","civ"};
      	rank[] = {3,0,0,30};
      	path[] = {"images\civ_hatch_among_us.jpg"};
      	special[] = {"tex"};
      };
      class APDVandal {
      	name = "APD Vandal";
      	faction[] = {"cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\reb_hatch_1.jpg"};
      	special[] = {""};
      };
      class Supporter {
      	name = "Supporter";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,15};
      	path[] = {"images\civ_hatch_donor_1.jpg"};
      	special[] = {"tex"};
      };
      class Legendary {
      	name = "Legendary";
      	faction[] = {"civ","cop"};
      	rank[] = {4,0,0,500};
      	path[] = {"images\civ_hatch_donor_3.jpg"};
      	special[] = {"tex"};
      };
      class MoneySwag {
        name = "Money Swag";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,250};
        path[] = {"images\civ_hatch_money.jpg"};
        special[] = {"76561198054270248","76561198047686034","76561198068036588","76561198147555014","76561198072533044","76561198087266544","76561198065089996","76561198129316227","76561198088305085","76561197986272184","76561198096379966","76561198059779976","76561198017538858","76561198021218502","76561197995204703","76561198120580476","76561198037167869","76561198090390036","76561198001531368","76561198147943978","76561198129317158","76561198077813631","76561198024574328","76561198030254739","76561198071078342","tex"};
      };
      class CivilianCouncil {
      	name = "Civilian Council";
      	faction[] = {"civ"};
      	rank[] = {0,0,4,0};
      	path[] = {"images\civ_rep_hatch.jpg"};
      	special[] = {"civCou"};
      };
      class NeonRider {
      	name = "Neon Rider";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_hatch_neon.jpg"};
      	special[] = {""};
      };
      class DinglesHatchback {
      	name = "Dingle's Hatchback";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,999999};
      	path[] = {"#(rgb,128,128,3)color(0.8,0.2,0,1)"};
      	special[] = {111};
      };
      class TimelessHatchback {
        name = "Combat Log Mobile";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,999999};
      	path[] = {"images\gang_vehicle_31745_0.jpg"};
      	special[] = {31745};
      };
    };
    class C_Hatchback_01_F : C_Hatchback_01_sport_F {
      class Beige {
      	name = "Beige";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base01_co.paa"};
      	special[] = {""};
      };
      class GreenC {
      	name = "Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base02_co.paa"};
      	special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base03_co.paa"};
      	special[] = {""};
      };
      class DarkBlueC {
      	name = "Dark Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base04_co.paa"};
      	special[] = {""};
      };
      class Brown {
      	name = "Brown";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base04_co.paa"};
      	special[] = {""};
      };
      class Yellow {
      	name = "Yellow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base06_co.paa"};
      	special[] = {""};
      };
      class White {
      	name = "White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base07_co.paa"};
      	special[] = {""};
      };
      class Grey {
      	name = "Grey";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base08_co.paa"};
      	special[] = {""};
      };
      class Black {
      	name = "Black";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Hatchback_01\data\hatchback_01_ext_base09_co.paa"};
      	special[] = {""};
      };
      class BrightYellow {
      	name = "Bright Yellow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,10000};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class Pink {
      	name = "Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,10000};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class NeonPink {
      	name = "Neon Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,10000};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class Purple {
      	name = "Purple";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,10000};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class RedC {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,10000};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class OrangeC {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,10000};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
    };
    //Offroads
    class C_Offroad_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {1,0,0,0};
        path[] = {"images\apd_offroad.jpg","images\apd_offroad.jpg"};
        special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\Offroad_01\Data\offroad_01_ext_co.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Yellow {
      	name = "Yellow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\Offroad_01\Data\offroad_01_ext_BASE01_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class White {
      	name = "White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\Offroad_01\Data\offroad_01_ext_BASE02_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\Offroad_01\Data\offroad_01_ext_BASE03_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class DarkRed {
      	name = "Dark Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\Offroad_01\Data\offroad_01_ext_BASE04_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class BlueWhite {
      	name = "Blue / White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\Offroad_01\Data\offroad_01_ext_BASE05_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Black {
        name = "Black";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
        special[] = {""};
      };
      class BrightYellow {
      	name = "Bright Yellow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,1,0,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Pink {
      	name = "Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0.2,1,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class NeonPink {
      	name = "Neon Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0,0.7,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Purple {
      	name = "Purple";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.2,0,1,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class LimeGreen {
      	name = "Lime Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,1,0,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class DarkGreen {
      	name = "Dark Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,0.2,0,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class NeonBlue {
      	name = "Neon Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,0,1,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class LightBlue {
      	name = "Light Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,0.1,0.3,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class NeonRed {
      	name = "Neon Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0,0,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.8,0.15,0,1)","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class EAF {
      	name = "EAF";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_EAF_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_EAF_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_eaf_co.paa"};
      	special[] = {""};
      };
      class TwoTone {
      	name = "Two Tone";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_offroad_1.jpg","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_blk_CO.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
      class Green {
        name = "Green";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_grn_co.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_grn_co.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_grn_co.paa"};
        special[] = {""};
      };
      class Ranger {
        name = "Ranger";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_ranger_co.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_ext_ranger_co.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_ranger_co.paa"};
        special[] = {""};
      };
      class IDAP {
      	name = "IDAP";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_orange\offroad_01\data\offroad_01_ext_idap_co.paa","\a3\soft_f_orange\offroad_01\data\offroad_01_ext_idap_co.paa","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
      	special[] = {""};
      };
    };
    class B_G_Offroad_01_F : C_Offroad_01_F {};
    class C_Offroad_01_covered_F : C_Offroad_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {2,0,0,0};
        path[] = {"images\apd_offroad.jpg","images\apd_offroad.jpg","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
        special[] = {""};
      };
    };
    class B_G_Offroad_01_armed_F : C_Offroad_01_F {
      class WoodlandGuerrilla {
      	name = "Woodland Guerrilla";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"A3\soft_f_bootcamp\Offroad_01\data\offroad_01_ext_IG_01_CO.paa"};
      	special[] = {""};
      };
      class DesertGuerrilla {
      	name = "Desert Guerrilla";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"A3\soft_f_bootcamp\Offroad_01\data\offroad_01_ext_IG_06_CO.paa"};
      	special[] = {""};
      };
    };
    class I_G_Offroad_01_armed_F : B_G_Offroad_01_armed_F {
      class APDEscort {
        name = "Police";
        faction[] = {"civ"};
        rank[] = {0,0,0,10000};
        path[] = {"images\apd_offroad.jpg","images\apd_offroad.jpg"};
        special[] = {""};
      };
    };
    class I_G_Offroad_01_AT_F : B_G_Offroad_01_armed_F {};
    class C_Offroad_01_repair_F {
      class Rescue {
      	name = "Rescue";
      	faction[] = {"med"};
      	rank[] = {0,2,0,0};
      	path[] = {"images\rr_offroad.jpg"};
      	special[] = {""};
      };
      class CamelTowing {
      	name = "Camel Towing";
      	faction[] = {"med"};
      	rank[] = {0,2,0,50};
      	path[] = {"images\rr_offroad_camel.jpg"};
      	special[] = {"tex"};
      };
    };
    class C_Offroad_01_comms_F {
      class Police {
        name = "Police Comms";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\apd_offroad.jpg","images\apd_offroad.jpg","\a3\Soft_F_Enoch\Offroad_01\Data\offroad_01_cover_blk_co.paa"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,3,0,0};
        path[] = {"images\rr_offroad.jpg","images\rr_offroad.jpg","images\rr_offroad_2a.jpg"};
        special[] = {""};
      };
    };
    //Jeeps
    class C_Offroad_02_unarmed_F {
      class White {
      	name = "White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_white_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_white_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_int_white_co.paa"};
      	special[] = {""};
      };
      class Black {
      	name = "Black";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_black_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_black_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_int_black_co.paa"};
      	special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_blue_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_blue_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_int_blue_co.paa"};
      	special[] = {""};
      };
      class Green {
      	name = "Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_green_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_green_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_int_green_co.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_orange_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_orange_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_int_orange_co.paa"};
      	special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_red_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_ext_red_co.paa","\A3\Soft_F_Exp\Offroad_02\Data\offroad_02_int_red_co.paa"};
      	special[] = {""};
      };
    };
    class I_C_Offroad_02_LMG_F : C_Offroad_02_unarmed_F {};
    //Quad
    class B_Quadbike_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {1,0,0,0};
        path[] = {"\A3\Soft_F_beta\Quadbike_01\Data\quadbike_01_civ_black_co.paa"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,2,0,0};
        path[] = {"images\rr_quad_0.jpg"};
        special[] = {""};
      };
      class Brown {
      	name = "Brown";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F\Quadbike_01\Data\Quadbike_01_co.paa"};
      	special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_beta\Quadbike_01\Data\quadbike_01_civ_blue_co.paa"};
      	special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_beta\Quadbike_01\Data\quadbike_01_civ_red_co.paa"};
      	special[] = {""};
      };
      class White {
      	name = "White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_beta\Quadbike_01\Data\quadbike_01_civ_white_co.paa"};
      	special[] = {""};
      };
      class DigiGreen {
      	name = "Digi Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F_beta\Quadbike_01\Data\quadbike_01_indp_co.paa"};
      	special[] = {""};
      };
      class HunterCamo {
      	name = "Hunter Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Quadbike_01\data\quadbike_01_indp_hunter_co.paa"};
      	special[] = {""};
      };
      class DigiDesert {
      	name = "Digi Desert";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\Soft_F\Quadbike_01\Data\quadbike_01_opfor_co.paa"};
      	special[] = {""};
      };
      class SolidBlack {
      	name = "Solid Black";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class BrightYellow {
      	name = "Bright Yellow";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,1,0,1)"};
      	special[] = {""};
      };
      class Pink {
      	name = "Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0.2,1,1)"};
      	special[] = {""};
      };
      class NeonPink {
      	name = "Neon Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0,0.7,1)"};
      	special[] = {""};
      };
      class Purple {
      	name = "Purple";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.2,0,1,1)"};
      	special[] = {""};
      };
      class LimeGreen {
      	name = "Lime Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,1,0,1)"};
      	special[] = {""};
      };
      class DarkGreen {
      	name = "Dark Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,0.2,0,1)"};
      	special[] = {""};
      };
      class NeonBlue {
      	name = "Neon Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,0,1,1)"};
      	special[] = {""};
      };
      class LightBlue {
      	name = "Light Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0,0.1,0.3,1)"};
      	special[] = {""};
      };
      class NeonRed {
      	name = "Neon Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0,0,1)"};
      	special[] = {""};
      };
      class SolidRed {
      	name = "Solid Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.4,0,0,1)"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.8,0.15,0,1)"};
      	special[] = {""};
      };
      class PacificCamo {
      	name = "Pacific Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_exp\quadbike_01\data\quadbike_01_ghex_co.paa"};
      	special[] = {""};
      };
    };
    //Hemmts
    class B_Truck_01_transport_F {
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,3,0,0};
        path[] = {"images\rr_hemtt_base_0.jpg","images\rr_hemtt_base_1.jpg","images\rr_hemtt_base_2.jpg"};
        special[] = {""};
      };
      class Green {
      	name = "Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"a3\soft_f_exp\truck_01\data\truck_01_ext_01_olive_co.paa","a3\soft_f_exp\truck_01\data\truck_01_ext_02_olive_co.paa","a3\soft_f_exp\truck_01\data\truck_01_cargo_olive_co.paa"};
      	special[] = {""};
      };
      class Sand {
      	name = "Sand";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"a3\soft_f_beta\truck_01\data\truck_01_ext_01_co.paa","a3\soft_f_beta\truck_01\data\truck_01_ext_02_co.paa","a3\soft_f_beta\truck_01\data\truck_01_cargo_co.paa"};
      	special[] = {""};
      };
      class Night {
      	name = "Night";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_hemtt_bcsat_0.jpg","images\civ_hemtt_bcsat_1.jpg","images\civ_hemtt_bcsat_1.jpg"};
      	special[] = {""};
      };
    };
    class B_Truck_01_fuel_F {
      class Green {
      	name = "Green";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"a3\soft_f_exp\truck_01\data\truck_01_ext_01_olive_co.paa","a3\soft_f_exp\truck_01\data\truck_01_ext_02_olive_co.paa","a3\soft_f_exp\truck_01\data\truck_01_fuel_olive_co.paa"};
      	special[] = {""};
      };
      class Sand {
      	name = "Sand";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"a3\soft_f_beta\truck_01\data\truck_01_ext_01_co.paa","a3\soft_f_beta\truck_01\data\truck_01_ext_02_co.paa","a3\soft_f_gamma\truck_01\data\truck_01_fuel_co.paa"};
      	special[] = {""};
      };
      class Night {
      	name = "Night";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_hemtt_bcsat_0.jpg","images\civ_hemtt_bcsat_1.jpg","a3\soft_f_exp\truck_01\data\truck_01_fuel_olive_co.paa"};
      	special[] = {""};
      };
    };
    class B_Truck_01_flatbed_F {
      class Rescue {
      	name = "Rescue";
      	faction[] = {"med"};
      	rank[] = {0,3,0,0};
      	path[] = {"images\rr_hemtt_base_0.jpg","images\rr_hemtt_base_1.jpg","images\rr_hemtt_base_2.jpg"};
      	special[] = {""};
      };
    };
    //Tempests
    class O_Truck_03_device_F {
      class Camo {
      	name = "Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_epc\truck_03\data\truck_03_ext01_co.paa","\a3\soft_f_epc\truck_03\data\truck_03_ext02_co.paa"};
      	special[] = {""};
      };
      class PacificCamo {
      	name = "Pacific Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_exp\truck_03\data\truck_03_ext01_ghex_co.paa","\a3\soft_f_exp\truck_03\data\truck_03_ext02_ghex_co.paa"};
      	special[] = {""};
      };
      class USArmy {
      	name = "U.S. Army";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_tempest_army_0.jpg","images\civ_tempest_army_1.jpg","images\civ_tempest_army_2.jpg","images\civ_tempest_army_3.jpg"};
      	special[] = {""};
      };
    };
    class O_Truck_03_covered_F : O_Truck_03_device_F {
      class PacificCamo {
        name = "Pacific Camo";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,99999};
        path[] = {""};
        special[] = {""};
      };
    };
    class O_Truck_03_fuel_F : O_Truck_03_device_F {
      class PacificCamo {
        name = "Pacific Camo";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,99999};
        path[] = {""};
        special[] = {""};
      };
      class USArmy {
        name = "U.S. Army";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\civ_tempest_army_0.jpg","images\civ_tempest_army_1.jpg","a3\soft_f_exp\Truck_03\Data\Truck_03_fuel_ghex_CO.paa"};
        special[] = {""};
      };
    };
    class O_Truck_03_transport_F : O_Truck_03_device_F {
      class PacificCamo {
        name = "Pacific Camo";
        faction[] = {"civ","cop"};
        rank[] = {3,0,0,99999};
        path[] = {""};
        special[] = {""};
      };
    };
    //Zamaks
    class I_Truck_02_F {
      class GreenDigi {
      	name = "Green Digi";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_indp_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_fuel_indp_co.paa"};
      	special[] = {""};
      };
      class DesertDigi {
      	name = "Desert Digi";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_opfor_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_fuel_opfor_co.paa"};
      	special[] = {""};
      };
    };
    class I_Truck_02_covered_F : I_Truck_02_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {7,0,0,0};
        path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
        special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_blue_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_co.paa"};
      	special[] = {""};
      };
      class BlueOlive {
      	name = "Blue Olive";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_blue_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_olive_co.paa"};
      	special[] = {""};
      };
      class OrangeBlue {
      	name = "Orange Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_co.paa"};
      	special[] = {""};
      };
      class OrangeOlive {
      	name = "Orange Olive";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_olive_co.paa"};
      	special[] = {""};
      };
      class GreenDigi {
        name = "Green Digi";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_indp_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_indp_co.paa"};
        special[] = {""};
      };
      class DesertDigi {
        name = "Desert Digi";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_opfor_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_opfor_co.paa"};
        special[] = {""};
      };
    };
    class I_Truck_02_transport_F : I_Truck_02_F {
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_blue_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_co.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_co.paa"};
      	special[] = {""};
      };
      class GreenDigi {
        name = "Green Digi";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_indp_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_indp_co.paa"};
        special[] = {""};
      };
      class DesertDigi {
        name = "Desert Digi";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_opfor_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_kuz_opfor_co.paa"};
        special[] = {""};
      };
    };
    class I_Truck_02_fuel_F : I_Truck_02_F {
      class Blue {
      	name = "Blue";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_blue_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_fuel_co.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_fuel_co.paa"};
      	special[] = {""};
      };
      class GreenDigi {
        name = "Green Digi";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_fuel_indp_co.paa"};
        special[] = {""};
      };
      class DesertDigi {
        name = "Desert Digi";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\soft_f_beta\truck_02\data\truck_02_kab_co.paa","\a3\soft_f_beta\truck_02\data\truck_02_fuel_opfor_co.paa"};
        special[] = {""};
      };
    };
    //Vans
    class C_Van_02_transport_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\APD_Van.jpg"};
        special[] = {""};
      };
      class White {
      	name = "White";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_white_CO.paa"};
      	special[] = {""};
      };
      class Blue {
      	name = "Blue";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_blue_CO.paa"};
      	special[] = {""};
      };
      class Green {
      	name = "Green";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_green_CO.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_orange_CO.paa"};
      	special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_red_CO.paa"};
      	special[] = {""};
      };
      class Brown {
      	name = "Brown";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_Syndikat_CO.paa"};
      	special[] = {""};
      };
      class DaltMining {
      	name = "Dalt Mining";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_Daltgreen_CO.paa"};
      	special[] = {""};
      };
      class BluePearl {
      	name = "Blue Pearl";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_BluePearl_CO.paa"};
      	special[] = {""};
      };
      class BattleBus {
      	name = "Battle Bus";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_BB_CO.paa"};
      	special[] = {""};
      };
      class FuelRacing {
      	name = "Fuel Racing";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_Fuel_CO.paa"};
      	special[] = {""};
      };
      class VranaRacing {
      	name = "Vrana Racing";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_vrana_CO.paa"};
      	special[] = {""};
      };
      class SwifDelivery {
      	name = "Swif Delivery";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_armazon_CO.paa"};
      	special[] = {""};
      };
      class MaskedCamo {
      	name = "Masked Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_Masked_CO.paa"};
      	special[] = {""};
      };
      class Camo {
      	name = "Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_FIA_01_CO.paa"};
      	special[] = {""};
      };
      class UnfinishedCamo {
      	name = "Unfinished Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_orange\van_02\data\van_body_FIA_02_unfinished_CO.paa"};
      	special[] = {""};
      };
    };
    class C_Van_02_vehicle_F : C_Van_02_transport_F {
      class Pedo {
      	name = "Pedo";
      	faction[] = {"civ"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_van_1.jpg"};
      	special[] = {""};
      };
      class AltisNewsNetwork {
      	name = "Altis News Network";
      	faction[] = {"civ"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_van_2.jpg"};
      	special[] = {""};
      };
    };
    class B_G_Van_02_vehicle_F : C_Van_02_vehicle_F {};
    class B_G_Van_02_transport_F : C_Van_02_vehicle_F {};
    class C_Van_02_medevac_F {
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,1,0,0};
        path[] = {"images\RR_Van.jpg"};
        special[] = {""};
      };
    };
    //Box trucks
    class C_Van_01_transport_F {
      class White {
      	name = "White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_co.paa"};
      	special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_red_co.paa"};
      	special[] = {""};
      };
      class Graffiti {
      	name = "Graffiti";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_ig_co.paa"};
      	special[] = {""};
      };
      class Redgull {
      	name = "Redgull";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_box_redgull_0.jpg","images\civ_box_redgull_1.jpg"};
      	special[] = {""};
      };
    };
    class C_Van_01_box_F {
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,0,1,0};
        path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_red_co.paa","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
        special[] = {""};
      };
      class White {
        name = "White";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_co.paa","\a3\soft_f_gamma\Van_01\Data\van_01_adds_co.paa"};
        special[] = {""};
      };
      class Red {
        name = "Red";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_red_co.paa","\a3\soft_f_gamma\Van_01\Data\van_01_adds_co.paa"};
        special[] = {""};
      };
      class Graffiti {
        name = "Graffiti";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_ig_co.paa","\a3\soft_f_gamma\Van_01\Data\van_01_adds_ig_co.paa"};
        special[] = {""};
      };
      class News {
      	name = "News";
      	faction[] = {"med"};
      	rank[] = {0,0,1,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_red_co.paa","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
      class BeerTruck {
      	name = "Beer Truck";
      	faction[] = {"civ"};
      	rank[] = {0,0,5,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_red_co.paa","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {"76561198128016491"};
      };
      class IceCreamTruck {
      	name = "Ice Cream Truck";
      	faction[] = {"civ"};
      	rank[] = {0,0,5,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_co.paa","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {"76561198037151720","76561198069862784"};
      };
      class Redgull {
        name = "Redgull";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
      	path[] = {"images\civ_box_redgull_0.jpg","images\civ_box_redgull_1.jpg"};
        special[] = {""};
      };
    };
    class C_Van_01_fuel_F {
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_red_co.paa","\a3\soft_f_gamma\Van_01\Data\van_01_tank_red_co.paa"};
      	special[] = {""};
      };
      class White {
      	name = "White";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_gamma\Van_01\Data\van_01_ext_co.paa","\a3\soft_f_gamma\Van_01\Data\van_01_tank_co.paa"};
      	special[] = {""};
      };
      class Graffiti {
      	name = "Graffiti";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_bootcamp\van_01\data\van_01_ext_ig_03_co.paa","\a3\soft_f_bootcamp\van_01\data\van_01_tank_ig_03_co.paa"};
      	special[] = {""};
      };
    };
    //Hunter
    class B_MRAP_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {4,0,0,0};
        path[] = {"images\apd_hunter_0.jpg","images\apd_hunter_1.jpg"};
        special[] = {""};
      };
      class PoliceDonor {
        name = "State Police";
        faction[] = {"cop"};
        rank[] = {4,0,0,250};
        path[] = {"images\apd_hunterdonor_0.jpg","images\apd_hunterdonor_1.jpg"};
        special[] = {"tex"};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,5,0,0};
        path[] = {"images\RR_Hunter_0.jpg","images\RR_Hunter_1.jpg"};
        special[] = {""};
      };
      class Khaki {
      	name = "Khaki";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f\mrap_01\data\mrap_01_base_co.paa","\a3\soft_f\mrap_01\data\mrap_01_adds_co.paa"};
      	special[] = {""};
      };
      class Pink {
      	name = "Pink";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(1,0.2,1,1)"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.8,0.15,0,1)"};
      	special[] = {""};
      };
      class Red {
      	name = "Red";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.4,0,0,1)"};
      	special[] = {""};
      };
      class AltisRebellion {
        name = "Altis Rebellion";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"images\civ_hunter_rebel_0.jpg","images\civ_hunter_rebel_1.jpg"};
        special[] = {""};
      };
      class BatmanHunter {
        name = "Batman Hunter";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"images\civ_hunter_0.jpg","images\civ_hunter_1.jpg"};
        special[] = {""};
      };
      class GraveDigger {
        name = "Grave Digger";
        faction[] = {"civ"};
        rank[] = {0,0,0,100};
        path[] = {"images\civ_hunter_donor_1.jpg","images\civ_hunter_donor_2.jpg"};
        special[] = {"tex"};
      };
    };
    //Ifrit
    class O_MRAP_02_F {
      class Camo {
      	name = "Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\A3\soft_F\MRAP_02\Data\mrap_02_ext_01_co.paa","\A3\soft_F\MRAP_02\Data\mrap_02_ext_02_co.paa"};
      	special[] = {""};
      };
      class PacificCamo {
      	name = "Pacific Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_exp\mrap_02\data\mrap_02_ext_01_ghex_co.paa","\a3\soft_f_exp\mrap_02\data\mrap_02_ext_02_ghex_co.paa"};
      	special[] = {""};
      };
      class Galaxy {
      	name = "Galaxy";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_galaxy_ifrit_0.jpg","images\civ_galaxy_ifrit_1.jpg"};
      	special[] = {""};
      };
      class AltisRebellion {
      	name = "Altis Rebellion";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_ifrit_rebel_0.jpg","images\civ_ifrit_rebel_1.jpg"};
      	special[] = {""};
      };
      class LouisVuitton {
      	name = "Louis Vuitton";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_ifrit_rotate_1.jpg","images\civ_ifrit_rotate_2.jpg"};
      	special[] = {""};
      };
      class Venom {
        name = "Venom";
        faction[] = {"civ"};
        rank[] = {0,0,0,50};
        path[] = {"images\civ_ifrit_venom_0.jpg","images\civ_ifrit_venom_1.jpg"};
        special[] = {"tex"};
      };
      class McOlympus {
        name = "McOlympus";
        faction[] = {"civ"};
        rank[] = {0,0,0,50};
        path[] = {"images\donor_ifrit_vip1.jpg","images\donor_ifrit_vip2.jpg"};
        special[] = {"tex"};
      };
      class Mario {
        name = "Mario";
        faction[] = {"civ"};
        rank[] = {0,0,0,50};
        path[] = {"images\donor_ifrit_mario1.jpg","images\donor_ifrit_mario2.jpg"};
        special[] = {"tex"};
      };
      class CherryBlossom {
      	name = "Cherry Blossom";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,50};
      	path[] = {"images\civ_ifrit_cherry_0.jpg","images\civ_ifrit_cherry_1.jpg"};
      	special[] = {""};
      };
      class Monster {
        name = "Monster";
        faction[] = {"civ"};
        rank[] = {0,0,0,100};
        path[] = {"images\civ_ifrit_monster1.jpg","images\civ_ifrit_monster2.jpg"};
        special[] = {""};
      };
      class Circuit {
        name = "Circuit";
        faction[] = {"civ"};
        rank[] = {0,0,0,100};
        path[] = {"images\civ_ifrit_donor_bonus_2.jpg","images\civ_ifrit_donor_bonus_2a.jpg"};
        special[] = {"tex"};
      };
      class ChampionExplorer {
        name = "Champion Explorer";
        faction[] = {"civ"};
        rank[] = {0,0,0,250};
        path[] = {"images\civ_ifrit_explorer_0.jpg","images\civ_ifrit_explorer_1.jpg"};
        special[] = {""};
      };
      class Black {
        name = "Black";
        faction[] = {"civ"};
        rank[] = {0,0,0,30};
        path[] = {"images\civ_ifrit_black_0.jpg","images\civ_ifrit_black_1.jpg"};
        special[] = {""};
      };
      class Grey {
        name = "Grey";
        faction[] = {"civ"};
        rank[] = {0,0,0,30};
        path[] = {"#(rgb,128,128,3)color(1,1,1,0.5)","images\civ_ifrit_black_1.jpg"};
        special[] = {""};
      };
      class White {
        name = "White";
        faction[] = {"civ"};
        rank[] = {0,0,0,30};
        path[] = {"#(rgb,128,128,3)color(1,1,1,1)","images\civ_ifrit_black_1.jpg"};
        special[] = {""};
      };
      class Blue {
        name = "Blue";
        faction[] = {"civ"};
        rank[] = {0,0,0,30};
        path[] = {"#(rgb,128,128,3)color(0,0.4,1,1)","images\civ_ifrit_black_1.jpg"};
        special[] = {""};
      };
      class Pink {
        name = "Pink";
        faction[] = {"civ"};
        rank[] = {0,0,0,30};
        path[] = {"#(rgb,128,128,3)color(0.35,0.00,0.25,1)","images\civ_ifrit_black_1.jpg"};
        special[] = {""};
      };
      class DirtyBastardsGangIfrit {
      	name = "Dirty Bastard's Gang Ifrit";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,999999};
      	path[] = {"images\gang_vehicle_111_0.jpg","images\gang_vehicle_111_1.jpg"};
      	special[] = {111};
      };
      class TeamplayerssGangIfrit {
      	name = "Teamplayers's Gang Ifrit";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,999999};
      	path[] = {"images\gang_vehicle_21653_0.jpg","images\gang_vehicle_21653_1.jpg"};
      	special[] = {21653};
      };
    };
    //Strider
    class I_MRAP_03_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {7,0,0,0};
        path[] = {"images\apd_strider.jpg"};
        special[] = {""};
      };
      class Rescue {
      	name = "Rescue";
      	faction[] = {"med"};
      	rank[] = {0,4,0,0};
      	path[] = {"images\RR_Strider.jpg"};
      	special[] = {""};
      };
      class Camo {
      	name = "Camo";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\soft_f_beta\mrap_03\data\mrap_03_ext_indp_co.paa"};
      	special[] = {""};
      };
      class AltisRebellion {
      	name = "Altis Rebellion";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\civ_strider_1.jpg"};
      	special[] = {""};
      };
      class Wakanda {
      	name = "Wakanda";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,100};
      	path[] = {"images\civ_strider_donor_2.jpg"};
      	special[] = {"tex"};
      };
      class ChampionExplorer {
        name = "Champion Explorer";
        faction[] = {"civ"};
        rank[] = {0,0,0,250};
        path[] = {"images\civ_strider_donor_1.jpg"};
        special[] = {"tex"};
      };
    };
    //SUV
    class C_SUV_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {2,0,0,0};
        path[] = {"images\apd_suv.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,1,0,0};
        path[] = {"images\rr_suv.jpg"};
        special[] = {""};
      };
      class DonorRescue {
        name = "Donor Rescue";
        faction[] = {"med"};
        rank[] = {0,0,0,50};
        path[] = {"images\RR_Donor_SUV.jpg"};
        special[] = {"tex"};
      };
      class DarkRed {
      	name = "Dark Red";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\SUV_01\Data\suv_01_ext_co.paa"};
      	special[] = {""};
      };
      class Black {
      	name = "Black";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\SUV_01\Data\suv_01_ext_02_co.paa"};
      	special[] = {""};
      };
      class Silver {
      	name = "Silver";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\SUV_01\Data\suv_01_ext_03_co.paa"};
      	special[] = {""};
      };
      class Orange {
      	name = "Orange";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"\a3\soft_f_gamma\SUV_01\Data\suv_01_ext_04_co.paa"};
      	special[] = {""};
      };
      class Vigilante {
      	name = "Vigilante";
      	faction[] = {"civ"};
      	rank[] = {0,0,4,0};
      	path[] = {"images\vigi_suv_1.jpg"};
      	special[] = {"vigi"};
      };
      class Monster {
      	name = "Monster";
      	faction[] = {"civ","civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_suv_4.jpg"};
      	special[] = {""};
      };
      class Mello {
      	name = "Mello";
      	faction[] = {"civ","cop"};
      	rank[] = {3,0,0,0};
      	path[] = {"images\civ_suv_5.jpg"};
      	special[] = {""};
      };
    };
    //Qilin
    class O_LSV_02_unarmed_viper_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\apd_qilin_0.jpg","images\apd_qilin_1.jpg","images\apd_qilin_2.jpg"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,2,0,0};
        path[] = {"images\RR_Qilin.jpg","images\qilin2.jpg","images\qilin3.jpg"};
        special[] = {""};
      };
      class Static {
        name = "Static";
        faction[] = {"civ","cop"};
        rank[] = {4,0,0,9999};
        path[] = {"#(ai,2048,2048,1)perlinNoise(768,1024,0,0.4)","#(ai,2048,2048,1)perlinNoise(768,1024,0,0.4)","#(ai,2048,2048,1)perlinNoise(768,1024,0,0.4)"};
        special[] = {"76561198071078342"};
      };
      class HexCamo {
      	name = "Hex Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {4,0,0,0};
      	path[] = {"\a3\soft_f_exp\lsv_02\data\csat_lsv_01_ghex_co.paa","\a3\soft_f_exp\lsv_02\data\csat_lsv_02_ghex_co.paa","\a3\soft_f_exp\lsv_02\data\csat_lsv_03_ghex_co.paa"};
      	special[] = {""};
      };
      class AridCamo {
      	name = "Arid Camo";
      	faction[] = {"civ","cop"};
      	rank[] = {4,0,0,0};
      	path[] = {"\a3\soft_f_exp\lsv_02\data\csat_lsv_01_arid_co.paa","\a3\soft_f_exp\lsv_02\data\csat_lsv_02_arid_co.paa","\a3\soft_f_exp\lsv_02\data\csat_lsv_03_arid_co.paa"};
      	special[] = {""};
      };
      class Black {
      	name = "Black";
      	faction[] = {"civ","cop"};
      	rank[] = {4,0,0,0};
      	path[] = {"\a3\soft_f_exp\lsv_02\data\csat_lsv_01_black_co.paa","\a3\soft_f_exp\lsv_02\data\csat_lsv_02_black_co.paa","\a3\soft_f_exp\lsv_02\data\csat_lsv_03_black_co.paa"};
      	special[] = {""};
      };
      class RoachGreen {
      	name = "Roach Green";
      	faction[] = {"civ","cop"};
      	rank[] = {4,0,0,15};
      	path[] = {"images\civ_qilin_donor_0.jpg","images\civ_qilin_donor_1.jpg","images\apd_qilin_2.jpg"};
      	special[] = {"tex"};
      };
      class Pink {
        name = "Pink";
        faction[] = {"civ","cop"};
        rank[] = {0,0,0,30};
        path[] = {"#(argb,8,8,3)color(1,0,0.5,1,ca)","images\qilin2.jpg","images\qilin3.jpg"};
        special[] = {""};
      };
    };
    class O_T_LSV_02_armed_F : O_LSV_02_unarmed_viper_F {};
    //Boats
    class C_Boat_Civil_01_F {
      class Rescue {
      	name = "Rescue";
      	faction[] = {"med"};
      	rank[] = {0,2,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.8,0.15,0,1)"};
      	special[] = {""};
      };
    };
    class C_Scooter_Transport_01_F {
      class Police {
      	name = "Police";
      	faction[] = {"cop"};
      	rank[] = {0,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
    };
    class C_Boat_Transport_02_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {0,0,0,0};
        path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
        special[] = {""};
      };
      class Rescue {
        name = "Rescue";
        faction[] = {"med"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\boat_f_exp\boat_transport_02\data\boat_transport_02_exterior_co.paa","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
        special[] = {""};
      };
      class BlueandWhite {
        name = "Blue and White";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\boat_f_exp\boat_transport_02\data\boat_transport_02_exterior_civilian_co.paa"};
        special[] = {""};
      };
    };
    //Prowler
    class B_LSV_01_unarmed_black_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\APD_Prowler_0.jpg","images\APD_Prowler_1.jpg","images\APD_Prowler_2.jpg","images\APD_Prowler_3.jpg"};
        special[] = {""};
      };
      class Vigilante {
      	name = "Vigilante";
      	faction[] = {"civ"};
      	rank[] = {0,0,4,0};
      	path[] = {"images\vig_prowler_0.jpg","images\vig_prowler_1.jpg","images\vig_prowler_2.jpg"};
      	special[] = {"vigi"};
      };
    };
    //Armed Prowler
    class B_T_LSV_01_armed_F {
      class APDEscort {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {0,0,0,10000};
        path[] = {"images\APD_Prowler_0.jpg","images\APD_Prowler_1.jpg","images\APD_Prowler_2.jpg","images\APD_Prowler_3.jpg"};
        special[] = {""};
      };
    }
    //Go-Kart
    class C_Kart_01_green_F {
      class Rescue {
      	name = "Kavala Kart";
      	faction[] = {"med"};
      	rank[] = {0,5,0,0};
      	path[] = {"images\rr_kart.jpg"};
      	special[] = {""};
      };
    };
    //Plane
    class C_Plane_Civil_01_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\APD_Airplane_0.jpg","images\APD_Airplane_1.jpg"};
        special[] = {""};
      };
      class Redline {
      	name = "Redline";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\air_f_exp\plane_civil_01\data\btt_ext_01_redline_co.paa","\a3\air_f_exp\plane_civil_01\data\btt_ext_02_redline_co.paa"};
      	special[] = {""};
      };
    };
    //Armed plane
    class C_Plane_Civil_01_racing_F {
      class Police {
        name = "Police";
        faction[] = {"cop"};
        rank[] = {3,0,0,0};
        path[] = {"images\APD_Airplane_0.jpg","images\APD_Airplane_1.jpg"};
        special[] = {""};
      };
      class Redline {
        name = "Redline";
        faction[] = {"civ"};
        rank[] = {6,0,0,0};
        path[] = {"\a3\air_f_exp\plane_civil_01\data\btt_ext_01_redline_co.paa", "\a3\air_f_exp\plane_civil_01\data\btt_ext_02_redline_co.paa"};
        special[] = {""};
      };
      class P51DMustang {
      	name = "P51-D Mustang";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\reb_planebody.jpg","images\reb_planewings.jpg"};
      	special[] = {""};
      };
      class German {
      	name = "German";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\reb_planebody1.jpg","images\reb_planewings1.jpg"};
      	special[] = {""};
      };
      class Japanese {
      	name = "Japanese";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"images\reb_plane_jap_0.jpg","images\reb_plane_jap_1.jpg"};
      	special[] = {""};
      };
    };
    //Tractor
    class C_Tractor_01_F {
      class RedTractor {
      	name = "Red Tractor";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\Soft_F_Enoch\Tractor_01\data\Tractor_01_2_red_co.paa"};
      	special[] = {""};
      };
      class GreenTractor {
      	name = "Green Tractor";
      	faction[] = {"civ"};
      	rank[] = {0,0,0,0};
      	path[] = {"\a3\Soft_F_Enoch\Tractor_01\data\Tractor_01_2_green_co.paa"};
      	special[] = {""};
      };
    };
    //VTOL
    class B_T_VTOL_01_infantry_F {
      class Blue {
        name = "Blue";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\air_f_exp\vtol_01\data\VTOL_01_EXT01_blue_CO.paa","\a3\air_f_exp\vtol_01\data\VTOL_01_EXT02_blue_CO.paa","\a3\air_f_exp\vtol_01\data\VTOL_01_EXT03_blue_CO.paa","\a3\air_f_exp\vtol_01\data\VTOL_01_EXT04_blue_CO.paa"};
        special[] = {""};
      };
      class Olive {
        name = "Olive";
        faction[] = {"civ"};
        rank[] = {0,0,0,0};
        path[] = {"\a3\air_f_exp\vtol_01\data\vtol_01_ext01_olive_co.paa","\a3\air_f_exp\vtol_01\data\vtol_01_ext02_olive_co.paa","\a3\air_f_exp\vtol_01\data\vtol_01_ext03_olive_co.paa","\a3\air_f_exp\vtol_01\data\vtol_01_ext04_olive_co.paa"};
        special[] = {""};
      };
    };
    class B_T_VTOL_01_vehicle_F : B_T_VTOL_01_infantry_F {};
    //Xian
    class O_T_VTOL_02_infantry_F {
      class Police {
      	name = "Master Chief";
      	faction[] = {"cop"};
      	rank[] = {7,0,0,0};
      	path[] = {"#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)","#(rgb,128,128,3)color(0.1,0.1,0.1,0.1)"};
      	special[] = {""};
      };
    };
    class O_T_VTOL_02_vehicle_F {
      class Rescue {
      	name = "Rescue";
      	faction[] = {"med"};
      	rank[] = {0,7,0,0};
      	path[] = {"images\rr_vtol_0.jpg","images\rr_vtol_1.jpg","images\rr_vtol_2.jpg","images\rr_vtol_2.jpg"};
      	special[] = {""};
      };
    };
  };
};
