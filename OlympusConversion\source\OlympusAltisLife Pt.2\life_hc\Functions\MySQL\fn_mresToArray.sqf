//	File: fn_mresToArray.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine";

//	Description:
//	Acts as a mres (MySQL Real Escape) for arrays so they
//	can be properly inserted into the database without causing
//	any problems. The return method is 'hacky' but it's effective.

private["_array"];
_array = param [0,"",[""]];
if(_array == "") exitWith {[]};
_array = toArray(_array);

for "_i" from 0 to (count _array)-1 do {
	_sel = _array select _i;
	if(_sel == 96) then {
		_array set[_i,39];
	};
};

_array = toString(_array);
_array = call compile format["%1", _array];
_array;
