//  File: fn_shedGarage.sqf
//	Author: <PERSON> "tkcjesse" Schultz
//	Description: Handles where vehicles spawn in sheds
params [
	["_spawnObject",objNull,[objNull]],
	["_type","",[""]]
];
private ["_gangShed","_gangID"];
_gangShed = false;
if((oev_gang_data select 2) >= 2) then {
	_gangShed = [
		format ["Would you like to access your personal garage or your gang garage?"],
		"Spawn Vehicle",
		"Gang",
		"Personal"
	] call BIS_fnc_guiMessage;
};

private _action = [
	"Would you like to spawn your vehicle inside the shed or outside?<br/>Outside spawns are currently considered work in process and may produce undesirable results...",
	"Spawn Location",
	"Outside",
	"Inside"
] call BIS_fnc_guiMessage;

if (_action) then {
	oev_garage_sp = [(typeOf _spawnObject),_spawnObject];
} else {
	oev_garage_sp = [_spawnObject,(getDir _spawnObject)+270];
};

oev_garage_type = _type;
_gangID = _spawnObject getVariable ["bldg_gangid",-2];
[[getPlayerUID player,playerSide,_type,player,_gangShed,_gangID],"OES_fnc_getVehicles",false,false] spawn OEC_fnc_MP;
["Life_impound_menu"] call OEC_fnc_createDialog;
disableSerialization;
ctrlSetText[2802,(localize "STR_ANOTF_QueryGarage")];