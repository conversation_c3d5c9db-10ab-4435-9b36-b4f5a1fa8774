/*%FSM<COMPILE "C:\Program Files (x86)\Steam\steamapps\common\Arma 3 Tools\FSMEditor\scriptedFSM.cfg, monitorVehicle">*/
/*%FSM<HEAD>*/
/*
item0[] = {"Start_Monitoring",0,250,-91.714722,-183.470551,76.423874,-136.760513,0.000000,"Start Monitoring Vehicle" \n ""};
item1[] = {"Continue",8,218,-55.136841,-112.505096,34.863190,-62.505157,0.000000,"Continue"};
item2[] = {"Check",3,250,-55.111816,108.231300,35.710663,145.071686,0.000000,"Check"};
item3[] = {"End",1,250,107.803177,-178.396973,197.803101,-134.154373,0.000000,"End"};
item4[] = {"canDisable",4,218,14.793121,184.588913,104.793121,234.588928,1.000000,"canDisable"};
item5[] = {"canEnable",4,4314,-113.801514,183.766434,-23.801514,233.766418,1.000000,"canEnable"};
item6[] = {"isDead",4,218,107.194817,101.592133,197.194824,151.592133,2.000000,"isDead"};
item7[] = {"sleepTime",4,218,-176.021271,30.431519,-86.021271,80.431519,0.000000,"sleepTime"};
item8[] = {"Wait",2,250,-54.295197,-40.301193,35.704819,9.698807,0.000000,"Wait"};
item9[] = {"canCheck",4,218,-54.295197,29.609100,35.704803,79.609100,0.000000,"canCheck"};
item10[] = {"Enable_Sim",2,250,-113.837860,259.745697,-23.837845,309.745697,0.000000,"Enable Sim"};
item11[] = {"Disable_Sim",2,250,16.035065,261.536926,106.035080,311.536926,0.000000,"Disable Sim"};
item12[] = {"",7,210,58.131683,374.391968,66.131683,382.391968,0.000000,""};
item13[] = {"",7,210,-72.636780,374.391968,-64.636795,382.391968,0.000000,""};
item14[] = {"",7,210,-135.751282,374.870361,-127.751297,382.870361,0.000000,""};
link0[] = {0,1};
link1[] = {1,8};
link2[] = {2,4};
link3[] = {2,5};
link4[] = {2,6};
link5[] = {2,7};
link6[] = {4,11};
link7[] = {5,10};
link8[] = {6,3};
link9[] = {7,8};
link10[] = {8,9};
link11[] = {9,2};
link12[] = {10,13};
link13[] = {11,12};
link14[] = {12,13};
link15[] = {13,14};
link16[] = {14,7};
globals[] = {0.000000,0,0,0,0,640,480,3,37,6316128,1,-382.640442,419.032898,660.262756,-260.985779,771,886,1};
window[] = {0,-1,-1,-1,-1,1318,260,1791,260,1,789};
*//*%FSM</HEAD>*/
class FSM
{
        fsmName = "monitorVehicle";
        class States
        {
                /*%FSM<STATE "Start_Monitoring">*/
                class Start_Monitoring
                {
                        name = "Start_Monitoring";
                        itemno = 0;
                        init = /*%FSM<STATEINIT""">*/"_vehicle = _this select 0;" \n
                         "_sim_dist = 200;" \n
                         "_sleep_interval = 3;" \n
                         "_sim_enabled = true;" \n
                         "_last_check = diag_tickTime + _sleep_interval;" \n
                         ""/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "Continue">*/
                                class Continue
                                {
                                        itemno = 1;
                                        priority = 0.000000;
                                        to="Wait";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"(allUnits isEqualType []) && (playableUnits isEqualType [])"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "Check">*/
                class Check
                {
                        name = "Check";
                        itemno = 2;
                        init = /*%FSM<STATEINIT""">*/""/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "isDead">*/
                                class isDead
                                {
                                        itemno = 6;
                                        priority = 2.000000;
                                        to="End";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"!alive _vehicle || isNull _vehicle || !oev_monitorVehicles"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                                /*%FSM<LINK "canEnable">*/
                                class canEnable
                                {
                                        itemno = 5;
                                        priority = 1.000000;
                                        to="Enable_Sim";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"!_sim_enabled && ((!(count (crew _vehicle) == 0) || (player distance _vehicle) < (_sim_dist)) || local _vehicle)"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                                /*%FSM<LINK "canDisable">*/
                                class canDisable
                                {
                                        itemno = 4;
                                        priority = 1.000000;
                                        to="Disable_Sim";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"(!local _vehicle) && _sim_enabled && (count (crew _vehicle) == 0) && (player distance _vehicle) > (_sim_dist) && ((getPosATL _vehicle) select 2) < 5"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                                /*%FSM<LINK "sleepTime">*/
                                class sleepTime
                                {
                                        itemno = 7;
                                        priority = 0.000000;
                                        to="Wait";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"_last_check = diag_tickTime + _sleep_interval;" \n
                                         "true;"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "End">*/
                class End
                {
                        name = "End";
                        itemno = 3;
                        init = /*%FSM<STATEINIT""">*/"if(!oev_monitorVehicles && !isNull _vehicle && (netid _vehicle != ""0:0"")) then {" \n
                         "	_vehicle enableSimulation true;" \n
                         "	_vehicle setVelocity [0,0,0.01];" \n
                         "};" \n
                         "_vehicle = nil;" \n
                         "_sim_dist = nil;" \n
                         "_sim_enabled = nil;" \n
                         "_sleep_interval = nil;" \n
                         "_last_check = nil;" \n
                         ""/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "Wait">*/
                class Wait
                {
                        name = "Wait";
                        itemno = 8;
                        init = /*%FSM<STATEINIT""">*/""/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "canCheck">*/
                                class canCheck
                                {
                                        itemno = 9;
                                        priority = 0.000000;
                                        to="Check";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"diag_tickTime > _last_check"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "Enable_Sim">*/
                class Enable_Sim
                {
                        name = "Enable_Sim";
                        itemno = 10;
                        init = /*%FSM<STATEINIT""">*/"_vehicle enablesimulation true;" \n
                         "_vehicle setVelocity [0,0,0.01];" \n
                         "_sim_enabled = true;"/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "sleepTime">*/
                                class sleepTime
                                {
                                        itemno = 7;
                                        priority = 0.000000;
                                        to="Wait";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"_last_check = diag_tickTime + _sleep_interval;" \n
                                         "true;"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
                /*%FSM<STATE "Disable_Sim">*/
                class Disable_Sim
                {
                        name = "Disable_Sim";
                        itemno = 11;
                        init = /*%FSM<STATEINIT""">*/"_vehicle enablesimulation false;" \n
                         "_sim_enabled = false;"/*%FSM</STATEINIT""">*/;
                        precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
                        class Links
                        {
                                /*%FSM<LINK "sleepTime">*/
                                class sleepTime
                                {
                                        itemno = 7;
                                        priority = 0.000000;
                                        to="Wait";
                                        precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
                                        condition=/*%FSM<CONDITION""">*/"_last_check = diag_tickTime + _sleep_interval;" \n
                                         "true;"/*%FSM</CONDITION""">*/;
                                        action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
                                };
                                /*%FSM</LINK>*/
                        };
                };
                /*%FSM</STATE>*/
        };
        initState="Start_Monitoring";
        finalStates[] =
        {
                "End",
        };
};
/*%FSM</COMPILE>*/