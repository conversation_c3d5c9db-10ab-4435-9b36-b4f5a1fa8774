//  File: fn_vehicle1Weight.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine
//	Description: Starting to lose track on shit I wrote a year ago..
private["_vehicle","_weight","_used"];
_vehicle = param [0,Obj<PERSON>ull,[Obj<PERSON>ull]];
if(isNull _vehicle) exitWith {};

_weight = -1;
_used = (_vehicle getVariable "Trunk") select 1;
_weight = ["allVehicles", (typeOf _vehicle)] call OEC_fnc_vehicleConfig;
_weight = (_weight select 9);

if(isNil "_used") then {_used = 0};
[_weight,_used];