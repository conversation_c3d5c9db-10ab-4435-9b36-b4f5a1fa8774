//  File: fn_warRecieveInv.sqf
//	Author: <PERSON> "tkc<PERSON><PERSON>" Schultz
//	Description: sent to opposing gang leader from warSendInv

params [
	["_mode",-1,[0]],
	["_sender",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]],
	["_invGang<PERSON>",0,[0]],
	["_invGangName","",[""]]
];
if (isNull _sender || _invGangName isEqualTo "" || _invGangID isEqualTo 0 || _mode isEqualTo -1 || oev_inCasino) exitWith {};
if (!(isNil "life_corpse") && { life_corpse getVariable ["revive", false] }) exitWith {
	[1, "STR_NOTF_NotAlive", true, []] remoteExecCall ["OEC_fnc_broadcast", _sender];
};
[1, "Gang war invitation sent."] remoteExecCall ["OEC_fnc_broadcast", _sender];
if (_invGangID in oev_declinedWars) exitWith {};
if (dialog) then {closeDialog 0;};
oev_declinedWars pushBackUnique _invGangID;

private _action = false;

if (_mode isEqualTo 0) then {
	_action = [
		format["The gang %1 wants to start a war with your gang. The war will last until one side decides to end it. RDM Rules will not apply for engagements between the two gangs. Do you want to go to war?",_invGangName],
		"Ready for War?",
		"Bring it",
		"Bail out"
	] call BIS_fnc_guiMessage;
};

if (_mode isEqualTo 1) then {
	life_warKey = true;
	hint parseText format ["The gang %1 wants to start a war with your gang. The war will last until one side decides to end it. RDM Rules will not apply for engagements between the two gangs. Do you want to go to war? You have 1 minute to accept or decline.<br/><br/>Press F8 to Accept<br/>Press F9 to Decline",_invGangName];
	private _timer = time + 60;
	waitUntil {uiSleep 1; (!isNil "life_warResponse" || _timer <= time)};
	if (_timer <= time) then {life_warResponse = false;};
	_action = life_warResponse;
	life_warResponse = nil;
};

private _gangID = (oev_gang_data select 0);
private _gangName = (oev_gang_data select 1);


_log_event = "Accepted Gang War";
if (_action) then {
	[[_sender,_invGangID,_invGangName,_gangID,_gangName,player],"OES_fnc_warInsertGang",false,false] spawn OEC_fnc_MP;

	private _enemy = ([_invGangID] call OEC_fnc_getOnlineMembers);
	private _ally = ([_gangID] call OEC_fnc_getOnlineMembers);
	[[_invGangID,_invGangName,0],"OEC_fnc_warStart",_ally,false] spawn OEC_fnc_MP;
	[[_gangID,_gangName,1],"OEC_fnc_warStart",_enemy,false] spawn OEC_fnc_MP;

} else {
	hint "You've declined the war invite. The other party will be automatically notified.";
	private _enemy = ([_invGangID] call OEC_fnc_getOnlineMembers);
	[[1,format["The gang: %1 has declined your war invitation!",_gangName]],"OEC_fnc_broadcast",_enemy,false] spawn OEC_fnc_MP;
	if !(isNull _sender) then {[[_gangID],"OEC_fnc_warDeclined",_enemy,false] spawn OEC_fnc_MP;};

	_log_event = "Declined Gang War";
};

[
	["event",_log_event],
	["player",name player],
	["player_id",getPlayerUID player],
	["gang",_gangName],
	["gang_id",_gangID],
	["target_gang",_invGangName],
	["target_gang_id",_invGangID],
	["invite_sender",name _sender],
	["invite_sender_id",getPlayerUID _sender]
] call OEC_fnc_logIt;
