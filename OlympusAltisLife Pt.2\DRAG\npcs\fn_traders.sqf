//[this,'STAND','cocaine'] call NPC_fnc_traders;
params ["_obj_main","_stance",["_type","",[""]]];

if (_obj_main isKindOf "Man") then {
	[_obj_main,_stance] call OEC_fnc_ambientAnim;
};

switch (_type) do {
	case "platinum": {_obj_main addAction ["Platinum Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "Wongs": {_obj_main addAction ["Wong's Turtle Market",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6]; _obj_main setVariable ["realname","Wong The Dong"]};
	case "redburger": {_obj_main addAction ["Red Burger",OEC_fnc_virt_menu,_type,1.5,false,false,"",'',6];};
	case "diamond": {_obj_main addAction ["Diamond Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "silver": {_obj_main addAction ["Silver Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "oil": {_obj_main addAction ["Oil Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "iron": {_obj_main addAction ["Iron and Copper Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "glass": {_obj_main addAction ["Glass Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "goldbar": {_obj_main addAction ["Goldbar Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "salvage": {_obj_main addAction ["Salvage Market",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "housing": {
		_obj_main addAction ["Realtor Map",OEC_fnc_openHouseMarket,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["Check Sold Property Funds",OEC_fnc_checkRealtor,[-1,1],1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];
	};
	case "fishmarket": {_obj_main addAction ["Fish Market",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "cement": {_obj_main addAction ["Cement Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "brew": {_obj_main addAction ["Moonshine Distributor",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "salt": {_obj_main addAction ["Salt Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "market": {_obj_main addAction ["Market",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};
	case "heroin": {
		_obj_main addAction ["Drug Dealer",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["Question Dealer",OEC_fnc_questionDealer,nil,1.5,false,false,"",'playerSide isEqualTo west && isNull objectParent player',6];
	};
	case "commodity": {_obj_main addAction ["Commodities Trader",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];};

	case "genstore": {_obj_main addAction ["General Store",OEC_fnc_weaponShopMenu,_type,1.5,false,false,"",'isNull objectParent player',6];};
	case "bruce": {_obj_main addAction ["Clothing Shop",OEC_fnc_clothingMenu,_type,1.5,false,false,"",'playerSide isEqualTo civilian',6];};
	case "dive": {
		_obj_main addAction [format['%1 ($%2)',['license_civ_dive'] call OEC_fnc_varToStr,[(['dive'] call OEC_fnc_licensePrice)] call OEC_fnc_numberText],OEC_fnc_buyLicense,'dive',1.5,false,false,"",'!license_civ_dive && playerSide isEqualTo civilian',6];
		_obj_main addAction ["Diving Shop",OEC_fnc_clothingMenu,_type,1.5,false,false,"",'license_civ_dive && playerSide isEqualTo civilian',6];
		_obj_main addAction ["Diving Item Shop",OEC_fnc_virt_menu,_type,1.5,false,false,"",'license_civ_dive && playerSide isEqualTo civilian && isNull objectParent player',6];
	};
	case "vigi": {
		_obj_main addAction [format['%1 ($%2)',['license_civ_gun'] call OEC_fnc_varToStr,[(['gun'] call OEC_fnc_licensePrice)] call OEC_fnc_numberText],OEC_fnc_buyLicense,'gun',1.5,false,false,"",'!license_civ_gun && playerSide isEqualTo civilian',6];
		_obj_main addAction [format['%1 ($%2)',['license_civ_vigilante'] call OEC_fnc_varToStr,[(['vigilante'] call OEC_fnc_licensePrice)] call OEC_fnc_numberText],OEC_fnc_buyLicense,'vigilante',1.5,false,false,"",'!license_civ_vigilante && playerSide isEqualTo civilian && (O_stats_playtime_civ >= 120)',6];
		_obj_main addAction ["Vigilante Weapon Shop",OEC_fnc_weaponShopMenu,'vigilante',1.5,false,false,"",'license_civ_vigilante && playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["Vigilante Clothing Shop",OEC_fnc_clothingMenu,'vig',1.5,false,false,"",'license_civ_vigilante && playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["Vigilante Market",OEC_fnc_virt_menu,'vigilante',1.5,false,false,"",'license_civ_vigilante && playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["<t color='#ADFF2F'>ATM</t>",OEC_fnc_atmMenu,"",1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["<t color='#FF0000'>Vigilante Rules</t>",OEC_fnc_vigiNotify,"",1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ['Medical Assistance',OEC_fnc_healHospital,"",1.5,false,false,"",'',6];
		_obj_main addAction ["Store Arrests",OEC_fnc_storeVigilanteArrests,0,1.5,false,false,"",'(oev_vigiarrests > 0) && license_civ_vigilante && playerSide isEqualTo civilian && isNull objectParent player',6];
		_obj_main addAction ["Claim Arrests",OEC_fnc_storeVigilanteArrests,1,1.5,false,false,"",'(oev_vigiarrests_stored > 0) && license_civ_vigilante && playerSide isEqualTo civilian && isNull objectParent player',6];
	};
	case "art": {
		_obj_main addAction ["Painting Dealer",OEC_fnc_virt_menu,_type,1.5,false,false,"",'playerSide isEqualTo civilian && isNull objectParent player',6];
	};
};
