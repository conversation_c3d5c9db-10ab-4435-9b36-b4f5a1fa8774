//fn_wanted2add.sqf

private["_display","_list","_side","_sortedPlayers"];
disableSerialization;
waitUntil {!isNull (findDisplay 9900)};
_display = findDisplay 9900;
_list = _display displayCtrl 9902;


lbClear _list;
_sortedPlayers = [];
{
	if(side _x isEqualTo civilian) then {
		_sortedPlayers pushBack [format["%1 - %2", name _x,"Civ"],_x];
	};
}foreach playableUnits;

_sortedPlayers sort true;

{
	_list lbAdd (_x select 0);
	_list lbSetData [(lbSize _list)-1,str(_x select 1)];
}foreach _sortedPlayers;

_list2 = _display displayCtrl 9991;
lbClear _list2;

/*
_text = "Abuse of Work Prot. Lic., $" + format["%1",30000*.75];
_data = "53";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

	//Cannot add new crimes to system this easily, this would break stuff, easier in remake

_text = "Abuse of Vigilente Lic., $" + format["%1",30000*.75];
_data = "54";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];
*/


//fn_wanted2add.sqf

private["_display","_list","_side","_sortedPlayers"];
disableSerialization;
waitUntil {!isNull (findDisplay 9900)};
_display = findDisplay 9900;
_list = _display displayCtrl 9902;


lbClear _list;
_sortedPlayers = [];
{
	if(side _x isEqualTo civilian) then {
		_sortedPlayers pushBack [format["%1 - %2", name _x,"Civ"],_x];
	};
}foreach playableUnits;

_sortedPlayers sort true;

{
	_list lbAdd (_x select 0);
	_list lbSetData [(lbSize _list)-1,str(_x select 1)];
}foreach _sortedPlayers;

_list2 = _display displayCtrl 9991;
lbClear _list2;

/*
_text = "Abuse of Work Prot. Lic., $" + format["%1",30000*.75];
_data = "53";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

	//Cannot add new crimes to system this easily, this would break stuff, easier in remake

_text = "Abuse of Vigilente Lic., $" + format["%1",30000];
_data = "54";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];
*/

_text = format["Aiding in Jail Break, $%1",86000];
_data = "42";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = format["Aiding in Pharm. Robbery, $%1",40000];
_data = "68";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = format["Aiding in Bank Robbery, $%1",81250];
_data = "72";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Aiding in Reserve Robbery, $" + format["%1",112500];
_data = "44";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = format["Aiding in BW Robbery, $%1",112500];
_data = "59";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. BW Robbery, $" + format["%1",82500];
_data = "65";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. Jail Break, $" + format["%1",63750];
_data = "66";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. Bank Robbery, $" + format["%1",32500];
_data = "71";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. Manslaughter, $" + format["%1",26250];
_data = "24";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. Reserve Robbery, $" + format["%1",82500];
_data = "45";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. Robbery, $" + format["%1",8000];
_data = "21";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Attp. Veh. Theft, $" + format["%1",5000];
_data = "23";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Avoiding a Checkpoint, $" + format["%1",30000];
_data = "50";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Destruction of property, $" + format["%1",63750];
_data = "35";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Disobeying an Officer, $" + format["%1",8000];
_data = "47";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Disturbing the Peace, $" + format["%1",1125];
_data = "52";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Driving w/o License, $" + format["%1",6250];
_data = "19";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Driving w/o Lights, $" + format["%1",2000];
_data = "20";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Flying w/o Collision Lights, $" + format["%1",2000];
_data = "70";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Entering a Police Area, $" + format["%1",6000];
_data = "34";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Resisting Arrest, $" + format["%1",16500];
_data = "31";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Flying/Hovering below 150m, $" + format["%1",15000];
_data = "41";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Flying w/o a Pilot License, $" + format["%1",10500];
_data = "43";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Hit and Run, $" + format["%1",7500];
_data = "30";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Hostage Situation, $" + format["%1",86500];
_data = "39";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Ilg. Aerial Veh. Landing, $" + format["%1",48750];
_data = "28";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Insurance Fraud, $" + format["%1",1500];
_data = "46";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Misuse of Emergency System, $" + format["%1",40000];
_data = "58";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Obstruction of Justice, $" + format["%1",15750];
_data = "57";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Obstruction of Traffic, $" + format["%1",4625];
_data = "48";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Operating an Ilg. Vehicle, $" + format["%1",31500];
_data = "29";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Party to a Crime, $" + format["%1",15000];
_data = "56";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Pos. of "+oev_copForce+" Equip., $" + format["%1",25500];
_data = "27";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Pos. of Explosives, $" + format["%1",30000];
_data = "69";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Pos. of Firearms w/o License, $" + format["%1",11000];
_data = "36";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Pos. of Ilg. Equipment, $" + format["%1",15000];
_data = "73";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Pos. of Ilg. Organ, $" + format["%1",22500];
_data = "62";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Pos. of Ilg. Weapon, $" + format["%1",12000];
_data = "37";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Speeding, $" + format["%1",1500];
_data = "25";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Terrorist Acts, $" + format["%1",93750];
_data = "40";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Usage of Drugs in Public, $" + format["%1",10000];
_data = "51";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Use of firearms within city, $" + format["%1",5000];
_data = "38";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Unlawful Taser Usage, $" + format["%1",30000];
_data = "64";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Veh. Theft, $" + format["%1",17500];
_data = "22";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Verbal Insults, $" + format["%1",3000];
_data = "33";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Verbal Threats, $" + format["%1",8000];
_data = "32";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Weapon Trafficking, $" + format["%1",15125];
_data = "49";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Reckless Driving, $" + format["%1",3000];
_data = "26";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Public Urination, $" + format["%1",2500];
_data = "74";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

_text = "Titan Hit, $" + format["%1",15000];
_data = "75";
_list2 lbAdd format["%1",_text];
_list2 lbSetData [(lbSize _list2)-1,_data];

lbSort _list2;