// File: fn_jipRequestTimer.sqf
// Author: <PERSON> "tkc<PERSON><PERSON>" Schultz

params [
	["_container",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]],
	["_player",obj<PERSON><PERSON>,[obj<PERSON>ull]]
];

if (isNull _container || isNull _player) exitWith {};

private _detonateTime = _container getvariable ["bombtime",0];
if (_detonateTime isEqualTo 0) exitWith {};
if(_container isEqualTo gallery_siren) exitWith {[round(_detonateTime - time),true] remoteExec ["OEC_fnc_robPainting",_player,false];};


[_container,round(_detonateTime - time)] remoteExec ["OEC_fnc_demoChargeTimer",_player,false];