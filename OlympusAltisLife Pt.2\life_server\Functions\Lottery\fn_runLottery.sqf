// File: fn_runLottery.sqf
// Author: Fusah
// Description: Runs the lottery!

private _restartTime = round((serverCycleLength - (serverTime - serverStartTime)) / 60);
private _winner = [];
private _picked = false;
if (_restartTime < 35) exitWith {};
if (life_lotteryCooldown) exitWith {};
uiSleep floor random 5; //ok fucknut u arnt going to start 2 of them
if (life_runningLottery) exitWith {};

life_runningLottery = true;
publicVariable "life_runningLottery";

[3,"<t color='#ffdd00'><t size='2'><t align='center'>Lottery<br/><t color='#eeeeff'><t align='center'><t size='1.2'>The lottery has started! Go and purchase a lottery ticket from your local gas station!<br /><br /><t color='#ffdd00'><t size='1.1'>A winner will be picked in 30 minutes.",false,[],"life_lottery"] remoteExec ["OEC_fnc_broadcast",-2,false];

private _time = 1800; //30 Minutes
for "_i" from 0 to 1 step 0 do {
	if (_time <= 0) exitWith {};
	if (_time in [1500,1200,900,600,300,60]) then {
		[3,format["<t color='#ffdd00'><t size='2'><t align='center'>Lottery<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Go and purchase a lottery ticket from your local gas station! A winner will be picked in %2 minute%3.<br /><br /><t color='#ffdd00'><t size='1.1'>The current jackpot is: $%1",[((count life_lottery_list) * 50000) * .95] call OEC_fnc_numberText,_time/60,if (_time/60 isEqualTo 1) then {""} else {"s"}],false,[],"life_lottery"] remoteExec ["OEC_fnc_broadcast",-2,false];
	};
	_time = _time - 60;
	uiSleep 60;
};

//lets go COMPLETLY RANDOM cough cough
for "_e" from 0 to 5 do {
	_winner = life_lottery_list select (floor random (count life_lottery_list));
	if ([_winner select 1] call OEC_fnc_isUIDActive) exitWith {_picked = true};
	uiSleep .1;
};
//welp lets jst get the first person who is in the game and reward them for atleast being fucking on..
if !(_picked) then {
	{
		if ([_x select 1] call OEC_fnc_isUIDActive) exitWith {_winner = life_lottery_list select _forEachIndex;_picked = true};
		} forEach life_lottery_list;
};
//oh cmon its like free money why would u fuckin leave
if !(_picked) then {[3,"<t color='#ffdd00'><t size='2'><t align='center'>Lottery<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Looks like there was no one to collect the winnings!<br /><br /><t color='#ffdd00'><t size='1.1'>The lottery will be available again after a short cooldown.",false,[],"life_lottery"] remoteExec ["OEC_fnc_broadcast",-2,false]};

//its like i look at my code and i....
if (_picked) then {
	private _playerNetID = [_winner select 1] call OES_fnc_getPlayer;
	private _winnings = ((count life_lottery_list) * 50000) * .95; // Take away 5 percent cuz we r jews
	if !(_playerNetID isEqualTo 0) then {
		[1,_winnings] remoteExec ["OEC_fnc_payPlayer",_playerNetID,false];
		format ["-LOTTERY- %1 (%2) won the lottery worth %3!",_winner select 0,_winner select 1,[_winnings] call OEC_fnc_numberText] call OES_fnc_diagLog;
	};
	for "_fuck" from 0 to 2 do {
		[3,format["<t color='#ffdd00'><t size='2'><t align='center'>Lottery<br/><t color='#eeeeff'><t align='center'><t size='1.2'>%1 has WON the lottery worth $%2!<br /><br /><t color='#ffdd00'><t size='1.1'>The lottery will be available again after a short cooldown.",_winner select 0,[_winnings] call OEC_fnc_numberText],false,[],"life_lottery"] remoteExec ["OEC_fnc_broadcast",-2,false];
		uiSleep 1;
	};
};

life_lotteryCooldown = true;
publicVariable "life_lotteryCooldown";

uiSleep 60; //1 minute cuz fuck it

life_lottery_list = [];
life_runningLottery = false;
publicVariable "life_runningLottery";
life_lotteryCooldown = false;
publicVariable "life_lotteryCooldown";
