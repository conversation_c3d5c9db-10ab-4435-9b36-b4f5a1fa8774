#include "..\..\macro.h"
//  File: fn_roadKit.sqf
//	Author: Ozadu
//	Description: Handles the road kit dialog
params[["_mode","",[""]]];

disableSerialization;
waitUntil {!isNull findDisplay 7365};

if !(isNull objectParent player) exitWith {closeDialog 0; hint "You cannot use the road kit while inside a vehicle.";};
if (playerSide isEqualTo civilian || oev_newsTeam) exitWith {closeDialog 0; hint "Civilians are unable to use road kits.";};
if (playerSide isEqualTo west && {oev_inCombat}) exitWith {closeDialog 0; hint "You cannot use the road kit while flagged as in-combat.";};

_list = getControl(7365,2857);
switch (_mode) do {
	case "load":{
		_index =  _list lbAdd "1. Road Cone";
		_list lbSetData [_index,"Cone"];
		_index = _list lbAdd "2. Road Barrier";
		_list lbSetData [_index,"Barrier"];
		if(__GETC__(life_medicLevel) > 2 || call (life_coplevel) > 2) then {
			_index = _list lbAdd "3. Mobile Helipad Light";
			_list lbSetData [_index,"HelipadLight"];
		};
	};

	case "select":{
		_selectedItem = getSelData(2857);
		[_selectedItem] call OEC_fnc_medicPlaceables;
		closeDialog 0;
	};
};
