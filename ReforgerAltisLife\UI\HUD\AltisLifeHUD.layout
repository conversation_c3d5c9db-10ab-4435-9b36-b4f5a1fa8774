FrameWidgetClass {
 Name "AltisLifeHUD"
 Slot FrameSlot {
  Anchor 0 0 1 1
  OffsetLeft 0
  OffsetTop 0
  OffsetRight 0
  OffsetBottom 0
 }
 
 // Main HUD Container
 OverlayWidgetClass {
  Name "HUDContainer"
  Slot FrameSlot {
   Anchor 0 0 1 1
   OffsetLeft 0
   OffsetTop 0
   OffsetRight 0
   OffsetBottom 0
  }
  
  // Top Left Info Panel
  FrameWidgetClass {
   Name "TopLeftPanel"
   Slot FrameSlot {
    Anchor 0 0 0 0
    OffsetLeft 20
    OffsetTop 20
    OffsetRight 350
    OffsetBottom 120
   }
   Color 0 0 0 0.7
   
   VerticalLayoutWidgetClass {
    Name "TopLeftLayout"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 10
     OffsetTop 10
     OffsetRight -10
     OffsetBottom -10
    }
    
    // Player Name
    TextWidgetClass {
     Name "PlayerNameDisplay"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Player Name"
     FontSize 16
     Color 1 1 1 1
     "Font Weight" 600
    }
    
    // Player Role
    TextWidgetClass {
     Name "RoleDisplay"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Civilian"
     FontSize 14
     Color 0.8 0.8 0.8 1
    }
    
    // Cash Display
    TextWidgetClass {
     Name "CashDisplay"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Cash: $5,000"
     FontSize 14
     Color 0.2 0.8 0.2 1
     "Font Weight" 500
    }
    
    // Bank Display
    TextWidgetClass {
     Name "BankDisplay"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Bank: $50,000"
     FontSize 14
     Color 0.2 0.6 0.8 1
     "Font Weight" 500
    }
   }
  }
  
  // Top Right Status Panel
  FrameWidgetClass {
   Name "TopRightPanel"
   Slot FrameSlot {
    Anchor 1 0 1 0
    OffsetLeft -320
    OffsetTop 20
    OffsetRight -20
    OffsetBottom 80
   }
   Color 0 0 0 0.7
   
   HorizontalLayoutWidgetClass {
    Name "TopRightLayout"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 10
     OffsetTop 10
     OffsetRight -10
     OffsetBottom -10
    }
    
    // Server Time
    TextWidgetClass {
     Name "ServerTimeDisplay"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "12:00"
     FontSize 14
     Color 1 1 1 1
     "Text Halign" center
    }
    
    // Player Count
    TextWidgetClass {
     Name "PlayerCountDisplay"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Players: 45/60"
     FontSize 12
     Color 0.8 0.8 0.8 1
     "Text Halign" center
    }
   }
  }
  
  // Notification Panel (Right side)
  FrameWidgetClass {
   Name "NotificationPanel"
   Slot FrameSlot {
    Anchor 1 0 1 0.5
    OffsetLeft -320
    OffsetTop 120
    OffsetRight -20
    OffsetBottom 0
   }
   
   VerticalLayoutWidgetClass {
    Name "NotificationLayout"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 0
     OffsetTop 0
     OffsetRight 0
     OffsetBottom 0
    }
   }
  }
  
  // Interaction Panel (Bottom Center)
  FrameWidgetClass {
   Name "InteractionPanel"
   Slot FrameSlot {
    Anchor 0.5 1 0.5 1
    OffsetLeft -200
    OffsetTop -120
    OffsetRight 200
    OffsetBottom -20
   }
   Color 0 0 0 0.8
   Visible false
   
   VerticalLayoutWidgetClass {
    Name "InteractionLayout"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 10
     OffsetTop 10
     OffsetRight -10
     OffsetBottom -10
    }
    
    // Interaction Title
    TextWidgetClass {
     Name "InteractionTitle"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Available Actions"
     FontSize 16
     Color 1 1 1 1
     "Text Halign" center
     "Font Weight" 600
    }
    
    // Interaction Options Container
    VerticalLayoutWidgetClass {
     Name "InteractionOptions"
     Slot LayoutSlot {
      SizeMode Fill
     }
    }
   }
  }
  
  // Progress Bar (Bottom Center)
  FrameWidgetClass {
   Name "ProgressBarPanel"
   Slot FrameSlot {
    Anchor 0.5 1 0.5 1
    OffsetLeft -150
    OffsetTop -60
    OffsetRight 150
    OffsetBottom -40
   }
   Color 0 0 0 0.8
   Visible false
   
   HorizontalLayoutWidgetClass {
    Name "ProgressLayout"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 10
     OffsetTop 5
     OffsetRight -10
     OffsetBottom -5
    }
    
    // Progress Text
    TextWidgetClass {
     Name "ProgressText"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "Processing..."
     FontSize 12
     Color 1 1 1 1
     "Text Halign" center
    }
    
    // Progress Bar Background
    FrameWidgetClass {
     Name "ProgressBarBG"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Color 0.2 0.2 0.2 1
     
     // Progress Bar Fill
     FrameWidgetClass {
      Name "ProgressBarFill"
      Slot FrameSlot {
       Anchor 0 0 0.5 1
       OffsetLeft 0
       OffsetTop 0
       OffsetRight 0
       OffsetBottom 0
      }
      Color 0.2 0.8 0.2 1
     }
    }
   }
  }
  
  // Mini Map Container (Bottom Right)
  FrameWidgetClass {
   Name "MiniMapPanel"
   Slot FrameSlot {
    Anchor 1 1 1 1
    OffsetLeft -220
    OffsetTop -220
    OffsetRight -20
    OffsetBottom -20
   }
   Color 0 0 0 0.7
   
   OverlayWidgetClass {
    Name "MiniMapContainer"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 5
     OffsetTop 5
     OffsetRight -5
     OffsetBottom -5
    }
    
    // Mini Map Background
    ImageWidgetClass {
     Name "MiniMapBackground"
     Slot FrameSlot {
      Anchor 0 0 1 1
      OffsetLeft 0
      OffsetTop 0
      OffsetRight 0
      OffsetBottom 0
     }
     Color 0.1 0.1 0.1 1
    }
    
    // Player Position Indicator
    ImageWidgetClass {
     Name "PlayerPositionIndicator"
     Slot FrameSlot {
      Anchor 0.5 0.5 0.5 0.5
      OffsetLeft -3
      OffsetTop -3
      OffsetRight 3
      OffsetBottom 3
     }
     Color 1 0 0 1
    }
   }
  }
  
  // Debug Info Panel (Top Center, only visible in debug mode)
  FrameWidgetClass {
   Name "DebugPanel"
   Slot FrameSlot {
    Anchor 0.5 0 0.5 0
    OffsetLeft -150
    OffsetTop 20
    OffsetRight 150
    OffsetBottom 100
   }
   Color 0 0 0 0.8
   Visible false
   
   VerticalLayoutWidgetClass {
    Name "DebugLayout"
    Slot FrameSlot {
     Anchor 0 0 1 1
     OffsetLeft 5
     OffsetTop 5
     OffsetRight -5
     OffsetBottom -5
    }
    
    TextWidgetClass {
     Name "DebugTitle"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "DEBUG INFO"
     FontSize 12
     Color 1 1 0 1
     "Text Halign" center
     "Font Weight" 600
    }
    
    TextWidgetClass {
     Name "DebugInfo"
     Slot LayoutSlot {
      SizeMode Fill
     }
     Text "FPS: 60\nPos: 1000, 0, 1000\nPlayers: 45"
     FontSize 10
     Color 1 1 1 1
    }
   }
  }
 }
}
