//	File: fn_chopShopSell.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Checks whether or not the vehicle is persistent or temp and sells it.

private["_vehicle","_isInsured","_color","_gangID"];
params [
	["_player",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]],
	["_vehicle",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]],
	["_price",500,[0]],
	["_cash",0,[0]],
	["_cashRand",0,[0]]
];

//Error checks
if(isNull _vehicle || isNull _player) exitWith {
	// [["oev_action_inUse",false],"OEC_fnc_netSetVar",nil,false] spawn OEC_fnc_MP;
};

private _unit = owner _player;
_dbInfo = _vehicle getVariable["dbInfo",[]];
_gangID = _vehicle getVariable ["gangID",0];
_displayName = getText(configFile >> "CfgVehicles" >> (typeOf _vehicle) >> "displayName");
_isInsured = _vehicle getVariable["insured",1];
_color = ((_vehicle getVariable ["oev_veh_color",["Default",0]]) select 0);
if (_color isEqualType 0) then {_color = str _color};
// pharma vehicles have _dbInfo = "1234"
if (typeName _dbInfo == "ARRAY" && count _dbInfo > 0) then {
	_uid = _dbInfo select 0;
	_plate = _dbInfo select 1;

	switch (_isInsured) do {
		case 0: {
			_query = format["UPDATE "+dbColumVehicle+" SET alive='0' WHERE pid='%1' AND plate='%2'",_uid,_plate];
			if !(_gangID isEqualTo 0) then {
				_query = format["UPDATE "+dbColumGangVehicle+" SET alive='0' WHERE gang_id='%1' AND plate='%2'",_gangID,_plate];
			};
			[_query,1] call OES_fnc_asyncCall;
		};
		case 1: {
			_query = format["UPDATE "+dbColumVehicle+" SET active='0', insured='0', modifications='""[0,0,0,0,0,0,0,0]""', inventory='""[]""', color='""[%3,0]""', persistentServer='0' WHERE pid='%1' AND plate='%2'",_uid,_plate,parseText _color];
			if !(_gangID isEqualTo 0) then {
				_query = format["UPDATE "+dbColumGangVehicle+" SET active='0', insured='0', modifications='""[0,0,0,0,0,0,0,0]""', inventory='""[]""', color='""[%3,0]""', persistentServer='0' WHERE gang_id='%1' AND plate='%2'",_gangID,_plate,parseText _color];
			};
			[_query,1] call OES_fnc_asyncCall;
		};
		case 2: {
			_query = format["UPDATE "+dbColumVehicle+" SET active='0', insured='0', inventory='""[]""', persistentServer='0' WHERE pid='%1' AND plate='%2'",_uid,_plate];
			if !(_gangID isEqualTo 0) then {
				_query = format["UPDATE "+dbColumGangVehicle+" SET active='0', insured='0', inventory='""[]""', persistentServer='0' WHERE gang_id='%1' AND plate='%2'",_gangID,_plate];
			};
			[_query,1] call OES_fnc_asyncCall;
		};
	};
};

if !(_gangID isEqualTo 0) then {
	format ["88 -LOGGED- %1 (%2) Chopped a gang %3 (%4) for %5",name _player,getPlayerUID _player,typeOf _vehicle,_gangID,[_price] call OEC_fnc_numberText] call OES_fnc_diagLog;
} else {
	format ["88 -LOGGED- %1 (%2) Chopped a %3 (%4) for %5",name _player, getPlayerUID _player,typeOf _vehicle,_vehicle getVariable["vehicle_info_owners","No owner available."],[_price] call OEC_fnc_numberText] call OES_fnc_diagLog;
};

deleteVehicle _vehicle; remoteExec ["OEC_fnc_netSetVar",_unit,false];
["oev_action_inUse",false] remoteExec ["OEC_fnc_netSetVar",_unit,false];
["oev_cash",_cash] remoteExec ["OEC_fnc_netSetVar",_unit,false];
["oev_cache_cash",_cashRand] remoteExec ["OEC_fnc_netSetVar",_unit,false];
[2,format[(localize "STR_NOTF_ChopSoldCar"),_displayName,[_price] call OEC_fnc_numberText]] remoteExec ["OEC_fnc_broadcast",_unit,false];
[1,"Vehicle looks good, here's some cash..."] remoteExec ["OEC_fnc_broadcast",_unit,false];
