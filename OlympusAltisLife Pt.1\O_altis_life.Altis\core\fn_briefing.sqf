waitUntil {!isNull player && player == player};
if(player diarySubjectExists "rules")exitwith{};

player createDiarySubject ["changelog","Change Log"];
player createDiarySubject ["serverrules","Server Rules"];
player createDiarySubject ["policerules","Law Enforcement Info"];
player createDiarySubject ["vigirules","Vigilante Rules"];
player createDiarySubject ["rrrules","RR Rules"];
player createDiarySubject ["controls","Controls"];
player createDiarySubject ["websiteinfo","Server Info"];

/*
Example
    player createDiaryRecord ["", //Container
        [
            "", //Subsection
                "
                    TEXT HERE<br/><br/>
                "
        ]
    ];
*/
player createDiaryRecord["changelog",
    [
        "Olympus Change Log",
            "
The Olympus change log can be found on the website:<br/>
https://olympus-entertainment.com/blogs/blog/1-altis-life-development/<br/>
            "
    ]
];

player createDiaryRecord ["serverrules",
    [
        "General Rules",
            "
General Olympus Rules can be found on the website:<br/>
https://olympus-entertainment.com/topic/4479-olympus-general-rules-6-25-2015/<br/>
            "
    ]
];

player createDiaryRecord ["serverrules",
    [
        "Olympus Life Rules",
            "
The rules regarding all things Altis Life can be found on the website:<br/>
https://olympus-entertainment.com/topic/24562-altis-life-server-rules/<br/>
            "
    ]
];

// Police Section
player createDiaryRecord ["policerules",
    [
        "Law Enforcement Rules",
            "
Law Enforcement Rules and Regulations can be found on the website:<br/>
https://olympus-entertainment.com/topic/31821-apd-master-handbook/<br/>
            "
    ]
];

// Rescue & Recovery Section
player createDiaryRecord ["rrrules",
    [
        "EMS/RnR Rules",
            "
For all RnR rules and guidelines visit the website listed:<br/>
https://olympus-entertainment.com/topic/36392-rr-handbook/<br/>
            "
    ]
];

// Vigilante Section
player createDiaryRecord ["vigirules",
    [
        "Vigilante Rules",
            "
Vigilantes are no exception to the law. They also have to follow the server rules regarding RDM, VDM etc.<br/><br/>
Vigilante Rules can be found on the website in the following section:<br/>
https://olympus-entertainment.com/topic/24562-altis-life-server-rules/<br/>
            "
    ]
];

// Controls Section

player createDiaryRecord ["controls",
    [
        "Game Controls",
            "
Certain Action keys can be rebound in the custom options menu of Arma.<br/><br/>
                1: Wanted List<br/>
                2: Cellphone<br/>
                4: Cop Stolen Vehicles and RnR Dispatch Response<br/>
                U: Lock and unlock cars and House doors<br/>
                Left Shift + O: Earplugs<br/>
                F: Cop and RR Siren<br/>
                Left Shift + L: Activates Cop and RR lights<br/>
                L: Normal Headlights<br/>
                L + Pistol aim: Cop radar<br/>
                Left Shift + F: Siren Yelp<br/>
                Left Shift + T: Nutcracker<br/>
                T: Vehicle Trunk and House Inventory<br/>
                Left Shift + R: Restrain (Cop Only)<br/>
                Tab: Surrender/Unsurrender<br/>
                R: Reload Weapon<br/>
                F: Change gun firing mode<br/>
                H: Holster/Unholster Gun<br/>
                Action Key 9: Mark vehicle as persistent<br/>
                Action Key 10: Windows Key Rebind<br/>
                Action Key 11: Redgull<br/>
                Action Key 12: Earplugs<br/>
                Action Key 14: Lethal Toggle (Cop Only)<br/>
                Action Key 15: Road Kit (Medic) and Spike Strip (Cop)<br/>
                Action Key 16: Repair Object (Medic)<br/>
                Action Key 17: Use Bait Car remote (Cop Only)<br/>
                Action Key 18: Autorun<br/>
                Action Key 19: Bloodbag<br/>
                Action Key 20: Use Heroin<br/>
                Left Windows Key: Main Interaction key which is<br/>
                used for picking up items/money, interacting with<br/>
                cars (repair,etc) and for Cop, RnR and Vigilantes to<br/>
                interact with players. Can be rebound to a single<br/>
                key like H by pressing<br/>
                ESC->Configure->Controls->Custom->Use Action 10<br/><br/>
            "
    ]
];

//Website Info

player createDiaryRecord ["websiteinfo",
    [
        "Website Info",
            "
Below is the info related to our servers.<br/>
If you would like to apply for a whitelisted role then head to the forums and fill out the proper application.<br/>
To put in a report, head to the website and click the Support tab at the top.<br/><br/>
1: Main Website - olympus-entertainment.com<br/>
2: Teamspeak - ts.olympus-entertainment.com
            "
    ]
];
