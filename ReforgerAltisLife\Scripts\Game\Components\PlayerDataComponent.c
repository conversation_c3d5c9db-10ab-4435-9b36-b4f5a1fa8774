//------------------------------------------------------------------------------------------------
//! Player Data Component
//! Manages persistent player data including cash, bank balance, licenses, and statistics
//! Converted from Olympus Altis Life player data system
//------------------------------------------------------------------------------------------------

[ComponentEditorProps(category: "Altis Life/Player", description: "Manages player persistent data")]
class PlayerDataComponentClass : ScriptComponentClass
{
}

//------------------------------------------------------------------------------------------------
class PlayerDataComponent : ScriptComponent
{
	[Attribute("", UIWidgets.EditBox, "Player Steam ID")]
	protected string m_sSteamID;
	
	[Attribute("0", UIWidgets.SpinBox, "Cash Amount", params: "0 ********* 1")]
	protected int m_iCash;
	
	[Attribute("50000", UIWidgets.SpinBox, "Bank Balance", params: "0 ********* 1")]
	protected int m_iBankBalance;
	
	[Attribute("0", UIWidgets.SpinBox, "Play Time (minutes)", params: "0 999999 1")]
	protected int m_iPlayTime;
	
	// Player role and permissions
	protected PlayerRole m_eCurrentRole = PlayerRole.CIVILIAN;
	protected int m_iRoleLevel = 0;
	protected ref array<string> m_aPermissions = {};
	
	// Licenses
	protected ref array<int> m_aLicenses = {};
	
	// Statistics (converted from Olympus 78-stat system)
	protected ref array<int> m_aStatistics = {};
	
	// Position data
	protected vector m_vLastPosition = Vector(0, 0, 0);
	protected string m_sLastWorld = "Everon";
	
	// Session data
	protected bool m_bDataLoaded = false;
	protected bool m_bDataDirty = false;
	
	// Events
	ref ScriptInvoker m_OnDataLoaded = new ScriptInvoker();
	ref ScriptInvoker m_OnCashChanged = new ScriptInvoker();
	ref ScriptInvoker m_OnBankBalanceChanged = new ScriptInvoker();
	
	//------------------------------------------------------------------------------------------------
	//! Component initialization
	override void OnPostInit(IEntity owner)
	{
		super.OnPostInit(owner);
		
		// Initialize statistics array (78 stats from Olympus)
		InitializeStatistics();
		
		// Get player controller and load data
		PlayerController pc = GetGame().GetPlayerController();
		if (pc)
		{
			m_sSteamID = pc.GetPlayerId();
			LoadPlayerData();
		}
		
		// Set up auto-save timer
		GetGame().GetCallqueue().CallLater(AutoSave, 30000, true); // Save every 30 seconds
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize statistics array with default values
	protected void InitializeStatistics()
	{
		m_aStatistics.Clear();
		for (int i = 0; i < 78; i++)
		{
			m_aStatistics.Insert(0);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Load player data from database
	void LoadPlayerData()
	{
		if (m_sSteamID.IsEmpty())
			return;
			
		// Request data from database manager
		DatabaseManager dbManager = DatabaseManager.GetInstance();
		if (dbManager)
		{
			dbManager.LoadPlayerData(m_sSteamID, this);
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Called when player data is loaded from database
	void OnPlayerDataLoaded(ref array<ref array<string>> results)
	{
		if (results.Count() > 0)
		{
			array<string> playerData = results[0];
			
			// Parse player data
			m_iCash = playerData[3].ToInt();
			m_iBankBalance = playerData[4].ToInt();
			m_iPlayTime = playerData[9].ToInt();
			
			// Parse position data
			if (!playerData[5].IsEmpty())
				m_vLastPosition[0] = playerData[5].ToFloat();
			if (!playerData[6].IsEmpty())
				m_vLastPosition[1] = playerData[6].ToFloat();
			if (!playerData[7].IsEmpty())
				m_vLastPosition[2] = playerData[7].ToFloat();
			if (!playerData[8].IsEmpty())
				m_sLastWorld = playerData[8];
		}
		
		m_bDataLoaded = true;
		m_OnDataLoaded.Invoke(this);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Save player data to database
	void SavePlayerData()
	{
		if (!m_bDataLoaded || !m_bDataDirty)
			return;
			
		DatabaseManager dbManager = DatabaseManager.GetInstance();
		if (dbManager)
		{
			dbManager.SavePlayerData(this);
			m_bDataDirty = false;
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Auto-save timer callback
	protected void AutoSave()
	{
		if (m_bDataDirty)
			SavePlayerData();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get cash amount
	int GetCash()
	{
		return m_iCash;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Set cash amount
	void SetCash(int amount)
	{
		if (amount < 0) amount = 0;
		
		int oldAmount = m_iCash;
		m_iCash = amount;
		m_bDataDirty = true;
		
		m_OnCashChanged.Invoke(oldAmount, m_iCash);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Modify cash amount (add or subtract)
	bool ModifyCash(int amount)
	{
		int newAmount = m_iCash + amount;
		if (newAmount < 0)
			return false;
			
		SetCash(newAmount);
		return true;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get bank balance
	int GetBankBalance()
	{
		return m_iBankBalance;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Set bank balance
	void SetBankBalance(int amount)
	{
		if (amount < 0) amount = 0;
		
		int oldAmount = m_iBankBalance;
		m_iBankBalance = amount;
		m_bDataDirty = true;
		
		m_OnBankBalanceChanged.Invoke(oldAmount, m_iBankBalance);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Modify bank balance (add or subtract)
	bool ModifyBankBalance(int amount)
	{
		int newAmount = m_iBankBalance + amount;
		if (newAmount < 0)
			return false;
			
		SetBankBalance(newAmount);
		return true;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get player role
	PlayerRole GetPlayerRole()
	{
		return m_eCurrentRole;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Set player role
	void SetPlayerRole(PlayerRole role, int level = 0)
	{
		m_eCurrentRole = role;
		m_iRoleLevel = level;
		m_bDataDirty = true;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if player has license
	bool HasLicense(int licenseTypeId)
	{
		return m_aLicenses.Find(licenseTypeId) != -1;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Add license to player
	void AddLicense(int licenseTypeId)
	{
		if (!HasLicense(licenseTypeId))
		{
			m_aLicenses.Insert(licenseTypeId);
			m_bDataDirty = true;
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Remove license from player
	void RemoveLicense(int licenseTypeId)
	{
		int index = m_aLicenses.Find(licenseTypeId);
		if (index != -1)
		{
			m_aLicenses.Remove(index);
			m_bDataDirty = true;
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get statistic value
	int GetStatistic(int statIndex)
	{
		if (statIndex < 0 || statIndex >= m_aStatistics.Count())
			return 0;
			
		return m_aStatistics[statIndex];
	}
	
	//------------------------------------------------------------------------------------------------
	//! Set statistic value
	void SetStatistic(int statIndex, int value)
	{
		if (statIndex < 0 || statIndex >= m_aStatistics.Count())
			return;
			
		m_aStatistics[statIndex] = value;
		m_bDataDirty = true;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Modify statistic value (add or subtract)
	void ModifyStatistic(int statIndex, int amount)
	{
		int currentValue = GetStatistic(statIndex);
		SetStatistic(statIndex, currentValue + amount);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Update last position
	void UpdateLastPosition(vector position, string world = "")
	{
		m_vLastPosition = position;
		if (!world.IsEmpty())
			m_sLastWorld = world;
		m_bDataDirty = true;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get last position
	vector GetLastPosition()
	{
		return m_vLastPosition;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get Steam ID
	string GetSteamID()
	{
		return m_sSteamID;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if data is loaded
	bool IsDataLoaded()
	{
		return m_bDataLoaded;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Component cleanup
	override void OnDelete(IEntity owner)
	{
		// Save data before cleanup
		if (m_bDataDirty)
			SavePlayerData();
			
		super.OnDelete(owner);
	}
}

//------------------------------------------------------------------------------------------------
//! Player role enumeration
enum PlayerRole
{
	CIVILIAN = 0,
	POLICE = 1,
	MEDICAL = 2,
	ADMIN = 3
}
