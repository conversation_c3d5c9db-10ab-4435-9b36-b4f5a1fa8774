//[this,'STAND'] call NPC_fnc_civGas;
params ["_obj_main","_stance"];

if (_obj_main isKindOf "Man") then {
	[_obj_main,_stance] call OEC_fnc_ambientAnim;
};

_obj_main addAction ["Market",OEC_fnc_virt_menu,"market",1.5,false,false,"",'',6];
_obj_main addAction ["General Store",OEC_fnc_weaponShopMenu,"genstore",1.5,false,false,"",'',6];
_obj_main addAction ["Purchase Lottery Tickets",{["start"] call OEC_fnc_buyLotteryTicket},"",1.5,false,false,"",'',6];