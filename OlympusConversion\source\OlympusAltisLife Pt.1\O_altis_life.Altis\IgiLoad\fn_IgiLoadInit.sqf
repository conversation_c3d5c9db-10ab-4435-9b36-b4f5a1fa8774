// File: fn_IgiLoadInit.sqf

waituntil {!isnil "bis_fnc_init"};
//if (isDedicated) exitwith {};
//if (isServer) exitwith {};
//waitUntil { !(isNull player) };
waitUntil { time > 0 };

IL_EV_Count = 0;
IL_Veh_Array = [];

//cutText ["IgiLoad is loading. Please wait...","PLAIN DOWN",2];
uiSleep (random 30);

//cutText [Format ["IgiLoad init Player: %1", Player],"PLAIN DOWN",2];

_null = [Player] spawn IG_fnc_IgiLoad;
waitUntil {scriptDone _null};

uiSleep (random (IL_Check_Veh_Max - IL_Check_Veh_Min));

{
	if ((typeOf _x) in (IL_Supported_Vehicles_All)) then
	{
		IL_Veh_Array pushBack _x;
		_null = [_x] spawn IG_fnc_IgiLoad;
		waitUntil {scriptDone _null};
	};
} forEach (vehicles);

//cutText ["IgiLoad loaded. Have fun :)","PLAIN DOWN",2];

while {true} do
{
	uiSleep (IL_Check_Veh_Min + (random (IL_Check_Veh_Max - IL_Check_Veh_Min)));

	//Delete vehicles from "IL_Veh_Array" if not in "vehicles"
	{
		if !(_x in vehicles) then
		{
			IL_Veh_Array = IL_Veh_Array - [_x];
		};
	} forEach (IL_Veh_Array);
	{
		if (((typeOf _x) in (IL_Supported_Vehicles_All)) && !(_x in IL_Veh_Array)) then
		{
			IL_Veh_Array pushBack _x;
			_null = [_x] spawn IG_fnc_IgiLoad;
			waitUntil {scriptDone _null};
		};
	} forEach (vehicles);
};
