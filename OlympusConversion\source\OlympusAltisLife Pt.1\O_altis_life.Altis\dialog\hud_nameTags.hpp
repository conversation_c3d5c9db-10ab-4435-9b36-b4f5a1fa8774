class Life_HUD_nameTags {
	idd = -1;
	duration = 1e+011;
	name = "Life_HUD_nameTags";
	onLoad = "uiNamespace setVariable['Life_HUD_nameTags',_this select 0]";
	objects[] = {};
	
	class controls
	{
		class BaseIcon
		{
			idc = -1;
			type = 13;
			style = 0;
			colorText[] = {1,1,1,1};
			colorBackground[] = {0,0,0,0};
			font = "PuristaMedium";
			text = "";
			size = 0.04;
			shadow = 1.5;
			w = 0; h = 0;
			x = 0.1; y = 0.1;
		};
		
		class p1 : BaseIcon {idc = 78000;};
		class p2 : BaseIcon {idc = 78000 + 1;};
		class p3 : BaseIcon {idc = 78000 + 2;};
		class p4 : BaseIcon {idc = 78000 + 3;};
		class p5 : BaseIcon {idc = 78000 + 4;};
		class p6 : BaseIcon {idc = 78000 + 5;};
		class p7 : BaseIcon {idc = 78000 + 6;};
		class p8 : BaseIcon {idc = 78000 + 7;};
		class p9 : BaseIcon {idc = 78000 + 8;};
		class p10 : BaseIcon {idc = 78000 + 9;};
		class p11 : BaseIcon {idc = 78000 + 10;};
		class p12 : BaseIcon {idc = 78000 + 11;};
		class p13 : BaseIcon {idc = 78000 + 12;};
		class p14 : BaseIcon {idc = 78000 + 13;};
		class p15 : BaseIcon {idc = 78000 + 14;};
		class p16 : BaseIcon {idc = 78000 + 15;};
		class p17 : BaseIcon {idc = 78000 + 16;};
		class p18 : BaseIcon {idc = 78000 + 17;};
		class p19 : BaseIcon {idc = 78000 + 18;};
		class p20 : BaseIcon {idc = 78000 + 19;};
		class p21 : BaseIcon {idc = 78000 + 20;};
		class p22 : BaseIcon {idc = 78000 + 21;};
		class p23 : BaseIcon {idc = 78000 + 22;};
		class p24 : BaseIcon {idc = 78000 + 23;};
		class p25 : BaseIcon {idc = 78000 + 24;};
		class p26 : BaseIcon {idc = 78000 + 25;};
		class p27 : BaseIcon {idc = 78000 + 26;};
		class p28 : BaseIcon {idc = 78000 + 27;};
		class p29 : BaseIcon {idc = 78000 + 28;};
		class p30 : BaseIcon {idc = 78000 + 29;};
		class p31 : BaseIcon {idc = 78000 + 30;};
		class p32 : BaseIcon {idc = 78000 + 31;};
		class p33 : BaseIcon {idc = 78000 + 32;};
		class p34 : BaseIcon {idc = 78000 + 33;};
		class p35 : BaseIcon {idc = 78000 + 34;};
		class p36 : BaseIcon {idc = 78000 + 35;};
		class p37 : BaseIcon {idc = 78000 + 36;};
		class p38 : BaseIcon {idc = 78000 + 37;};
		class p39 : BaseIcon {idc = 78000 + 38;};
		class p40 : BaseIcon {idc = 78000 + 39;};
		class p41 : BaseIcon {idc = 78000 + 40;};
		class p42 : BaseIcon {idc = 78000 + 41;};
		class p43 : BaseIcon {idc = 78000 + 42;};
		class p44 : BaseIcon {idc = 78000 + 43;};
		class p45 : BaseIcon {idc = 78000 + 44;};
		class p46 : BaseIcon {idc = 78000 + 45;};
		class p47 : BaseIcon {idc = 78000 + 46;};
		class p48 : BaseIcon {idc = 78000 + 47;};
		class p49 : BaseIcon {idc = 78000 + 48;};
		class p50 : BaseIcon {idc = 78000 + 49;};
		class p51 : BaseIcon {idc = 78000 + 50;};
		class p52 : BaseIcon {idc = 78000 + 51;};
		class p53 : BaseIcon {idc = 78000 + 52;};
		class p54 : BaseIcon {idc = 78000 + 53;};
		class p55 : BaseIcon {idc = 78000 + 54;};
		class p56 : BaseIcon {idc = 78000 + 55;};
		class p57 : BaseIcon {idc = 78000 + 56;};
		class p58 : BaseIcon {idc = 78000 + 57;};
		class p59 : BaseIcon {idc = 78000 + 58;};
		class p60 : BaseIcon {idc = 78000 + 59;};
		class p61 : BaseIcon {idc = 78000 + 60;};
		class p62 : BaseIcon {idc = 78000 + 61;};
		class p63 : BaseIcon {idc = 78000 + 62;};
		class p64 : BaseIcon {idc = 78000 + 63;};
		class p65 : BaseIcon {idc = 78000 + 64;};
		class p66 : BaseIcon {idc = 78000 + 65;};
		class p67 : BaseIcon {idc = 78000 + 66;};
		class p68 : BaseIcon {idc = 78000 + 67;};
		class p69 : BaseIcon {idc = 78000 + 68;};
		class p70 : BaseIcon {idc = 78000 + 69;};
		class p71 : BaseIcon {idc = 78000 + 70;};
		class p72 : BaseIcon {idc = 78000 + 71;};
		class p73 : BaseIcon {idc = 78000 + 72;};
		class p74 : BaseIcon {idc = 78000 + 73;};
		class p75 : BaseIcon {idc = 78000 + 74;};
		class p76 : BaseIcon {idc = 78000 + 75;};
		class p77 : BaseIcon {idc = 78000 + 76;};
		class p78 : BaseIcon {idc = 78000 + 77;};
		class p79 : BaseIcon {idc = 78000 + 78;};
		class p80 : BaseIcon {idc = 78000 + 79;};
		class p81 : BaseIcon {idc = 78000 + 80;};
		class p82 : BaseIcon {idc = 78000 + 81;};
		class p83 : BaseIcon {idc = 78000 + 82;};
		class p84 : BaseIcon {idc = 78000 + 83;};
		class p85 : BaseIcon {idc = 78000 + 84;};
		class p86 : BaseIcon {idc = 78000 + 85;};
		class p87 : BaseIcon {idc = 78000 + 86;};
		class p88 : BaseIcon {idc = 78000 + 87;};
		class p89 : BaseIcon {idc = 78000 + 88;};
		class p90 : BaseIcon {idc = 78000 + 89;};
		class p91 : BaseIcon {idc = 78000 + 90;};
		class p92 : BaseIcon {idc = 78000 + 91;};
		class p93 : BaseIcon {idc = 78000 + 92;};
		class p94 : BaseIcon {idc = 78000 + 93;};
		class p95 : BaseIcon {idc = 78000 + 94;};
		class p96 : BaseIcon {idc = 78000 + 95;};
		class p97 : BaseIcon {idc = 78000 + 96;};
		class p98 : BaseIcon {idc = 78000 + 97;};
		class p99 : BaseIcon {idc = 78000 + 98;};
		class p100 : BaseIcon {idc = 78000 + 99;};
		class p101 : BaseIcon {idc = 78000 + 100;};
		class p102 : BaseIcon {idc = 78000 + 101;};
		class p103 : BaseIcon {idc = 78000 + 102;};
		class p104 : BaseIcon {idc = 78000 + 103;};
		class p105 : BaseIcon {idc = 78000 + 104;};
		class p106 : BaseIcon {idc = 78000 + 105;};
		class p107 : BaseIcon {idc = 78000 + 106;};
		class p108 : BaseIcon {idc = 78000 + 107;};
		class p109 : BaseIcon {idc = 78000 + 108;};
		class p110 : BaseIcon {idc = 78000 + 109;};
	};
};