//	Author: <PERSON> "<PERSON>" <PERSON>wine
//	Description:
//	Client sends a cleanup request when they hit Abort,
//	the server will then monitor when that client aborts and
//	delete the weapon holders.

private["_client","_loops","_containers"];
_client = param [0,<PERSON>b<PERSON><PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]];
if(isNull _client) exitWith {};

_loops = 0;
while {true} do {
	if(_loops >= 25) exitWith {};
	if(isNull _client) exitWith {};
	if(!alive _client) exitWith {
		_containers = nearestObjects[_client,["WeaponHolderSimulated"],7];
		if(count _containers > 0) then {
			{deleteVehicle _x;} foreach _containers; //Delete the containers.
		};
		deleteVehicle _client; //Get rid of the corpse.
	};

	_loops = _loops + 1;
	uiSleep 1;
};