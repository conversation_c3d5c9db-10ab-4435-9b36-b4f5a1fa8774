<?xml version="1.0" encoding="utf-8"?>
<Project name="Altis Life RPG">
	<Package name="Global">
		<Key ID="STR_Global_Close">
			<Original>Close</Original>
		</Key>
		<Key ID="STR_Global_Sell">
			<Original>Sell</Original>
		</Key>
		<Key ID="STR_Global_Buy">
			<Original>Buy</Original>
		</Key>
		<Key ID="STR_Global_Give">
			<Original>Give</Original>
		</Key>
		<Key ID="STR_Global_Use">
			<Original>Use</Original>
		</Key>
		<Key ID="STR_Global_Remove">
			<Original>Remove</Original>
		</Key>
		<Key ID="STR_Global_Settings">
			<Original>Settings</Original>
		</Key>
		<Key ID="STR_Global_RentVeh">
			<Original>Rent</Original>
		</Key>
		<Key ID="STR_Global_Retrieve">
			<Original>Retrieve</Original>
		</Key>
		<Key ID="STR_Global_NoSelection">
			<Original>You did not select anything.</Original>
		</Key>
		<Key ID="STR_Global_Yes">
			<Original>Yes</Original>
		</Key>
		<Key ID="STR_Global_No">
			<Original>No</Original>
		</Key>
		<Key ID="STR_Global_Cancel">
			<Original>Cancel</Original>
		</Key>
		<Key ID="STR_Global_Impound">
			<Original>Impound</Original>
		</Key>
	</Package>
	<Package name="Admin_Menu">
		<Key ID="STR_Admin_Title">
			<Original>Admin Menu</Original>
		</Key>
		<Key ID="STR_Admin_GetID">
			<Original>Get ID</Original>
		</Key>
	</Package>
	<Package name="Admin_Notifications">
		<Key ID="STR_ANOTF_Query">
			<Original>Querying...</Original>
		</Key>
		<Key ID="STR_ANOTF_QueryGarage">
			<Original>Searching Database for vehicles...</Original>
		</Key>
		<Key ID="STR_ANOTF_Query_2">
			<Original>You are already querying a player.</Original>
		</Key>
		<Key ID="STR_ANOTF_QueryFail">
			<Original>Player no longer exists?</Original>
		</Key>
	</Package>
	<Package name="ATM_Strings">
		<Key ID="STR_ATM_Title">
			<Original>Bank Account Management</Original>
		</Key>
		<Key ID="STR_ATM_Withdraw">
			<Original>Withdraw</Original>
		</Key>
		<Key ID="STR_ATM_Deposit">
			<Original>Deposit</Original>
		</Key>
		<Key ID="STR_ATM_Transfer">
			<Original>Transfer</Original>
		</Key>
		<Key ID="STR_ATM_GreaterThan">
			<Original>You can't deposit more then $999,999 at a time</Original>
		</Key>
		<Key ID="STR_ATM_notnumeric">
			<Original>The amount entered isn't a numeric value.</Original>
		</Key>
		<Key ID="STR_ATM_DepositMSG">
 			<Original>You have deposited $%1 into your bank account</Original>

 		</Key>
 		<Key ID="STR_ATM_NoneSelected">
 			<Original>You need to select someone to transfer to</Original>
 		</Key>
 		<Key ID="STR_ATM_DoesntExist">
 			<Original>The player selected doesn't seem to exist?</Original>

 		</Key>
 		<Key ID="STR_ATM_TransferMax">
 			<Original>You can't transfer more then $999,999</Original>

 		</Key>
 		<Key ID="STR_ATM_NotEnoughFunds">
 			<Original>You don't have that much in your bank account!</Original>
 		</Key>
		<Key ID="STR_ATM_NotEnoughWarPoints">
			<Original>You don't have that many war points to your name!</Original>
		</Key>
 		<Key ID="STR_ATM_SentMoneyFail">
 			<Original>You do not have enough money in your bank account, to transfer $%1 you will need $%2 as a tax fee.</Original>

 		</Key>
 		<Key ID="STR_ATM_SentMoneySuccess">
 			<Original>You have transferred $%1 to %2.\n\nA tax fee of $%3 was taken for the wire transfer.</Original>

 		</Key>
 		<Key ID="STR_ATM_SentWarPtsSuccess">
 			<Original>You have transferred %1 war points to %2.</Original>

 		</Key>
 		<Key ID="STR_ATM_WithdrawMax">
 			<Original>You can't withdraw more then $999,999</Original>

 		</Key>
 		<Key ID="STR_ATM_WithdrawMin">
 			<Original>You can't withdraw less then $100</Original>

 		</Key>
 		<Key ID="STR_ATM_WithdrawSuccess">
 			<Original>You have withdrawn $%1 from your bank account</Original>

 		</Key>
 		<Key ID="STR_ATM_NotEnoughCash">
 			<Original>You do not have that much cash on you.</Original>

 		</Key>
 		<Key ID="STR_ATM_DepositGang">
 			<Original>You have deposited $%1 into your gangs bank account</Original>

 		</Key>
		<Key ID="STR_ATM_WithdrawGang">
 			<Original>You have withdrawn $%1 from your gangs bank account</Original>

 		</Key>
	</Package>
	<Package name="Cell_Phone">
		<Key ID="STR_CELL_Title">
			<Original>Altis Mobile</Original>
 		</Key>
		<Key ID="STR_CELL_TextToSend">
			<Original>Message To Send:</Original>
		</Key>
		<Key ID="STR_CELL_TextMSGBtn">
			<Original>Text Message:</Original>
		</Key>
		<Key ID="STR_CELL_TextPolice">
			<Original>Text Police</Original>
		</Key>
		<Key ID="STR_CELL_TextAdmins">
			<Original>Text Admins</Original>
		</Key>
		<Key ID="STR_CELL_AdminMsg">
			<Original>Admin Message</Original>
		</Key>
		<Key ID="STR_CELL_AdminMSGAll">
			<Original>Admin Msg All</Original>
		</Key>
		<Key ID="STR_CELL_EMSRequest">
			<Original>Request EMS</Original>
		</Key>
	</Package>
	<Package name="Chop_Shop">
		<Key ID="STR_ChopShop_Title">
			<Original>Local Chop Shop</Original>
		</Key>
	</Package>
	<Package name="Impound_Yard">
		<Key ID="STR_ImpoundYard_Title">
			<Original>Local Impound Yard</Original>
		</Key>
	</Package>
	<Package name="Gang_Menu">
		<Key ID="STR_Gang_Title">
			<Original>Gang Management</Original>
		</Key>
		<Key ID="STR_Gang_Title2">
			<Original>Gang Management - Current Gangs</Original>
		</Key>
		<Key ID="STR_Gang_Join">
			<Original>Join</Original>
		</Key>
		<Key ID="STR_Gang_Leave">
			<Original>Leave</Original>
		</Key>
		<Key ID="STR_Gang_UpgradeSlots">
			<Original>Upgrade Slots</Original>
		</Key>
		<Key ID="STR_Gang_Kick">
			<Original>Kick</Original>
		</Key>
		<Key ID="STR_Gang_SetLeader">
			<Original>Set Leader</Original>
		</Key>
		<Key ID="STR_Gang_PriceTxt">
			<Original>To create a gang it costs $%1</Original>
		</Key>
		<Key ID="STR_Gang_Create">
			<Original>Create</Original>
		</Key>
		<Key ID="STR_Gang_YGN">
			<Original>Your Gang Name</Original>
		</Key>
		<Key ID="STR_Gang_MainMenu">
			<Original>You aren't in a gang currently, what do you want to do?</Original>
		</Key>
		<Key ID="STR_Gang_Browse">
			<Original>Browse</Original>
		</Key>
		<Key ID="STR_Gang_Invite_Player">
			<Original>Invite Player</Original>
		</Key>
		<Key ID="STR_Gang_Disband_Gang">
			<Original>Disband Gang</Original>
		</Key>
		<Key ID="STR_Gang_Invitation">
			<Original>Gang Invitation</Original>
		</Key>
		<Key ID="STR_Gang_Transfer">
			<Original>Transfer Gang Leadership</Original>
		</Key>
		<Key ID="STR_Gang_UpgradeMax">
			<Original>Upgrade Maximum Allowed Gang Members</Original>
		</Key>
	</Package>
	<Package name="Gang_Notification">
		<Key ID="STR_GNOTF_CreateGang">
			<Original>You must create a gang first before capturing it!</Original>
		</Key>
		<Key ID="STR_GNOTF_Controlled">
			<Original>Your gang already has control over this hideout!</Original>
		</Key>
		<Key ID="STR_GNOTF_Captured">
			<Original>Only one person shall capture at once!</Original>
		</Key>
		<Key ID="STR_GNOTF_AlreadyControlled">
			<Original>This hideout is controlled by %1.&lt;br/&gt;&lt;br/&gt;Are you sure you want to take over their gang area?</Original>
		</Key>
		<Key ID="STR_GNOTF_CurrentCapture">
			<Original>Hideout is currently under control...</Original>
		</Key>
		<Key ID="STR_GNOTF_CaptureCancel">
			<Original>Capturing cancelled</Original>
		</Key>
		<Key ID="STR_GNOTF_Capturing">
			<Original>Capturing Hideout</Original>
		</Key>
		<Key ID="STR_GNOTF_CaptureSuccess">
			<Original>%1 and his gang: %2 - have taken control of a local hideout!</Original>
		</Key>
		<Key ID="STR_GNOTF_Over32">
			<Original>You can't have a gang name longer then 32 characters.</Original>
		</Key>
		<Key ID="STR_GNOTF_IncorrectChar">
			<Original>You have invalid characters in your gang name. It can only consist of Numbers and letters with an underscore</Original>
		</Key>
		<Key ID="STR_GNOTF_NotEnoughMoney">
			<Original>You do not have enough money in your bank account.\n\nYou lack: $%1</Original>
		</Key>
		<Key ID="STR_GNOTF_CreateSuccess">
			<Original>You have created the gang %1 for $%2</Original>
		</Key>
		<Key ID="STR_GNOTF_DisbandWarn">
			<Original>You are about to disband the gang, by disbanding the gang it will be removed from the database and the group will be dropped. &lt;br/&gt;&lt;br/&gt;Are you sure you want to disband the group? You will not be refunded the price for creating it.</Original>
		</Key>
		<Key ID="STR_GNOTF_DisbandGangPro">
			<Original>Disbanding the gang...</Original>
		</Key>
		<Key ID="STR_GNOTF_DisbandGangCanc">
			<Original>Disbanding cancelled</Original>
		</Key>
		<Key ID="STR_GNOTF_DisbandWarn_2">
			<Original>The leader has disbanded the gang.</Original>
		</Key>
		<Key ID="STR_GNOTF_InviteMSG">
			<Original>%1 has invited you to a gang called %2&lt;br/&gt;If you accept the invitation you will be a part of their gang and will have access to the controlled gang hideouts.</Original>
		</Key>
		<Key ID="STR_GNOTF_InvitePlayerMSG">
			<Original>You are about to invite %1 to your gang, if they accept the invite they will be rank 0.</Original>
		</Key>
		<Key ID="STR_GNOTF_SelectPerson">
			<Original>You need to select a person to invite!</Original>
		</Key>
		<Key ID="STR_GNOTF_InviteSent">
			<Original>You have sent a invite to your gang to %1</Original>
		</Key>
		<Key ID="STR_GNOTF_InviteCancel">
			<Original>Invitation Cancelled</Original>
		</Key>
		<Key ID="STR_GNOTF_KickSelf">
			<Original>You cannot kick yourself!</Original>
		</Key>
		<Key ID="STR_GNOTF_MaxSlot">
			<Original>Your gang has reached its maximum allowed slots, please upgrade your gangs slot limit.</Original>
		</Key>
		<Key ID="STR_GNOTF_SelectKick">
			<Original>You need to select a person to kick!</Original>
		</Key>
		<Key ID="STR_GNOTF_LeaderLeave">
			<Original>You cannot leave the gang without appointing a new leader first!</Original>
		</Key>
		<Key ID="STR_GNOTF_TransferMSG">
			<Original>You are about to transfer leadership over to %1&lt;br/&gt;By transferring leadership you will no longer be in control of the gang unless ownership is transferred back.</Original>
		</Key>
		<Key ID="STR_GNOTF_TransferCancel">
			<Original>Transfer of leadership cancelled.</Original>
		</Key>
		<Key ID="STR_GNOTF_TransferSelf">
			<Original>You are already the leader!</Original>
		</Key>
		<Key ID="STR_GNOTF_TransferSelect">
			<Original>You need to select a person first!</Original>
		</Key>
		<Key ID="STR_GNOTF_MaxMemberMSG">
			<Original>You are about to upgrade the maximum members allowed for your gang.</Original>
		</Key>
		<Key ID="STR_GNOTF_CurrentMax">
			<Original>Current Max: %1</Original>
		</Key>
		<Key ID="STR_GNOTF_UpgradeMax">
			<Original>Upgraded Max: %2</Original>
		</Key>
		<Key ID="STR_GNOTF_Price">
			<Original>Price:</Original>
		</Key>
		<Key ID="STR_GNOTF_NotEoughMoney_2">
			<Original>You do not have enough money in your bank account to upgrade the gangs maximum member limit.</Original>
		</Key>
		<Key ID="STR_GNOTF_Current">
			<Original>Current:</Original>
		</Key>
		<Key ID="STR_GNOTF_Lacking">
			<Original>Lacking:</Original>
		</Key>
		<Key ID="STR_GNOTF_UpgradeSuccess">
			<Original>You have upgraded from %1 to %2 maximum slots for &lt;t color='#8cff9b'&gt;$%3&lt;/t&gt;</Original>
		</Key>
		<Key ID="STR_GNOTF_UpgradeCancel">
			<Original>Upgrade cancelled.</Original>
		</Key>
		<Key ID="STR_GNOTF_Funds">
			<Original>Funds:</Original>
		</Key>
		<Key ID='STR_GNOTF_GangLeader'>
			<Original>(Gang Leader)</Original>
		</Key>
	</Package>
	<Package name="Garage_Strings">
		<Key ID="STR_Garage_Title">
			<Original>Vehicle Garage</Original>
		</Key>
		<Key ID="STR_Garage_GetVehicle">
			<Original>Get Vehicle</Original>
		</Key>
		<Key ID="STR_Garage_SellVehicle">
			<Original>Sell Vehicle</Original>
		</Key>
		<Key ID="STR_Garage_SellPrice">
			<Original>Sell Price</Original>
		</Key>
		<Key ID="STR_Garage_SFee">
			<Original>Storage Fee</Original>
		</Key>
		<Key ID="STR_Garage_NoVehicles">
			<Original>No vehicles found in garage.</Original>
		</Key>
		<Key ID="STR_Garage_CashError">
			<Original>You don't have $%1 in your bank account</Original>
		</Key>
		<Key ID="STR_Garage_SpawningVeh">
			<Original>Spawning vehicle please wait...</Original>
		</Key>
		<Key ID="STR_Garage_SQLError_Destroyed">
			<Original>Sorry but %1 was classified as a destroyed vehicle and was sent tot he scrap yard.</Original>
		</Key>
		<Key ID="STR_Garage_SQLError_Active">
			<Original>Sorry but %1 is already active somewhere in the world and cannot be spawned.</Original>
		</Key>
		<Key ID="STR_Garage_SpawnPointError">
			<Original>There is already a vehicle on the spawn point. You will be refunded the cost of getting it out.</Original>
		</Key>
		<Key ID="STR_Garage_SoldCar">
			<Original>You sold that vehicle for $%1</Original>
		</Key>
		<Key ID="STR_Garage_Store_NotPersistent">
			<Original>That vehicle is a rental and cannot be stored in your garage.</Original>
		</Key>
		<Key ID="STR_Garage_Store_NoOwnership">
			<Original>That vehicle doesn't belong to you therefor you cannot store it in your garage.</Original>
		</Key>
		<Key ID="STR_Garage_Store_Success">
			<Original>The vehicle has been stored in your garage.</Original>
		</Key>
		<Key ID="STR_Garage_Store_Server">
			<Original>The server is trying to store the vehicle...</Original>
		</Key>
		<Key ID="STR_Garage_Selection_Error">
			<Original>The selection had a error...</Original>
		</Key>
		<Key ID="STR_Garage_NoNPC">
			<Original>There isn't a vehicle near the NPC.</Original>
		</Key>
	</Package>
	<Package name="Key_Chain">
		<Key ID="STR_Keys_Title">
			<Original>Key Chain - Current List of Keys</Original>
		</Key>
		<Key ID="STR_Keys_DropKey">
			<Original>Drop Key</Original>
		</Key>
		<Key ID="STR_Keys_GiveKey">
			<Original>Give Key</Original>
		</Key>
	</Package>
	<Package name="Player_Menu">
		<Key ID="STR_PM_Title">
			<Original>Player Menu</Original>
		</Key>
		<Key ID="STR_PM_cItems">
			<Original>Current Items</Original>
		</Key>
		<Key ID="STR_PM_Licenses">
			<Original>Licenses</Original>
		</Key>
		<Key ID="STR_PM_MoneyStats">
			<Original>Money Stats</Original>
		</Key>
		<Key ID="STR_PM_MyGang">
			<Original>My Gang</Original>
		</Key>
		<Key ID="STR_PM_WantedList">
			<Original>Wanted List</Original>
		</Key>
		<Key ID="STR_PM_CellPhone">
			<Original>Cell Phone</Original>
		</Key>
		<Key ID="STR_PM_KeyChain">
			<Original>Key Chain</Original>
		</Key>
		<Key ID="STR_PM_AdminMenu">
			<Original>Admin Menu</Original>
		</Key>
		<Key ID="STR_PM_SyncData">
			<Original>Sync Data</Original>
		</Key>
	</Package>
	<Package name="Settings_Menu">
		<Key ID="STR_SM_Title">
			<Original>Settings Menu</Original>
		</Key>
		<Key ID="STR_SM_onFoot">
			<Original>On Foot:</Original>
		</Key>
		<Key ID="STR_SM_inCar">
			<Original>In Car:</Original>
		</Key>
		<Key ID="STR_SM_inAir">
			<Original>In Air:</Original>
		</Key>
		<Key ID="STR_SM_ToolTip1">
			<Original>View distance while on foot</Original>
		</Key>
		<Key ID="STR_SM_ToolTip2">
			<Original>View distance while in a land vehicle</Original>
		</Key>
		<Key ID="STR_SM_ToolTip3">
			<Original>View distance while in a air vehicle</Original>
		</Key>
		<Key ID="STR_SM_PlayerTags">
			<Original>Player Tags</Original>
		</Key>
		<Key ID="STR_SM_TagsON">
			<Original>Tags ON</Original>
		</Key>
		<Key ID="STR_SM_TagsOFF">
			<Original>Tags OFF</Original>
		</Key>
		<Key ID="STR_SM_SC">
			<Original>Sidechat Settings</Original>
		</Key>
		<Key ID="STR_SM_SCOFF">
			<Original>Sidechat OFF</Original>
		</Key>
		<Key ID="STR_SM_SCON">
			<Original>Sidechat ON</Original>
		</Key>
	</Package>
	<Package name="Virt_Shop">
		<Key ID="STR_VS_SI">
			<Original>Shop Inventory</Original>
		</Key>
		<Key ID="STR_VS_PI">
			<Original>Your Inventory</Original>
		</Key>
		<Key ID="STR_VS_BuyItem">
			<Original>Buy Item</Original>
		</Key>
		<Key ID="STR_VS_SellItem">
			<Original>Sell Item</Original>
		</Key>
	</Package>
	<Package name="Spawn_Menu">
		<Key ID="STR_Spawn_Title">
			<Original>Spawn Selection</Original>
		</Key>
		<Key ID="STR_Spawn_Spawn">
			<Original>Spawn</Original>
		</Key>
		<Key ID="STR_Spawn_CSP">
			<Original>Current Spawn Point</Original>
		</Key>
		<Key ID="STR_Spawn_Spawned">
			<Original>You have spawned at</Original>
		</Key>
	</Package>
	<Package name="Ticket_Menu">
		<Key ID="STR_Ticket_GiveTicket">
			<Original>Give Ticket</Original>
		</Key>
		<Key ID="STR_Ticket_PayTicket">
			<Original>Pay Ticket</Original>
		</Key>
		<Key ID="STR_Ticket_RefuseTicket">
			<Original>Refuse Ticket</Original>
		</Key>
	</Package>
	<Package name="Trunk_Menu">
		<Key ID="STR_Trunk_TInventory">
			<Original>Trunk Inventory</Original>
		</Key>
		<Key ID="STR_Trunk_PInventory">
			<Original>Player Inventory</Original>
		</Key>
		<Key ID="STR_Trunk_Take">
			<Original>Take</Original>
		</Key>
		<Key ID="STR_Trunk_Store">
			<Original>Store</Original>
		</Key>
	</Package>
	<Package name="Wanted_Menu">
		<Key ID="STR_Wanted_Title">
			<Original>APD Wanted List</Original>
		</Key>
		<Key ID="STR_Wanted_Pardon">
			<Original>Pardon</Original>
		</Key>
	</Package>
	<Package name="Animals">
		<Key ID="STR_ANIM_Salema">
			<Original>Salema</Original>
		</Key>
		<Key ID="STR_ANIM_Ornate">
			<Original>Ornate</Original>
		</Key>
		<Key ID="STR_ANIM_Mackerel">
			<Original>Mackerel</Original>
		</Key>
		<Key ID="STR_ANIM_Tuna">
			<Original>Tuna</Original>
		</Key>
		<Key ID="STR_ANIM_Mullet">
			<Original>Mullet</Original>
		</Key>
		<Key ID="STR_ANIM_Catshark">
			<Original>Cat Shark</Original>
		</Key>
		<Key ID="STR_ANIM_Rabbit">
			<Original>Rabbit</Original>
		</Key>
	</Package>
	<Package name="Notifications">
		<Key ID="STR_NOTF_NE_1">
			<Original>You do not have $%1 for a %2</Original>
		</Key>
		<Key ID="STR_NOTF_B_1">
			<Original>You bought a %1 for $%2</Original>
		</Key>
		<Key ID="STR_NOTF_S_1">
			<Original>You sold a %1 for $%2</Original>
		</Key>
		<Key ID="STR_NOTF_Arrested_1">
			<Original>%1 was arrested by %2 for $%3</Original>
		</Key>
		<Key ID="STR_NOTF_KidneyRemoved_1">
			<Original>%1 has had their kidney removed by %2.</Original>
		</Key>
		<Key ID="STR_NOTF_MedicAllowRespawn_1">
			<Original>%1 has set %2 as DNR.</Original>
		</Key>
		<Key ID="STR_NOTF_Fishing">
			<Original>You caught a %1</Original>
		</Key>
		<Key ID="STR_NOTF_CaughtTurtle">
			<Original>You have taken some turtle meat</Original>
		</Key>
		<Key ID="STR_NOTF_Earned_1">
			<Original>You have earned $%1</Original>
		</Key>
		<Key ID="STR_NOTF_NetDrop">
			<Original>Dropping fishing net...</Original>
		</Key>
		<Key ID="STR_NOTF_NetDropFail">
			<Original>Didn't catch any fish...</Original>
		</Key>
		<Key ID="STR_NOTF_NetUp">
			<Original>Fishing net pulled up.</Original>
		</Key>
		<Key ID="STR_NOTF_InvFull">
			<Original>Your inventory space is full.</Original>
		</Key>
		<Key ID="STR_NOTF_Gathering">
			<Original>Gathering %1...</Original>
		</Key>
		<Key ID="STR_NOTF_ChopSoldCar">
			<Original>You have sold a %1 for $%2</Original>
		</Key>
		<Key ID="STR_NOTF_Picked">
			<Original>You have picked %1 %2</Original>
		</Key>
		<Key ID="STR_NOTF_Collected">
			<Original>You have collected some %1</Original>
		</Key>
		<Key ID="STR_NOTF_DPStart">
			<Original>You are to deliver this package to %1</Original>
		</Key>
		<Key ID="STR_NOTF_DPTask">
			<Original>Deliver this package to %1</Original>
		</Key>
		<Key ID="STR_NOTF_DPFailed">
			<Original>You failed to deliver the package because you died or re-deployed.</Original>
		</Key>
		<Key ID="STR_NOTF_HS_NoCash">
			<Original>You do not have $%1 to be healed</Original>
		</Key>
		<Key ID="STR_NOTF_HS_Healing">
			<Original>Please stay still</Original>
		</Key>
		<Key ID="STR_NOTF_HS_ToFar">
			<Original>You need to be within 5m while the doctor is healing you</Original>
		</Key>
		<Key ID="STR_NOTF_HS_Healed">
			<Original>You are now fully healed.</Original>
		</Key>
		<Key ID="STR_NOTF_HS_Dopamine">
			<Original>You have received dopamine.</Original>
		</Key>
		<Key ID="STR_NOTF_Impounding">
			<Original>Impounding Vehicle</Original>
		</Key>
		<Key ID="STR_NOTF_ImpoundingCancelled">
			<Original>Impounding has been cancelled.</Original>
		</Key>
		<Key ID="STR_NOTF_Impounded">
			<Original>You have impounded a %1\n\nYou have received $%2 for cleaning up the streets!</Original>
		</Key>
		<Key ID="STR_NOTF_HasImpounded">
			<Original>%1 has impounded %2's %3</Original>
		</Key>
		<Key ID="STR_NOTF_HasRecovered">
			<Original>%1 has recovered %2's stolen %3</Original>
		</Key>
		<Key ID="STR_NOTF_AbortESC">
			<Original>Abort available in %1</Original>
		</Key>
		<Key ID="STR_NOTF_RespawnESC">
			<Original>Respawn available in %1</Original>
		</Key>
		<Key ID="STR_NOTF_DeviceIsMining">
			<Original>This vehicle is already mining</Original>
		</Key>
		<Key ID="STR_NOTF_DeviceFull">
			<Original>The vehicle is full</Original>
		</Key>
		<Key ID="STR_NOTF_notNearResource">
			<Original>You are not near a resource field</Original>
		</Key>
		<Key ID="STR_NOTF_MiningStopped">
			<Original>You cannot turn the vehicle on when mining</Original>
		</Key>
		<Key ID="STR_NOTF_DeviceMining">
			<Original>The Device is mining...</Original>
		</Key>
		<Key ID="STR_NOTF_DeviceMined">
			<Original>Completed cycle, the device has mined %1</Original>
		</Key>
		<Key ID="STR_NOTF_OutOfFuel">
			<Original>Vehicle is out of fuel</Original>
		</Key>
		<Key ID="STR_NOTF_SpikeStrip">
			<Original>You have packed up the spike strip.</Original>
		</Key>
		<Key ID="STR_NOTF_PickedEvidence">
			<Original>%1 has been placed in evidence, you have received $%2 as a reward.</Original>
		</Key>
		<Key ID="STR_NOTF_PickedMoney">
			<Original>You have picked up $%1</Original>
		</Key>
		<Key ID="STR_NOTF_Bail_Post">
			<Original>You must wait at least 3 minutes in jail before paying a bail.</Original>
		</Key>
		<Key ID="STR_NOTF_Bail_NotEnough">
			<Original>You do not have $%1 in your bank account to pay bail.</Original>
		</Key>
		<Key ID="STR_NOTF_Bail_Bailed">
			<Original>%1 has posted bail!</Original>
		</Key>
		<Key ID="STR_NOTF_VehicleNear">
			<Original>There isn't a vehicle nearby...</Original>
		</Key>
		<Key ID="STR_NOTF_Restrained">
			<Original>%1 was restrained by %2</Original>
		</Key>
		<Key ID="STR_NOTF_LicenseSearch">
			<Original>Officer %1 is checking your licenses.</Original>
		</Key>
		<Key ID="STR_NOTF_Searching">
			<Original>Searching...</Original>
		</Key>
		<Key ID="STR_NOTF_SearchVehFail">
			<Original>Couldn't search the vehicle</Original>
		</Key>
		<Key ID="STR_NOTF_VehCheat">
			<Original>This vehicle has no information, it was probably spawned in through cheats. \n\nDeleting vehicle.</Original>
		</Key>
		<Key ID="STR_NOTF_Action">
			<Original>You are already doing an action. Please wait for it to end.</Original>
		</Key>
		<Key ID="STR_NOTF_Unrestrain">
			<Original>%1 was unrestrained by %2</Original>
		</Key>
		<Key ID="STR_NOTF_Robbed">
			<Original>%1 has robbed %2 for $%3</Original>
		</Key>
		<Key ID="STR_NOTF_RobFail">
			<Original>%1 doesn't have any money.</Original>
		</Key>
		<Key ID="STR_NOTF_SeizeCashFail">
			<Original>%1 doesn't have any cash on hand.</Original>
		</Key>
		<Key ID="STR_NOTF_Tazed">
			<Original>%1 was tased by %2</Original>
		</Key>
		<Key ID="STR_NOTF_EnemyTaze">
			<Original>%1 was tased by enemy gang member %2</Original>
		</Key>
		<Key ID="STR_NOTF_VehContraband">
			<Original>A vehicle was searched and has $%1 worth of drugs / contraband.</Original>
		</Key>
		<Key ID="STR_NOTF_PulledOut">
			<Original>You have been pulled out of the vehicle</Original>
		</Key>
		<Key ID="STR_NOTF_GivenItem">
			<Original>%1 has gave you %2 %3</Original>
		</Key>
		<Key ID="STR_NOTF_GivenMoney">
			<Original>%1 has given you $%2</Original>
		</Key>
		<Key ID="STR_NOTF_SendingData">
			<Original>Sending information to server please wait.....</Original>
		</Key>
		<Key ID="STR_NOTF_StrippingPlayer">
			<Original>Removing %1 from %2 </Original>
		</Key>
		<Key ID="STR_NOTF_ActionCancel">
			<Original>Action Cancelled</Original>
		</Key>
		<Key ID="STR_NOTF_ActionInProc">
			<Original>An action is already being processed...</Original>
		</Key>
		<Key ID="STR_NOTF_NoSpace">
			<Original>You don't have enough space for that amount!</Original>
		</Key>
		<Key ID="STR_NOTF_NotAlive">
			<Original>That player isn't alive. Please try again later.</Original>
		</Key>
		<Key ID="STR_NOTF_NotEnoughMoney">
			<Original>You don't have that much money!</Original>
		</Key>
		<Key ID="STR_NOTF_NotACop">
			<Original>You are not a cop.</Original>
		</Key>
		<Key ID="STR_NOTF_NoRoom">
			<Original>You don't have enough room for that item.</Original>
		</Key>
		<Key ID="STR_NOTF_ProcessWOLicense">
 			<Original>You need $%1 to process without a license!</Original>

 		</Key>
 		<Key ID="STR_NOTF_RapidAction">
 			<Original>You can't rapidly use action keys!</Original>

 		</Key>
 		<Key ID="STR_NOTF_NotEnoughFunds">
 			<Original>You do not have enough funds in your bank account</Original>

 		</Key>
 		<Key ID="STR_NOTF_CouldntAdd">
 			<Original>Couldn't add it to your inventory.</Original>

 		</Key>
		<Key ID="STR_NOTF_EatMSG_1">
 			<Original>You haven't eaten anything in awhile, You should find something to eat soon!</Original>

 		</Key>
 		<Key ID="STR_NOTF_EatMSG_2">
 			<Original>You are starting to starve, you need to find something to eat otherwise you will die.</Original>

 		</Key>
 		<Key ID="STR_NOTF_EatMSG_3">
 			<Original>You will die very soon if you don't eat something</Original>

 		</Key>
 		<Key ID="STR_NOTF_EatMSG_Death">
 			<Original>You are starving to death!</Original>

 		</Key>
 		<Key ID="STR_NOTF_DrinkMSG_1">
 			<Original>You haven't drank anything in awhile, You should find something to drink soon.</Original>

 		</Key>
 		<Key ID="STR_NOTF_DrinkMSG_2">
 			<Original>You haven't drank anything in along time, you should find something to drink soon or you'll start to die from dehydration.</Original>

 		</Key>
 		<Key ID="STR_NOTF_DrinkMSG_3">
 			<Original>You are now suffering from severe dehydration find something to drink quickly!</Original>

 		</Key>
 		<Key ID="STR_NOTF_DrinkMSG_Death">
 			<Original>You are dying from dehydration!</Original>

 		</Key>
 		<Key ID="STR_NOTF_MaxWeight">
 			<Original>You are over carrying your max weight! You will not be able to run or move fast till you drop some items!</Original>

 		</Key>
 		<Key ID="STR_NOTF_SearchVeh">
 			<Original>&lt;t color='#FF0000'&gt;&lt;t size='2'&gt;Vehicle Info&lt;/t&gt;&lt;/t&gt;&lt;br/&gt;&lt;t color='#FFD700'&gt;&lt;t size='1.5'&gt;Owners&lt;/t&gt;&lt;/t&gt;&lt;br/&gt; %1</Original>

 		</Key>
 		<Key ID="STR_NOTF_SearchVehGang">
 			<Original>&lt;t color='#FF0000'&gt;&lt;t size='2'&gt;Vehicle Info&lt;/t&gt;&lt;/t&gt;&lt;br/&gt;&lt;t color='#FFD700'&gt;&lt;t size='1.5'&gt;Owners&lt;/t&gt;&lt;/t&gt;&lt;br/&gt; %1 &lt;br/&gt;&lt;br/&gt;&lt;t color='#FF0000'&gt;&lt;t size='1.5'&gt;GANG VEHICLE&lt;/t&gt;&lt;/t&gt;&lt;</Original>

 		</Key>
 		<Key ID="STR_NOTF_CannotSearchPerson">
			<Original>Cannot search that person.</Original>
		</Key>
		<Key ID="STR_NOTF_Repairing">
 			<Original>Repairing %1</Original>

 		</Key>
 		<Key ID="STR_NOTF_RepairingInVehicle">
 			<Original>You must be outside of the vehicle to fix it. Sorry that this prevents you from exploiting the system, well not really.</Original>

 		</Key>
 		<Key ID="STR_NOTF_RepairedVehicle">
 			<Original>You have repaired that vehicle.</Original>

 		</Key>
 		<Key ID="STR_NOTF_Gather_Success">
			<Original>You have gathered %2 %1</Original>
		</Key>
		<Key ID="STR_NOTF_GatherVeh">
			<Original>You can't gather from inside a car!</Original>
		</Key>
		<Key ID="STR_NOTF_NoLootingPerson">
			<Original>You are not allowed to loot dead bodies</Original>
		</Key>
	</Package>
	<Package name="Jail_Strings">
		<Key ID="STR_Jail_Warn">
			<Original>You have been arrested, wait your time out. If you attempt to respawn or reconnect your time will increase!</Original>
		</Key>
		<Key ID="STR_Jail_LicenseNOTF">
			<Original>For being arrested you have lost the following licenses if you own them\n\nFirearms License\nWPL License\nDriver License\nVigilante License</Original>
		</Key>
		<Key ID="STR_Jail_EscapeNOTF">
			<Original>%1 has escaped from jail!</Original>
		</Key>
		<Key ID="STR_Jail_EscapeSelf">
			<Original>You have escaped from jail, you still retain your previous crimes and now have a count of escaping jail.</Original>
		</Key>
		<Key ID="STR_Jail_Released">
			<Original>You have served your time in jail and have been released.</Original>
		</Key>
		<Key ID="STR_Jail_Time">
			<Original>Time Remaining:</Original>
		</Key>
		<Key ID="STR_Jail_Pay">
			<Original>Can pay bail:</Original>
		</Key>
		<Key ID="STR_Jail_Price">
			<Original>Bail Price:</Original>
		</Key>
		<Key ID="STR_Jail_Paid">
			<Original>You have paid your bail and are now free.</Original>
		</Key>
	</Package>
	<Package name="Civ_Strings">
		<Key ID="STR_Civ_KnockedOut">
			<Original>%1 has knocked you out.</Original>
		</Key>
		<Key ID="STR_Civ_LicenseRemove_1">
			<Original>You have lost all your motor vehicle licenses for vehicular manslaughter.</Original>
		</Key>
		<Key ID="STR_Civ_LicenseRemove_2">
			<Original>You have lost your firearms license for manslaughter.</Original>
		</Key>
		<Key ID="STR_Civ_RobFail">
			<Original>They didn't have any cash...</Original>
		</Key>
		<Key ID="STR_Civ_Robbed">
			<Original>You stole $%1</Original>
		</Key>
		<Key ID="STR_Civ_VaultEmpty">
			<Original>The safe is empty!</Original>
		</Key>
		<Key ID="STR_Civ_VaultInUse">
			<Original>Someone is already accessing the safe..</Original>
		</Key>
		<Key ID="STR_Civ_NotEnoughCops">
			<Original>There needs to be 5 or more cops online to continue.</Original>
		</Key>
		<Key ID="STR_Civ_SafeInv">
			<Original>Safe Inventory</Original>
		</Key>
		<Key ID="STR_Civ_SelectItem">
			<Original>You need to select an item!</Original>
		</Key>
		<Key ID="STR_Civ_IsntEnoughGold">
			<Original>There isn't %1 gold bar(s) in the safe!</Original>
		</Key>
	</Package>
	<Package name="Cop_Strings">
		<Key ID="STR_Cop_TicketError">
			<Original>Couldn't open the ticketing interface</Original>
		</Key>
		<Key ID="STR_Cop_Ticket">
			<Original>Ticketing %1</Original>
		</Key>
		<Key ID="STR_Cop_TicketNil">
			<Original>Person to ticket is nil</Original>
		</Key>
		<Key ID="STR_Cop_TicketExist">
			<Original>Person to ticket doesn't exist.</Original>
		</Key>
		<Key ID="STR_Cop_TicketNum">
			<Original>You didn't enter actual number.</Original>
		</Key>
		<Key ID="STR_Cop_TicketOver100">
			<Original>Tickets can not be more than $200,000!</Original>
		</Key>
		<Key ID="STR_Cop_TicketGive">
			<Original>%1 gave a ticket of $%2 to %3</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_NotEnough">
			<Original>You don't have enough money in your bank account or on you to pay the ticket.</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_NotEnoughNOTF">
			<Original>%1 couldn't pay the ticket due to not having enough money.</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_Paid">
			<Original>You have paid the ticket of $%1</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_PaidNOTF">
			<Original>%1 paid the ticket of $%2</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_PaidNOTF_2">
			<Original>%1 paid the ticket.</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_GUI_Given">
			<Original>%1 has given you a ticket for $%2. You currently have $%3 in your bank.</Original>
		</Key>
		<Key ID="STR_Cop_Ticket_Refuse">
			<Original>%1 refused to pay the ticket.</Original>
		</Key>
		<Key ID="STR_Cop_BountyRecieve">
			<Original>You have collected a bounty of $%1 for arresting a criminal.</Original>
		</Key>
		<Key ID="STR_Cop_BountyKill">
			<Original>You have collected a bounty of $%1 for killing a wanted criminal</Original>
		</Key>
		<Key ID="STR_Cop_Contraband">
			<Original>%1 has $%2 worth of contraband on them.</Original>
		</Key>
		<Key ID="STR_Cop_IllegalItems">
			<Original>Illegal Items</Original>
		</Key>
		<Key ID="STR_Cop_Robber">
			<Original>%1 was identified as the gas station robber!</Original>
		</Key>
		<Key ID="STR_Cop_NoIllegal">
			<Original>No illegal items</Original>
		</Key>
		<Key ID="STR_Cop_NotaDoor">
			<Original>You are not near a door!</Original>
		</Key>
		<Key ID="STR_Cop_EnablePiP">
			<Original>You need to enable Picture in Picture (PiP) through your video settings to use this!</Original>
		</Key>
		<Key ID="STR_Cop_Licenses">
			<Original>Licenses:</Original>
		</Key>
		<Key ID="STR_Cop_Vigi_Tier">
			<Original>Vigi Tier:</Original>
		</Key>
		<Key ID="STR_Cop_NoLicenses">
			<Original>No Licenses</Original>
		</Key>
		<Key ID="STR_Cop_NoIllegalVeh">
			<Original>Nothing illegal in this vehicle</Original>
		</Key>
		<Key ID="STR_Cop_VehEmpty">
			<Original>This vehicle is empty</Original>
		</Key>
		<Key ID="STR_Cop_RobberDead">
			<Original>$%1 from the Federal Reserve robbery was returned from the robber being killed.</Original>
		</Key>
		<Key ID="STR_Cop_RepairVault">
			<Original>Repairing vault...</Original>
		</Key>
		<Key ID="STR_Cop_VaultRepaired">
			<Original>The vault is now fixed and re-secured.</Original>
		</Key>
		<Key ID="STR_Cop_VaultLocked">
			<Original>The vault is already locked?</Original>
		</Key>
		<Key ID="STR_Cop_VaultUnder1">
			<Original>You can't enter anything below 1!</Original>
		</Key>
		<Key ID="STR_Cop_OnlyGold">
			<Original>You can't store anything but gold bars in the safe.</Original>
		</Key>
		<Key ID="STR_Cop_NotEnoughGold">
			<Original>You don't have %1 gold bar(s)</Original>
		</Key>
		<Key ID="STR_Cop_CantRemove">
			<Original>Couldn't remove the item(s) from your inventory to put in the safe.</Original>
		</Key>
		<Key ID="STR_Cop_CouldntSearch">
			<Original>Couldn't search %1</Original>
		</Key>
		<Key ID="STR_Cop_RepairingDoor">
			<Original>Repairing Door...</Original>
		</Key>
		<Key ID="STR_Cop_NoLicensesFound">
			<Original>No Licenses&lt;br/&gt;</Original>
		</Key>
		<Key ID="STR_Cop_DealerQuestion">
			<Original>No one has sold to this dealer recently.</Original>
		</Key>
		<Key ID="STR_Cop_DealerMSG">
			<Original>The following people have been selling to this dealer recently.</Original>
		</Key>
		<Key ID="STR_Cop_Radar">
			<Original>Radar</Original>
		</Key>
		<Key ID="STR_Cop_VehSpeed">
			<Original>Vehicle Speed %1 km/h</Original>
		</Key>
		<Key ID="STR_Cop_ExcessiveRestrain">
			<Original>You have been released automatically for excessive restrainment time</Original>
		</Key>
		<Key ID="STR_Cop_Retrained">
			<Original>You have been restrained by %1</Original>
		</Key>
	</Package>
	<Package name="MISC_Strings">
		<Key ID="STR_MISC_TooMuch">
			<Original>You tried to give %1 %2 %3 but they couldn't hold that so it was returned.</Original>
		</Key>
		<Key ID="STR_MISC_TooMuch_2">
			<Original>%1 returned %2 %3 because they couldn't hold that amount.</Original>
		</Key>
		<Key ID="STR_MISC_TooMuch_3">
			<Original>%1 has gave you %2 but you can only hold %3 so %4 was returned back.</Original>
		</Key>
		<Key ID="STR_MISC_AttachmentMSG">
			<Original>Do you want to add this item to your weapon or inventory? If you add it to your weapon your current existing attachment will be lost!</Original>
		</Key>
		<Key ID="STR_MISC_Attachment">
			<Original>Attachment slot taken!</Original>
		</Key>
		<Key ID="STR_MISC_Weapon">
			<Original>Weapon</Original>
		</Key>
		<Key ID="STR_MISC_Inventory">
			<Original>Inventory</Original>
		</Key>
		<Key ID="STR_MISC_Backpack">
			<Original>You are not allowed to look into someone's backpack!</Original>
		</Key>
		<Key ID="STR_MISC_VehInventory">
			<Original>You are not allowed to access this vehicle while its locked.</Original>
		</Key>
		<Key ID="STR_MISC_VehInvUse">
			<Original>This vehicle's trunk is in use, only one person can use it at a time.</Original>
		</Key>
		<Key ID="STR_MISC_DialogError">
			<Original>Failed Creating Dialog</Original>
		</Key>
		<Key ID="STR_MISC_VehUnlock">
			<Original>You have unlocked your vehicle.</Original>
		</Key>
		<Key ID="STR_MISC_VehLock">
			<Original>You have locked your vehicle.</Original>
		</Key>
		<Key ID="STR_MISC_WailON">
			<Original>Primary Siren On</Original>
		</Key>
		<Key ID="STR_MISC_WailOFF">
			<Original>Primary Siren Off</Original>
		</Key>
		<Key ID="STR_MISC_YelpON">
			<Original>Secondary Siren On</Original>
		</Key>
		<Key ID="STR_MISC_YelpOFF">
			<Original>Secondary Siren Off</Original>
		</Key>
		<Key ID="STR_MISC_LightsOn">
			<Original>Lights On</Original>
		</Key>
		<Key ID="STR_MISC_LightsOff">
			<Original>Lights Off</Original>
		</Key>
		<Key ID="STR_MISC_NoStorageWarn">
			<Original>You need to install storage containers to have storing capabilities!</Original>
		</Key>
		<Key ID="STR_MISC_NoStorageVeh">
			<Original>This vehicle isn't capable of storing virtual items.</Original>
		</Key>
		<Key ID="STR_MISC_Weight">
			<Original>Weight:</Original>
		</Key>
		<Key ID="STR_MISC_HouseStorage">
			<Original>House Storage</Original>
		</Key>
		<Key ID="STR_MISC_VehStorage">
			<Original>Vehicle Trunk</Original>
		</Key>
		<Key ID="STR_MISC_VehDoesntExist">
			<Original>The vehicle either doesn't exist or is destroyed.</Original>
		</Key>
		<Key ID="STR_MISC_WrongNumFormat">
			<Original>Invalid Number format</Original>
		</Key>
		<Key ID="STR_MISC_Under1">
			<Original>You can't enter anything below 1!</Original>
		</Key>
		<Key ID="STR_MISC_NotEnough">
			<Original>The vehicle doesn't have that many of that item.</Original>
		</Key>
	</Package>
	<Package name="Housing_Strings">
		<Key ID="STR_House_ContainerError">
			<Original>Error saving container, couldn't locate house?</Original>
		</Key>
		<Key ID="STR_House_ContainerDeny">
			<Original>You are not allowed to access this storage container without the owner opening it.</Original>
		</Key>
		<Key ID="STR_House_Door_Unlock">
			<Original>You have unlocked the door.</Original>
		</Key>
		<Key ID="STR_House_Door_Lock">
			<Original>You have locked the door.</Original>
		</Key>
		<Key ID="STR_House_Door_NotNear">
			<Original>You are not near a door!</Original>
		</Key>
		<Key ID="STR_House_Sell_Process">
			<Original>This house was recently sold and is still processing in the database.</Original>
		</Key>
		<Key ID="STR_House_License">
			<Original>You do not have a home owners license!</Original>
		</Key>
		<Key ID="STR_House_Max_House">
			<Original>You can only own %1 houses at a time.</Original>
		</Key>
		<Key ID="STR_House_NotEnough">
			<Original>You do not have enough money!</Original>
		</Key>
		<Key ID="STR_House_BuyMSG">
			<Original>This house is available for &lt;t color='#8cff9b'&gt;$%1&lt;/t&gt;&lt;br/&gt;It supports up to %2 storage containers</Original>
		</Key>
		<Key ID="STR_House_Purchase">
			<Original>Purchase House</Original>
		</Key>
		<Key ID="STR_House_Raid_NoOwner">
			<Original>This house doesn't belong to anyone.</Original>
		</Key>
		<Key ID="STR_House_Raid_OwnerOff">
			<Original>This person is not online there for you cannot raid their house!</Original>
		</Key>
		<Key ID="STR_House_Raid_DoorUnlocked">
			<Original>The door is already unlocked!</Original>
		</Key>
		<Key ID="STR_House_Raid_Progress">
			<Original>Breaking lock on door</Original>
		</Key>
		<Key ID="STR_House_Raid_NOTF">
			<Original>%1 your house is being raided by the cops!</Original>
		</Key>
		<Key ID="STR_House_Raid_Nothing">
			<Original>There is nothing in this house.</Original>
		</Key>
		<Key ID="STR_House_Raid_Searching">
			<Original>Searching House...</Original>
		</Key>
		<Key ID="STR_House_Raid_TooFar">
			<Original>You went too far away from the house!</Original>
		</Key>
		<Key ID="STR_House_Raid_Successful">
			<Original>A house was raided and had $%1 worth of drugs / contraband.</Original>
		</Key>
		<Key ID="STR_GangBldg_Raid_Successful">
			<Original>A gang building was raided and had $%1 worth of drugs / contraband.</Original>
		</Key>
		<Key ID="STR_House_Raid_NoIllegal">
			<Original>Nothing illegal in this house.</Original>
		</Key>
		<Key ID="STR_House_LockingUp">
			<Original>Locking up house please wait...</Original>
		</Key>
		<Key ID="STR_House_LockedUp">
			<Original>House has been locked up.</Original>
		</Key>
		<Key ID="STR_House_SellHouseMSG">
			<Original>Are you sure you want to sell your house? It will sell for: &lt;t color='#8cff9b'&gt;$%1&lt;/t&gt; plus a percentage of upgrades.</Original>
		</Key>
		<Key ID="STR_Property_UpgradeStorageMSG">
			<Original>Are you sure you want to upgrade your house's virtual storage capacity to %1? It will cost you: &lt;t color='#8cff9b'&gt;$%2&lt;/t&gt;</Original>
		</Key>
		<Key ID="STR_Property_UpgradePhysicalStorageMSG">
			<Original>Are you sure you want to upgrade your house's physical storage capacity to %1? It will cost you: &lt;t color='#8cff9b'&gt;$%2&lt;/t&gt;</Original>
		</Key>
	</Package>
	<Package name="Vehicle_Service">
		<Key ID="STR_Service_Chopper_NoAir">
			<Original>There isn't a chopper on the helipad!</Original>
		</Key>
		<Key ID="STR_Serive_Chopper_NotEnough">
			<Original>You need $1,000 to service your chopper</Original>
		</Key>
		<Key ID="STR_Service_Chopper_Servicing">
			<Original>Servicing Chopper</Original>
		</Key>
		<Key ID="STR_Service_Chopper_Missing">
			<Original>The vehicle is no longer alive or on the helipad!</Original>
		</Key>
		<Key ID="STR_Service_Chopper_Done">
			<Original>Your chopper is now repaired and refuelled.</Original>
		</Key>
	</Package>
	<Package name="Life_Items">
		<Key ID="STR_Item_Cream">
			<Original>Ice Cream</Original>
		</Key>
		<Key ID="STR_Item_Potato">
			<Original>Potato</Original>
		</Key>
		<Key ID="STR_Item_Potatos">
			<Original>Potatos</Original>
		</Key>
		<Key ID="STR_Item_Cannabis">
			<Original>Cannabis</Original>
		</Key>
		<Key ID="STR_Item_Apple">
			<Original>Apple</Original>
		</Key>
		<Key ID="STR_Item_Apples">
			<Original>Apples</Original>
		</Key>
		<Key ID="STR_Item_Cocaine">
			<Original>Cocaine</Original>
		</Key>
		<Key ID="STR_Item_Heroin">
			<Original>Heroin</Original>
		</Key>
		<Key ID="STR_Item_Oil">
			<Original>Oil</Original>
		</Key>
		<Key ID="STR_Item_Peach">
			<Original>Peach</Original>
		</Key>
		<Key ID="STR_Item_Peaches">
			<Original>Peaches</Original>
		</Key>
		<Key ID="STR_Item_OilU">
			<Original>Unprocessed Oil</Original>
		</Key>
		<Key ID="STR_Item_OilP">
			<Original>Processed Oil</Original>
		</Key>
		<Key ID="STR_Item_HeroinU">
			<Original>Unprocessed Heroin</Original>
		</Key>
		<Key ID="STR_Item_HeroinP">
			<Original>Processed Heroin</Original>
		</Key>
		<Key ID="STR_Item_Marijuana">
			<Original>Marijuana</Original>
		</Key>
		<Key ID="STR_Item_RabbitMeat">
			<Original>Rabbit Meat</Original>
		</Key>
		<Key ID="STR_Item_SalemaMeat">
			<Original>Salema Meat</Original>
		</Key>
		<Key ID="STR_Item_OrnateMeat">
			<Original>Ornate Meat</Original>
		</Key>
		<Key ID="STR_Item_MackerelMeat">
			<Original>Mackerel Meat</Original>
		</Key>
		<Key ID="STR_Item_TunaMeat">
			<Original>Tuna Meat</Original>
		</Key>
		<Key ID="STR_Item_MulletMeat">
			<Original>Mullet Meat</Original>
		</Key>
		<Key ID="STR_Item_CatSharkMeat">
			<Original>Cat Shark Meat</Original>
		</Key>
		<Key ID="STR_Item_TurtleMeat">
			<Original>Turtle Meat</Original>
		</Key>
		<Key ID="STR_Item_FishingPole">
			<Original>Fishing Pole</Original>
		</Key>
		<Key ID="STR_Item_WaterBottle">
			<Original>Water Bottle</Original>
		</Key>
		<Key ID="STR_Item_Coffee">
			<Original>Coffee</Original>
		</Key>
		<Key ID="STR_Item_TurtleSoup">
			<Original>Turtle Soup</Original>
		</Key>
		<Key ID="STR_Item_Donuts">
			<Original>Donuts</Original>
		</Key>
		<Key ID="STR_Item_FuelE">
			<Original>Empty Fuel Can</Original>
		</Key>
		<Key ID="STR_Item_FuelF">
			<Original>Full Fuel Can</Original>
		</Key>
		<Key ID="STR_Item_Pickaxe">
			<Original>Pickaxe</Original>
		</Key>
		<Key ID="STR_Item_CopperOre">
			<Original>Copper Ore</Original>
		</Key>
		<Key ID="STR_Item_IronOre">
			<Original>Iron Ore</Original>
		</Key>
		<Key ID="STR_Item_IronIngot">
			<Original>Iron Ingot</Original>
		</Key>
		<Key ID="STR_Item_CopperIngot">
			<Original>Copper Ingot</Original>
		</Key>
		<Key ID="STR_Item_Sand">
			<Original>Sand</Original>
		</Key>
		<Key ID="STR_Item_Salt">
			<Original>Salt</Original>
		</Key>
		<Key ID="STR_Item_SaltR">
			<Original>Refined Salt</Original>
		</Key>
		<Key ID="STR_Item_Glass">
			<Original>Glass</Original>
		</Key>
		<Key ID="STR_Item_DiamondC">
			<Original>Diamond Cut</Original>
		</Key>
		<Key ID="STR_Item_DiamondU">
			<Original>Diamond Uncut</Original>
		</Key>
		<Key ID="STR_Item_TBacon">
			<Original>Tactical Bacon</Original>
		</Key>
		<Key ID="STR_Item_RedGull">
			<Original>RedGull</Original>
		</Key>
		<Key ID="STR_Item_Lockpick">
			<Original>Lockpick</Original>
		</Key>
		<Key ID="STR_Item_Rock">
			<Original>Rock</Original>
		</Key>
		<Key ID="STR_Item_CementBag">
			<Original>Cement Bag</Original>
		</Key>
		<Key ID="STR_Item_GoldBar">
			<Original>Gold Bar</Original>
		</Key>
		<Key ID="STR_Item_BCharge">
			<Original>Blasting Charge</Original>
		</Key>
		<Key ID="STR_Item_BCutter">
			<Original>Bolt Cutter</Original>
		</Key>
		<Key ID="STR_Item_FAxe">
			<Original>Fireaxe</Original>
		</Key>
		<Key ID="STR_Item_DefuseKit">
			<Original>Bomb Defuse Kit</Original>
		</Key>
		<Key ID="STR_Item_StorageBS">
			<Original>Small Storage Box</Original>
		</Key>
		<Key ID="STR_Item_StorageBL">
			<Original>Large Storage Box</Original>
		</Key>
		<Key ID="STR_Item_CocaineU">
			<Original>Unprocessed Cocaine</Original>
		</Key>
		<Key ID="STR_Item_CocaineP">
			<Original>Processed Cocaine</Original>
		</Key>
		<Key ID="STR_Item_SpikeStrip">
			<Original>Spike Strip</Original>
		</Key>
	</Package>
	<Package name="Life_License">
		<Key ID="STR_License_Driver">
			<Original>Driver License</Original>
		</Key>
		<Key ID="STR_License_Pilot">
			<Original>Pilot License</Original>
		</Key>
		<Key ID="STR_License_Heroin">
			<Original>Heroin Training</Original>
		</Key>
		<Key ID="STR_License_Oil">
			<Original>Oil Processing</Original>
		</Key>
		<Key ID="STR_License_Diving">
			<Original>Diving License</Original>
		</Key>
		<Key ID="STR_License_Boat">
			<Original>Boating License</Original>
		</Key>
		<Key ID="STR_License_Firearm">
			<Original>Firearm License</Original>
		</Key>
		<Key ID="STR_License_WPL">
			<Original>Workers Protection License</Original>
		</Key>
		<Key ID="STR_License_Swat">
			<Original>SWAT License</Original>
		</Key>
		<Key ID="STR_License_CG">
			<Original>Coast Guard License</Original>
		</Key>
		<Key ID="STR_License_MCG">
			<Original>Coast Guard License</Original>
		</Key>
		<Key ID="STR_License_Rebel">
			<Original>Rebel Training</Original>
		</Key>
		<Key ID="STR_License_Truck">
			<Original>Truck License</Original>
		</Key>
		<Key ID="STR_License_Diamond">
			<Original>Diamond Processing</Original>
		</Key>
		<Key ID="STR_License_Copper">
			<Original>Copper Processing</Original>
		</Key>
		<Key ID="STR_License_Iron">
			<Original>Iron Processing</Original>
		</Key>
		<Key ID="STR_License_Sand">
			<Original>Sand Processing</Original>
		</Key>
		<Key ID="STR_License_Salt">
			<Original>Salt Processing</Original>
		</Key>
		<Key ID="STR_License_Cocaine">
			<Original>Cocaine Training</Original>
		</Key>
		<Key ID="STR_License_Marijuana">
			<Original>Marijuana Training</Original>
		</Key>
		<Key ID="STR_License_Cement">
			<Original>Cement Mixing License</Original>
		</Key>
		<Key ID="STR_License_Home">
			<Original>Home Owners License</Original>
		</Key>
	</Package>
	<Package name="More_Item_Strings">
		<Key ID="STR_ISTR_Blast_VaultOnly">
			<Original>This can only be used on a vault or prison door.</Original>
		</Key>
		<Key ID="STR_ISTR_Blast_AlreadyPlaced">
			<Original>There is already a charge placed on this.</Original>
		</Key>
		<Key ID="STR_ISTR_Blast_AlreadyOpen">
			<Original>This is already open.</Original>
		</Key>
		<Key ID="STR_ISTR_Blast_Placed">
			<Original>A blasting charge has been placed on the federal reserves vault, You have till the clock runs out to disarm the charge!</Original>
		</Key>
		<Key ID="STR_ISTR_Blast_KeepOff">
			<Original>The timer is ticking! Keep the cops away from the door!</Original>
		</Key>
		<Key ID="STR_ISTR_Blast_Disarmed">
			<Original>The charge has been disarmed!</Original>
		</Key>
		<Key ID="STR_ISTR_Blast_Opened">
			<Original>The vault is now opened</Original>
		</Key>
		<Key ID="STR_ISTR_Bolt_NotNear">
			<Original>You are not looking at a house door.</Original>
		</Key>
		<Key ID="STR_ISTR_Bolt_AlertFed">
			<Original>!!! SOMEONE IS BREAKING INTO THE FEDERAL RESERVE !!!</Original>
		</Key>
		<Key ID="STR_ISTR_Bolt_AlertHouse">
			<Original>%1 was seen breaking into a house.</Original>
		</Key>
		<Key ID="STR_ISTR_FirAx_AlertHouse">
			<Original>%1 is using his fireaxe to enter a house.</Original>
		</Key>
		<Key ID="STR_ISTR_Bolt_Process">
			<Original>Cutting lock on door</Original>
		</Key>
		<Key ID="STR_ISTR_Axe_Process">
			<Original>Axing the door open</Original>
		</Key>
		<Key ID="STR_ISTR_Defuse_Nothing">
			<Original>There is no charge on this?</Original>
		</Key>
		<Key ID="STR_ISTR_Defuse_Process">
			<Original>Defusing charge...</Original>
		</Key>
		<Key ID="STR_ISTR_Defuse_Success">
			<Original>The charge has been defused</Original>
		</Key>
		<Key ID="STR_ISTR_Jerry_NotLooking">
			<Original>You need to look at the vehicle you want to refuel!</Original>
		</Key>
		<Key ID="STR_ISTR_Jerry_NotNear">
			<Original>You need to be closer to the vehicle!</Original>
		</Key>
		<Key ID="STR_ISTR_Jerry_Process">
			<Original>Refuelling %1</Original>
		</Key>
		<Key ID="STR_ISTR_Jerry_Success">
			<Original>You have refuelled that %1</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_AlreadyHave">
			<Original>This vehicle is already in your key-chain.</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_Process">
			<Original>Lock-picking %1</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_TooFar">
			<Original>You got to far away from the target.</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_InCar">
			<Original>You cannot lockpick while inside a vehicle.</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_Success">
			<Original>You now have keys to this vehicle.</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_Failed">
			<Original>The lockpick broke.</Original>
		</Key>
		<Key ID="STR_ISTR_Lock_FailedNOTF">
			<Original>%1 was seen trying to lockpick a car.</Original>
		</Key>
		<Key ID="STR_ISTR_SlimJim_AlreadyHave">
			<Original>This vehicle is already in your key-chain.</Original>
		</Key>
		<Key ID="STR_ISTR_SlimJim_Process">
			<Original>Unlocking %1</Original>
		</Key>
		<Key ID="STR_ISTR_SlimJim_TooFar">
			<Original>You got to far away from the target.</Original>
		</Key>
		<Key ID="STR_ISTR_SlimJim_InCar">
			<Original>You cannot use a Slim Jim while inside a vehicle.</Original>
		</Key>
		<Key ID="STR_ISTR_SlimJim_Success">
			<Original>You now have keys to this vehicle.</Original>
		</Key>
		<Key ID="STR_ISTR_Pick_NotNear">
			<Original>You are not near a mine!</Original>
		</Key>
		<Key ID="STR_ISTR_Pick_MineVeh">
			<Original>You can't mine from inside a car!</Original>
		</Key>
		<Key ID="STR_ISTR_Pick_Success">
			<Original>You have mined %2 %1</Original>
		</Key>
		<Key ID="STR_ISTR_Spike_Place">
			<Original>Place Spike Strip</Original>
		</Key>
		<Key ID="STR_ISTR_Spike_Pack">
			<Original>Pack up Spike Strip</Original>
		</Key>
		<Key ID="STR_ISTR_Box_NotinHouse">
			<Original>You need to be inside your house to place this.</Original>
		</Key>
		<Key ID="STR_ISTR_Box_HouseFull">
			<Original>You cannot place any more storage containers in your house.</Original>
		</Key>
		<Key ID="STR_ISTR_Box_HouseFull_2">
			<Original>No more free storage spaces in your house.</Original>
		</Key>
		<Key ID="STR_ISTR_SelectItemFirst">
			<Original>You need to select an item first!</Original>
		</Key>
		<Key ID="STR_ISTR_RedGullEffect">
			<Original>You can now run farther for 3 minutes</Original>
		</Key>
		<Key ID="STR_ISTR_SpikesDeployment">
			<Original>You already have a Spike Strip active in deployment</Original>
		</Key>
		<Key ID="STR_ISTR_RefuelInVehicle">
			<Original>You can't refuel the vehicle while in it!</Original>
		</Key>
		<Key ID="STR_ISTR_NotUsable">
			<Original>This item isn't usable.</Original>
		</Key>
	</Package>
	<Package name="Process_Action">
		<Key ID="STR_Process_Oil">
			<Original>Processing Oil</Original>
		</Key>
		<Key ID="STR_Process_Diamond">
			<Original>Cutting Diamonds</Original>
		</Key>
		<Key ID="STR_Process_Heroin">
			<Original>Processing Heroin</Original>
		</Key>
		<Key ID="STR_Process_Copper">
			<Original>Processing Copper</Original>
		</Key>
		<Key ID="STR_Process_Iron">
			<Original>Processing Iron</Original>
		</Key>
		<Key ID="STR_Process_Sand">
			<Original>Processing Sand</Original>
		</Key>
		<Key ID="STR_Process_Salt">
			<Original>Processing Salt</Original>
		</Key>
		<Key ID="STR_Process_Cocaine">
			<Original>Processing Cocain</Original>
		</Key>
		<Key ID="STR_Process_Marijuana">
			<Original>Processing Marijuana</Original>
		</Key>
		<Key ID="STR_Process_Cement">
			<Original>Mixing Cement</Original>
		</Key>
		<Key ID="STR_Process_Stay">
			<Original>You need to stay within 10m to process.</Original>
		</Key>
		<Key ID="STR_Process_License">
			<Original>You need $%1 to process without a license!</Original>
		</Key>
		<Key ID="STR_Process_Processed">
			<Original>You have processed %1 into %2</Original>
		</Key>
		<Key ID="STR_Process_Processed2">
			<Original>You have processed %1 into %2 for $%3</Original>
		</Key>
	</Package>
	<Package name="Gather_Items">
		<Key ID="STR_Gather_Apples">
			<Original>Gather Apples</Original>
		</Key>
		<Key ID="STR_Gather_Peaches">
			<Original>Gather Peaches</Original>
		</Key>
		<Key ID="STR_Gather_Heroin">
			<Original>Gather Heroin</Original>
		</Key>
		<Key ID="STR_Gather_Cannabis">
			<Original>Gather Cannabis</Original>
		</Key>
		<Key ID="STR_Gather_Cocaine">
			<Original>Gather Cocaine</Original>
		</Key>
	</Package>
	<Package name="Medical_Strings">
		<Key ID="STR_Medic_Online">
			<Original>Medics Online: %1</Original>
		</Key>
		<Key ID="STR_Medic_Near">
			<Original>Medics Nearby: %1</Original>
		</Key>
		<Key ID="STR_Medic_Request">
			<Original>%1 is requesting EMS Revive.</Original>
		</Key>
		<Key ID="STR_Denial_Request">
			<Original>%1 is requesting to be denied.</Original>
		</Key>
		<Key ID="STR_Medic_Respawn">
			<Original>Respawn Available in: %1</Original>
		</Key>
		<Key ID="STR_Medic_Respawn_2">
			<Original>You can now respawn</Original>
		</Key>
		<Key ID="STR_Medic_RevivePay">
			<Original>%1 has revived you and a fee of $%2 was taken from your bank account for their services.</Original>
		</Key>
		<Key ID="STR_Medic_AlreadyReviving">
			<Original>Someone else is already reviving this person</Original>
		</Key>
		<Key ID="STR_Medic_Progress">
			<Original>Reviving %1</Original>
		</Key>
		<Key ID="STR_Dope_Progress">
			<Original>Giving dopamine to %1</Original>
		</Key>
		<Key ID="STR_Medic_RevivedRespawned">
			<Original>This person either respawned or was already revived.</Original>
		</Key>
		<Key ID="STR_Medic_RevivePayReceive">
			<Original>You have revived %1 and received $%2 for your services.</Original>
		</Key>
		<Key ID="STR_Medic_TooFar">
			<Original>You got to far away from the body.</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_NotEnough">
			<Original>You don't have enough money in your bank account or on you to pay the invoice.</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_NotEnoughNOTF">
			<Original>%1 couldn't pay the invoice due to not having enough money.</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_Paid">
			<Original>You have paid the invoice of $%1</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_PaidNOTF">
			<Original>%1 paid the invoice of $%2</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_PaidNOTF_2">
			<Original>%1 paid the invoice.</Original>
		</Key>
		<Key ID="STR_Medic_TicketError">
			<Original>Couldn't open the ticketing interface</Original>
		</Key>
		<Key ID="STR_Medic_Invoice">
			<Original>Writing Invoice For: %1</Original>
		</Key>
		<Key ID="STR_Medic_InvoiceNil">
			<Original>Person to ticket is nil</Original>
		</Key>
		<Key ID="STR_Medic_InvoiceExist">
			<Original>Person to invoice doesn't exist.</Original>
		</Key>
		<Key ID="STR_Medic_InvoiceNum">
			<Original>You didn't enter actual number.</Original>
		</Key>
		<Key ID="STR_Medic_InvoiceGive">
			<Original>%1 gave an invoice of $%2 to %3</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_Refuse">
			<Original>%1 refused to pay the invoice.</Original>
		</Key>
		<Key ID="STR_Medic_Invoice_GUI_Given">
			<Original>%1 has written you an invoice for $%2</Original>
		</Key>
	</Package>
	<Package name="Federal_Reserve">
		<Key ID="STR_NOTF_FRRobbery_fail">
			<Original>The robbery has failed due to unknown reasons</Original>
		</Key>
		<Key ID="STR_NOTF_FRRobbery_Success">
			<Original>$%1 was stolen from the robbery on the federal reserve</Original>
		</Key>
		<Key ID="STR_FEDR_VInUse">
			<Original>This vault is already being robbed by someone else</Original>
		</Key>
		<Key ID="STR_FEDR_VRecently">
			<Original>This vault was already robbed recently</Original>
		</Key>
	</Package>
	<Package name="Other_GUI_Stuff">
		<Key ID="STR_GUI_Garage">
			<Original>Garage</Original>
		</Key>
		<Key ID="STR_GUI_YourVeh">
			<Original>Your Vehicles</Original>
		</Key>
		<Key ID="STR_GUI_VehInfo">
			<Original>Vehicle Information</Original>
		</Key>
		<Key ID="STR_GUI_PlayerReveal">
			<Original>Automatically reveals nearest objects within 15m, turn this setting off if you are experiencing performance issues.</Original>
		</Key>
		<Key ID="STR_GUI_PlayTags">
			<Original>Controls whether or not players will have name tags above their head.</Original>
		</Key>
		<Key ID="STR_GUI_ShopStock">
			<Original>Shop Stock</Original>
		</Key>
	</Package>
	<Package name="Player_Interaction">
		<Key ID="STR_pInAct_Title">
			<Original>Player Interaction Menu</Original>
		</Key>
		<Key ID="STR_pInAct_PutInCar">
			<Original>Put in vehicle</Original>
		</Key>
		<Key ID="STR_pInAct_Unrestrain">
			<Original>Un-Restrain</Original>
		</Key>
		<Key ID="STR_pInAct_checkLicenses">
			<Original>Check Licenses</Original>
		</Key>
		<Key ID="STR_pInAct_SearchPlayer">
			<Original>Search Player</Original>
		</Key>
		<Key ID="STR_pInAct_StopEscort">
			<Original>Stop Escorting</Original>
		</Key>
		<Key ID="STR_pInAct_Escort">
			<Original>Escort Player</Original>
		</Key>
		<Key ID="STR_pInAct_TicketBtn">
			<Original>Ticket Player</Original>
		</Key>
		<Key ID="STR_pInAct_Arrest">
			<Original>Send to jail</Original>
		</Key>
		<Key ID="STR_pInAct_Repair">
			<Original>Repair Door</Original>
		</Key>
		<Key ID="STR_pInAct_CloseOpen">
			<Original>Open / Close</Original>
		</Key>
		<Key ID="STR_pInAct_BreakDown">
			<Original>Break down door</Original>
		</Key>
		<Key ID="STR_pInAct_SearchHouse">
			<Original>Search house</Original>
		</Key>
		<Key ID="STR_pInAct_LockHouse">
			<Original>Lock up house</Original>
		</Key>
		<Key ID="STR_pInAct_BuyHouse">
			<Original>Buy House</Original>
		</Key>
		<Key ID="STR_pInAct_SellGarage">
			<Original>Sell Garage</Original>
		</Key>
		<Key ID="STR_pInAct_AccessGarage">
			<Original>Garage</Original>
		</Key>
		<Key ID="STR_pInAct_StoreVeh">
			<Original>Store Vehicle</Original>
		</Key>
		<Key ID="STR_pInAct_SellHouse">
			<Original>Sell House</Original>
		</Key>
		<Key ID="STR_pInAct_UnlockStorage">
			<Original>Unlock Storage</Original>
		</Key>
		<Key ID="STR_pInAct_LockStorage">
			<Original>Lock Storage</Original>
		</Key>
		<Key ID="STR_pInAct_LightsOff">
			<Original>Turn Lights Off</Original>
		</Key>
		<Key ID="STR_pInAct_LightsOn">
			<Original>Turn Lights On</Original>
		</Key>
		<Key ID="STR_pInAct_DepositToGang">
			<Original>Deposit To Gang</Original>
		</Key>
		<Key ID="STR_pInAct_InvoiceBtn">
			<Original>Invoice Player</Original>
		</Key>
		<Key ID="STR_pInAct_FareBtn">
			<Original>Charge Fare</Original>
		</Key>
	</Package>
	<Package name="Vehicle_Interaction">
		<Key ID="STR_vInAct_Title">
			<Original>Vehicle Interaction Menu</Original>
		</Key>
		<Key ID="STR_vInAct_Unflip">
			<Original>Unflip Vehicle</Original>
		</Key>
		<Key ID="STR_vInAct_GetInKart">
			<Original>Get In Kart</Original>
		</Key>
		<Key ID="STR_vInAct_PushBoat">
			<Original>Push Boat</Original>
		</Key>
		<Key ID="STR_vInAct_PushPlane">
			<Original>Push Plane</Original>
		</Key>
		<Key ID="STR_vInAct_Repair">
			<Original>Repair Vehicle</Original>
		</Key>
		<Key ID="STR_vInAct_Registration">
			<Original>Registration</Original>
		</Key>
		<Key ID="STR_vInAct_SearchVehicle">
			<Original>Search Vehicle</Original>
		</Key>
		<Key ID="STR_vInAct_PullOut">
			<Original>Pullout Players</Original>
		</Key>
		<Key ID="STR_vInAct_Impound">
			<Original>Impound Vehicle</Original>
		</Key>
		<Key ID="STR_vInAct_DeviceMine">
			<Original>Mine from device</Original>
		</Key>
	</Package>
	<Package name="Smartphone">
		<Key ID="STR_PM_Smartphone">
			<Original>Smartphone</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_TITLE">
			<Original>Smartphone Menu</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_PLAYERLISTTITLE">
			<Original>Player List</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_MESSAGETITLE">
			<Original>Emergency Calls!</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_RANDOMTITLE">
			<Original>Select a Message</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_SCHREIBEN">
			<Original>Compose</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_NOTRUF">
			<Original>Emergency</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_Absenden">
			<Original>Send</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_NACHRICHTTITLE">
			<Original>Message:</Original>
		</Key>
		<Key ID="STR_SMARTPHONE_Notruftitle">
			<Original>Emergency Calls!</Original>
		</Key>
	</Package>
	<Package name="Session_Strings">
		<Key ID="STR_Session_Query">
			<Original>Sending request to server for player information UID [%1]</Original>
		</Key>
		<Key ID="STR_Session_QueryFail">
			<Original>The server didn't find any player information matching your UID, attempting to add player to system.</Original>
		</Key>
		<Key ID="STR_Session_Error">
			<Original>There was an error in trying to setup your client.</Original>
		</Key>
		<Key ID="STR_Session_Received">
			<Original>Received request from server... Validating...</Original>
		</Key>
		<Key ID="STR_Session_SyncdAlready">
			<Original>You have already used the sync option, you can only use this feature once every 5 minutes.</Original>
		</Key>
		<Key ID="STR_Session_SyncCheater">
			<Original>Because of some cheater corrupting the BIS MP Framework they have stopped you from enjoying our mission.\n\nYou can try this again in a minute if you feel it is a mistake.</Original>
		</Key>
		<Key ID="STR_Session_SyncData">
			<Original>Syncing player information to the server.\n\nPlease wait up to 20 seconds before leaving.</Original>
		</Key>
	</Package>
	<Package name="Shop_Strings">
		<Key ID="STR_Shop_ATMRobbed">
			<Original>Because you robbed a store you can't use the ATM for 3 minutes.</Original>
		</Key>
		<Key ID="STR_Shop_NoClothes">
			<Original>You didn't choose the clothes you wanted to buy.</Original>
		</Key>
		<Key ID="STR_Shop_NotEnoughClothes">
			<Original>Sorry sir, you don't have enough money to buy those clothes.</Original>
		</Key>
		<Key ID="STR_Shop_Total">
			<Original>Total:</Original>
		</Key>
		<Key ID="STR_Shop_NoSelection">
			<Original>No Selection</Original>
		</Key>
		<Key ID="STR_Shop_NoDisplay">
			<Original>No Display</Original>
		</Key>
		<Key ID="STR_Shop_NoVehNear">
			<Original>There are no vehicles near to sell.</Original>
		</Key>
		<Key ID="STR_Shop_ChopShopError">
			<Original>There was a problem opening the chop shop menu.</Original>
		</Key>
		<Key ID="STR_Shop_ChopShopSelling">
			<Original>Selling vehicle please wait...</Original>
		</Key>
		<Key ID="STR_Shop_NotaCiv">
			<Original>You need to be a civilian to use this store!</Original>
		</Key>
		<Key ID="STR_Shop_NotaCop">
			<Original>You need to be a cop to use this store!</Original>
		</Key>
		<Key ID="STR_Shop_NotaReb">
			<Original>You don't have rebel training yet!</Original>
		</Key>
		<Key ID="STR_Shop_NotaDive">
			<Original>You need a Diving license to use this shop!</Original>
		</Key>
		<Key ID="STR_Shop_YouNeed">
			<Original>You need a %1 to buy from this shop!</Original>
		</Key>
		<Key ID="STR_Shop_UI_Clothing">
			<Original>Clothing</Original>
		</Key>
		<Key ID="STR_Shop_UI_Hats">
			<Original>Hats</Original>
		</Key>
		<Key ID="STR_Shop_UI_Glasses">
			<Original>Glasses</Original>
		</Key>
		<Key ID="STR_Shop_UI_Vests">
			<Original>Vests</Original>
		</Key>
		<Key ID="STR_Shop_UI_Backpack">
			<Original>Backpack</Original>
		</Key>
		<Key ID="STR_Shop_Unimpound_VehExist">
			<Original>There is currently a car there.</Original>
		</Key>
		<Key ID="STR_Shop_Unimpound_NotEnough">
			<Original>You do not have enough money on you or in your bank to get your car back.</Original>
		</Key>
		<Key ID="STR_Shop_Unimpound_Success">
			<Original>You have unimpounded your %1 for $%3</Original>
		</Key>
		<Key ID="STR_Shop_Veh_DidntPick">
			<Original>You did not pick a vehicle!</Original>
		</Key>
		<Key ID="STR_Shop_Veh_NotEnough">
			<Original>You do not have enough cash to purchase this vehicle.\n\nAmount Lacking: $%1</Original>
		</Key>
		<Key ID="STR_Shop_WarVeh_NotEnough">
			<Original>You do not have enough warpoints to purchase this vehicle.\n\nAmount Lacking: %1 Warpoints</Original>
		</Key>
		<Key ID="STR_Shop_Veh_NoLicense">
			<Original>You do not have the required license!</Original>
		</Key>
		<Key ID="STR_Shop_Veh_Block">
			<Original>There is a vehicle currently blocking the spawn point</Original>
		</Key>
		<Key ID="STR_Shop_Veh_Bought">
			<Original>You bought a %1 for $%2.\n\nYou can unlock/lock the vehicle by pressing U.</Original>
		</Key>
		<Key ID="STR_Shop_WarVeh_Bought">
			<Original>You bought a %1 for %2 Warpoints.\n\nYou can unlock/lock the vehicle by pressing U.</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_Rental">
			<Original>Rental Price:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_Ownership">
			<Original>Ownership Price:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_MaxSpeed">
			<Original>Max Speed:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_HPower">
			<Original>Horse Power:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_PSeats">
			<Original>Passenger Seats:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_Trunk">
			<Original>Trunk Capacity:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_Fuel">
			<Original>Fuel Capacity:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_Armor">
			<Original>Armor Rating:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_RetrievalP">
			<Original>Retrieval Price:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_SellP">
			<Original>Sell Price:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_UI_Color">
			<Original>Color:</Original>
		</Key>
		<Key ID="STR_Shop_Veh_NotAllowed">
			<Original>You are not allowed to use this shop!</Original>
		</Key>
		<Key ID="STR_Shop_Virt_Nothing">
			<Original>You need to select an item to buy.</Original>
		</Key>
		<Key ID="STR_Shop_Virt_NoNum">
			<Original>You didn't enter an actual number</Original>
		</Key>
		<Key ID="STR_Shop_Virt_NotEnough">
			<Original>You don't have that many items to sell!</Original>
		</Key>
		<Key ID="STR_Shop_Virt_Gang_FundsMSG">
			<Original>The gang has enough funds to pay for this, would you like to pay with the gangs funds or your own?</Original>
		</Key>
		<Key ID="STR_Shop_Virt_Gang_Funds">
			<Original>Gang Funds:</Original>
		</Key>
		<Key ID="STR_Shop_Virt_YourFunds">
			<Original>Your Cash:</Original>
		</Key>
		<Key ID="STR_Shop_Virt_YourorGang">
			<Original>Pay with cash or gang funds</Original>
		</Key>
		<Key ID="STR_Shop_Virt_UI_GangFunds">
			<Original>Gang Funds</Original>
		</Key>
		<Key ID="STR_Shop_Virt_UI_YourCash">
			<Original>Your Cash</Original>
		</Key>
		<Key ID="STR_Shop_Virt_BoughtGang">
			<Original>You bought %1 %2 for $%3 with the gangs funds</Original>
		</Key>
		<Key ID="STR_Shop_Virt_BoughtItem">
			<Original>You bought %1 %2 for $%3</Original>
		</Key>
		<Key ID="STR_Shop_Virt_SellItem">
			<Original>You sold %1 %2 for $%3</Original>
		</Key>
		<Key ID="STR_Shop_Weapon_NoSelect">
			<Original>You need to select an item to buy/sell.</Original>
		</Key>
		<Key ID="STR_Shop_Weapon_Sold">
			<Original>You sold a %1 for &lt;t color='#8cff9b'&gt;$%2&lt;/t&gt;</Original>
		</Key>
		<Key ID="STR_Shop_Weapon_BoughtGang">
			<Original>You bought a %1 for &lt;t color='#8cff9b'&gt;$%2&lt;/t&gt; with the gangs funds.</Original>
		</Key>
		<Key ID="STR_Shop_Weapon_BoughtItem">
			<Original>You bought a %1 for &lt;t color='#8cff9b'&gt;$%2&lt;/t&gt;</Original>
		</Key>
		<Key ID="STR_Shop_Weapon_ShopInv">
			<Original>Shop Inventory</Original>
		</Key>
		<Key ID="STR_Shop_Weapon_YourInv">
			<Original>Your Inventory</Original>
		</Key>
	</Package>
	<Package name="FSM_Strings">
		<Key ID="STR_FSM_Paycheck">
			<Original>You will receive your next paycheck in %1 minutes</Original>
		</Key>
		<Key ID="STR_FSM_MissedPay">
			<Original>You have missed a paycheck because you were dead.</Original>
		</Key>
		<Key ID="STR_FSM_ReceivedPay">
			<Original>You have received a paycheck of $%1</Original>
		</Key>
	</Package>
	<Package name="Player_Actions">
		<Key ID="STR_pAct_DropFishingNet">
			<Original>Drop Fishing Net</Original>
		</Key>
		<Key ID="STR_pAct_RobPerson">
			<Original>Rob Person</Original>
		</Key>
	</Package>
	<Package name="Invoice_Menu">
		<Key ID="STR_Invoice_GiveInvoice">
			<Original>Write Invoice</Original>
		</Key>
		<Key ID="STR_Invoice_PayInvoice">
			<Original>Pay Invoice</Original>
		</Key>
		<Key ID="STR_Invoice_RefuseInvoice">
			<Original>Refuse Invoice</Original>
		</Key>
	</Package>
	<Package name="Fare_Menu">
		<Key ID="STR_Fare_GiveFare">
			<Original>Charge</Original>
		</Key>
		<Key ID="STR_Fare_PayFare">
			<Original>Pay Fare</Original>
		</Key>
		<Key ID="STR_Fare_RefuseFare">
			<Original>Refuse Fare</Original>
		</Key>
	</Package>
</Project>
