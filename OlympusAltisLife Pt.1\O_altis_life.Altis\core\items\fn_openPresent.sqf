#include <zmacro.h>
//	Description: Opens presents under christmas trees, gives players items
//  File: fn_openPresent.sqf
private["_target","_item"];
_target = _this param [0,objNull,[objNull]];

if(isNull _target) exitWith {};
if(!isNil {_target getVariable "receiver"}) exitWith {};

if(scriptAvailable(300)) exitWith {hint "Don't be greedy ;)";};

_target setVariable ["receiver",getPlayerUID player,true];

titleText ["Unwrapping present...","PLAIN DOWN"];

sleep random(3);

titleText ["Happy Holidays!\nThank you for being apart of the Olympus Entertainment community.","PLAIN DOWN"];

if(isNull _target) exitWith {};
if(_target getVariable ["receiver",""] != getPlayerUID player) exitWith {};
deleteVehicle _target;

[[player,"party_horn"],"OEC_fnc_say3D",-2,false] spawn OEC_fnc_MP;

switch(playerSide) do {
	case west: {
		_item = ["donuts","coffee","salt","potato"] call BIS_fnc_selectRandom;

		[true,_item,1 + round(random(4))] call OEC_fnc_handleInv;
		[true,"fireworks",2 + round(random(5))] call OEC_fnc_handleInv;
	};
	case independent: {
		_item = ["donuts","coffee","salt"] call BIS_fnc_selectRandom;

		[true,_item,1 + round(random(4))] call OEC_fnc_handleInv;
		[true,"fireworks",2 + round(random(5))] call OEC_fnc_handleInv;
	};
	case civilian: {
		switch(true) do {
			case ((O_stats_crimes select 0) > 100000): {
				hint "HoHoho! You've been naughty this year! The APD have been notified of your position, you're welcome.";
				[[3,format["<t color='#66ccff'><t size='1.8'><t align='center'>Santa Alert<br/><t color='#ffffff'><t align='center'><t size='1'>HoHoHo! I spotted criminal %1 %2 attempting to steal presents, get em!", (player getVariable["realname",name player]), ((getPos player) call BIS_fnc_locationDescription)],false,[]],"OEC_fnc_broadcast",west,false] spawn OEC_fnc_MP;

				[true,"potato",1 + round(random(4))] call OEC_fnc_handleInv;
				[true,"salt",1 + round(random(4))] call OEC_fnc_handleInv;
				[true,"fireworks",2 + round(random(5))] call OEC_fnc_handleInv;
			};
			case ((oev_is_arrested select 0) == 1): {
				hint "HoHoho! You've been naughty this year!";

				[true,"rock",1 + round(random(4))] call OEC_fnc_handleInv;
				[true,"salt",1 + round(random(4))] call OEC_fnc_handleInv;
				[true,"fireworks",2 + round(random(5))] call OEC_fnc_handleInv;
			};
			case (oev_atmcash > 1250000):{
				hint "HoHoho! You're already a MILLIONARE! The best gifts are for the less fortunate.";

				[true,"salt",1 + round(random(4))] call OEC_fnc_handleInv;
				[true,"fireworks",2 + round(random(5))] call OEC_fnc_handleInv;
			};
			default {
				hint "HoHoho! I've got something special for you...";
				_item = ["goldbar","diamondc","silverr"] call BIS_fnc_selectRandom;

				if((missionNamespace getVariable "life_inv_fireworks") < 15) then {
					[true,_item,5 + round(random(10))] call OEC_fnc_handleInv;
				}else{
					[true,"diamondc",3 + round(random(5))] call OEC_fnc_handleInv;
				};

				[true,"fireworks",3 + round(random(5))] call OEC_fnc_handleInv;
			};
		};
	};
	default {};
};