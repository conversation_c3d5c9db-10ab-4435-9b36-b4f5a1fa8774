//------------------------------------------------------------------------------------------------
//! Session Manager Component
//! Manages player session lifecycle, initialization, and cleanup
//! Converted from Olympus Altis Life session management system
//------------------------------------------------------------------------------------------------

[ComponentEditorProps(category: "Altis Life/Session", description: "Manages player session lifecycle")]
class SessionManagerComponentClass : ScriptComponentClass
{
}

//------------------------------------------------------------------------------------------------
class SessionManagerComponent : ScriptComponent
{
	// Session state
	protected bool m_bSessionActive = false;
	protected bool m_bInitialized = false;
	protected float m_fSessionStartTime = 0;
	protected float m_fLastSaveTime = 0;
	
	// Player references
	protected PlayerController m_PlayerController;
	protected PlayerDataComponent m_PlayerData;
	protected IEntity m_PlayerEntity;
	
	// Session configuration
	[Attribute("30", UIWidgets.SpinBox, "Auto-save interval (seconds)", params: "10 300 1")]
	protected int m_iAutoSaveInterval = 30;
	
	[Attribute("true", UIWidgets.CheckBox, "Enable position tracking")]
	protected bool m_bTrackPosition = true;
	
	// Events
	ref ScriptInvoker m_OnSessionStarted = new ScriptInvoker();
	ref ScriptInvoker m_OnSessionEnded = new ScriptInvoker();
	ref ScriptInvoker m_OnPlayerInitialized = new ScriptInvoker();
	
	//------------------------------------------------------------------------------------------------
	//! Component initialization
	override void OnPostInit(IEntity owner)
	{
		super.OnPostInit(owner);
		
		m_PlayerEntity = owner;
		m_PlayerController = GetGame().GetPlayerController();
		
		if (m_PlayerController)
		{
			// Get or create player data component
			m_PlayerData = PlayerDataComponent.Cast(owner.FindComponent(PlayerDataComponent));
			if (!m_PlayerData)
			{
				Print("[SessionManager] ERROR: PlayerDataComponent not found on player entity");
				return;
			}
			
			// Listen for data loaded event
			m_PlayerData.m_OnDataLoaded.Insert(OnPlayerDataLoaded);
			
			StartSession();
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Start player session
	void StartSession()
	{
		if (m_bSessionActive)
			return;
			
		m_fSessionStartTime = GetGame().GetWorld().GetWorldTime();
		m_fLastSaveTime = m_fSessionStartTime;
		m_bSessionActive = true;
		
		Print(string.Format("[SessionManager] Session started for player: %1", m_PlayerController.GetPlayerId()));
		
		// Set up auto-save timer
		GetGame().GetCallqueue().CallLater(AutoSave, m_iAutoSaveInterval * 1000, true);
		
		// Set up position tracking
		if (m_bTrackPosition)
			GetGame().GetCallqueue().CallLater(UpdatePosition, 5000, true);
		
		m_OnSessionStarted.Invoke(this);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Called when player data is loaded
	protected void OnPlayerDataLoaded(PlayerDataComponent playerData)
	{
		if (!m_bInitialized)
		{
			InitializePlayer();
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize player after data is loaded
	protected void InitializePlayer()
	{
		if (m_bInitialized || !m_PlayerData.IsDataLoaded())
			return;
			
		Print("[SessionManager] Initializing player...");
		
		// Set player spawn position based on last known position
		vector lastPos = m_PlayerData.GetLastPosition();
		if (lastPos != Vector(0, 0, 0))
		{
			// TODO: Teleport player to last position
			Print(string.Format("[SessionManager] Player last position: %1", lastPos.ToString()));
		}
		
		// Initialize player role and permissions
		PlayerRole role = m_PlayerData.GetPlayerRole();
		InitializePlayerRole(role);
		
		// Initialize UI and HUD
		InitializePlayerUI();
		
		m_bInitialized = true;
		m_OnPlayerInitialized.Invoke(this);
		
		Print("[SessionManager] Player initialization complete");
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize player role-specific systems
	protected void InitializePlayerRole(PlayerRole role)
	{
		switch (role)
		{
			case PlayerRole.CIVILIAN:
				InitializeCivilianSystems();
				break;
				
			case PlayerRole.POLICE:
				InitializePoliceSystems();
				break;
				
			case PlayerRole.MEDICAL:
				InitializeMedicalSystems();
				break;
				
			case PlayerRole.ADMIN:
				InitializeAdminSystems();
				break;
		}
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize civilian-specific systems
	protected void InitializeCivilianSystems()
	{
		Print("[SessionManager] Initializing civilian systems");
		// TODO: Initialize civilian job system, licenses, etc.
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize police-specific systems
	protected void InitializePoliceSystems()
	{
		Print("[SessionManager] Initializing police systems");
		// TODO: Initialize police equipment, arrest system, etc.
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize medical-specific systems
	protected void InitializeMedicalSystems()
	{
		Print("[SessionManager] Initializing medical systems");
		// TODO: Initialize medical equipment, revive system, etc.
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize admin-specific systems
	protected void InitializeAdminSystems()
	{
		Print("[SessionManager] Initializing admin systems");
		// TODO: Initialize admin tools, moderation interface, etc.
	}
	
	//------------------------------------------------------------------------------------------------
	//! Initialize player UI and HUD
	protected void InitializePlayerUI()
	{
		Print("[SessionManager] Initializing player UI");
		// TODO: Initialize HUD, dialogs, and UI systems
	}
	
	//------------------------------------------------------------------------------------------------
	//! Auto-save timer callback
	protected void AutoSave()
	{
		if (!m_bSessionActive || !m_PlayerData)
			return;
			
		m_PlayerData.SavePlayerData();
		m_fLastSaveTime = GetGame().GetWorld().GetWorldTime();
	}
	
	//------------------------------------------------------------------------------------------------
	//! Update player position tracking
	protected void UpdatePosition()
	{
		if (!m_bSessionActive || !m_PlayerData || !m_PlayerEntity)
			return;
			
		vector currentPos = m_PlayerEntity.GetOrigin();
		m_PlayerData.UpdateLastPosition(currentPos);
	}
	
	//------------------------------------------------------------------------------------------------
	//! End player session
	void EndSession()
	{
		if (!m_bSessionActive)
			return;
			
		Print("[SessionManager] Ending session...");
		
		// Final save
		if (m_PlayerData)
			m_PlayerData.SavePlayerData();
		
		// Calculate session time
		float sessionDuration = GetGame().GetWorld().GetWorldTime() - m_fSessionStartTime;
		Print(string.Format("[SessionManager] Session duration: %.2f seconds", sessionDuration));
		
		m_bSessionActive = false;
		m_OnSessionEnded.Invoke(this);
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get session duration in seconds
	float GetSessionDuration()
	{
		if (!m_bSessionActive)
			return 0;
			
		return GetGame().GetWorld().GetWorldTime() - m_fSessionStartTime;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if session is active
	bool IsSessionActive()
	{
		return m_bSessionActive;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Check if player is initialized
	bool IsPlayerInitialized()
	{
		return m_bInitialized;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Get player data component
	PlayerDataComponent GetPlayerData()
	{
		return m_PlayerData;
	}
	
	//------------------------------------------------------------------------------------------------
	//! Component cleanup
	override void OnDelete(IEntity owner)
	{
		EndSession();
		super.OnDelete(owner);
	}
}
