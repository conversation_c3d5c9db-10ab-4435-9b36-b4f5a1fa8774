// File: fn_votingBooth.sqf
// Author:Ozadu
// <PERSON>le voting booth

private["_voteList","_descriptionText","_voteButton"];
params[
	["_mode","",[""]]
];

if (O_stats_playtime_civ < 1680) exitWith {closeDialog 0; hint "You can only vote in the Civilian Representative elections if you have over 1,680 minutes!";};
if !(life_voting_active) exitWith {closeDialog 0; hint "The polls closed on the first of the month! Sorry! Check the forums to see who the newly elected Civilian Representative is!";};

/*Data received from server*/
if(_mode == "data") exitWith {
	_votes = param[1,[],[[]]];
	_temp = [];
	{
		_temp pushBack (_x select 1);
	} forEach _votes;
	life_votes = _temp;
};

disableSerialization;
waitUntil{!isNull findDisplay 99111};
_voteList = ((findDisplay 99111) displayCtrl 99112);
_descriptionText = ((findDisplay 99111) displayCtrl 99113);
_voteButton = ((findDisplay 99111) displayCtrl 99115);
_fileContents = loadFile "voteCandidates.txt";
_candidates = call compile _fileContents;
hint parseText "The candidates you see listed are running for Olympus's Civilian Representative Position. This in an out of game position that is applied for on the forums every 2 months. These people will be responsible for representing the civilian population. You have two votes, so pick carefully what two people you want to vote for. You cannot change your vote once submitted.<br/><br/>More information on this can be found on our website: Olympus-Entertainment.com";

switch(_mode) do {

	/*Initial set up for the voting booth*/
	case "setup":{
		_voteButton ctrlShow false;
		if(isNil "life_votes") then {
			[[player,"data"],"OES_fnc_votingBoothServer",false] call OEC_fnc_MP;
			waitUntil{!isNil "life_votes"};
		};

		if(count (life_votes) > 1) exitWith {closeDialog 0; hint "You have already voted."};

		_count = 0;
		for "_i" from 0 to ((count _candidates)-1) do{
			_candidate = _candidates select _i;
			_candidateName = _candidate select 0;
			_candidateId = _candidate select 1;
			if(!(_candidateId in life_votes)) then {
				_voteList lbAdd (_candidateName);
				_voteList lbSetData[_count,_candidateId];
				_count = _count + 1;
			};
		};
	};

	/*On candidate list selection*/
	case "LBSelChanged":{
		_selected = lbCurSel _voteList;
		_pid = _voteList lbData _selected;
		_description = "";
		for "_i" from 0 to ((count _candidates)-1) do {
			_candidate = _candidates select _i;
			_candidateId = _candidate select 1;
			_candidateDescription = _candidate select 2;
			if(_candidateId == _pid) exitWith {
				_description = _candidateDescription;
			};
		};
		_descriptionText ctrlSetStructuredText parseText format ["<t size='0.75'>%1</t>",_description];
		_voteButton ctrlShow true;
	};

	/*Vote button clicked*/
	case "vote":{
		_selected = lbCurSel _voteList;
		_pid = _voteList lbData _selected;
		[[player,"vote",_pid],"OES_fnc_votingBoothServer",false] call OEC_fnc_MP;
		life_votes pushBack _pid;
		closeDialog 0;
	};
};