//	File: fn_wantedAdd.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Adds or appends a unit to the wanted list.
params [
	["_uid","",[""]],
	["_name","",[""]],
	["_type","",[""]],
	["_player",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]],
	["_customBounty",-1,[0]]
];

if(_uid isEqualTo "" || _type isEqualTo "" || _name isEqualTo "") exitWith {};

private _playerNetID = [_uid] call OES_fnc_getPlayer;
if !(_playerNetID isEqualTo 0) then {
	[parseNumber(_type),_uid] remoteExec ["OEC_fnc_updateWanted",_playerNetID,false];
};

//What is the crime?
_type = switch(_type) do {
 	case "1": {["Vehicular Manslaughter",35000]};
	case "2": {["Manslaughter",30000]};
	case "3": {["Escaping Jail",56000]};
	case "4": {["Assault",500]};
	case "5": {["Attempted Rape",3000]};
	case "6": {["Attempted Grand Theft Auto",5000]};
	case "7": {["Use of illegal explosives",8000]};
	case "8": {["Robbery",30000]};
	case "9": {["Kidnapping",11250]};
	case "10": {["Attempted Kidnapping",4000]};
	case "11": {["Grand Theft Auto",17500]};
	case "12": {["Petty Theft",7000]};
	case "13": {["Hit and Run",7500]};
	case "14": {["Possession of Contraband",31500]};
	case "15": {["Drug Possession",45000]};
	case "16": {["Drug Trafficking",34000]};
	case "17": {["Burglary",175000]};
	case "18": {["Organ Dealing",17000]};
	case "19": {["Driving w/o license",6250]};
	case "20": {["Driving w/o lights",2000]};
	case "21": {["Attp. Robbery",8000]};
	case "22": {["Veh. Theft", 17500]};
	case "23": {["Attp. Veh. Theft",5000]};
	case "24": {["Attp. Manslaughter",26250]};
	case "25": {["Speeding",1500]};
	case "26": {["Reckless Driving",3000]};
	case "27": {["Pos. of APD Equip.",25500]};
	case "28": {["Ilg. Aerial Veh. Landing",48750]};
	case "29": {["Operating an ilg. veh.",31500]};
	case "30": {["Hit and Run",7500]};
	case "31": {["Resisting Arrest",16500]};
	case "32": {["Verbal Threats",8000]};
	case "33": {["Verbal Insults",3000]};
	case "34": {["Entering a Police Area",6000]};
	case "35": {["Destruction of property",63750]};
	case "36": {["Pos. of firearms w/o license",11000]};
	case "37": {["Pos. of an ilg. weapon",12000]};
	case "38": {["Use of firearms within city",5000]};
	case "39": {["Hostage Situation",86500]};
	case "40": {["Terrorist Acts",93750]};
	case "41": {["Flying/Hovering below 150m",15000]};
	case "42": {["Aiding in jail break",86000]};
	case "43": {["Flying w/o a pilot license",10500]};
	case "44": {["Aiding in Reserve Robbery",112500]};
	case "45": {["Attp. Reserve Robbery",82500]};
	case "46": {["Insurance Fraud",1500]};
	case "47": {["Disobeying an Officer",8000]};
	case "48": {["Obstruction of Traffic",4625]};
	case "49": {["Weapon Trafficking",15125]};
	case "50": {["Avoiding a Checkpoint",30000]};
	case "51": {["Usage of Drugs in Public",10000]};
	case "52": {["Disturbing the Peace",1125]};
	case "53": {["LEO Manslaughter",37500]};
	case "54": {["Gov't Cyber Attack",30000]};
	case "55": {["Destruction of Gov't Property",63750]};
	case "56": {["Party to a Crime",15000]};
	case "57": {["Obstruction of Justice",15750]};
	case "58": {["Misuse of Emergency System",40000]};
	case "59": {["Aiding in BW Robbery",112500]};
	case "60": {["Gas Station Robbery",18750]};
	case "61": {["Organ Harvesting",11250]};
	case "62": {["Pos. of Illegal Organ",22500]};
	case "63": {["Gang Homicide",15000]};
	case "64": {["Unlawful Taser Usage",30000]};
	case "65": {["Attp. BW Robbery",82500]};
	case "66": {["Attp. Jail Break",63750]};
	case "67": {["Kidnapping Gov't Official",92750]};
	case "68": {["Aiding in Pharm. Robbery",40000]};
	case "69": {["Pos. of Explosives",30000]};
	case "70": {["Flying w/o Collision Lights",2000]};
	case "71": {["Attp. Bank Robbery",32500]};
	case "72": {["Aiding in Bank Robbery",81250]};
	case "73": {["Pos. of Ilg. Equipment",15000]};
	case "74": {["Public Urination",2500]};
  case "75": {["Titan Hit",15000]};
	default {[]};
};

private["_data","_crimes","_val"];

if (count _type isEqualTo 0) exitWith {};
_type set [1,(_type select 1)];
//Is there a custom bounty being sent? Set that as the pricing.
if !(_customBounty isEqualTo -1) then {_type set [1,_customBounty];};
//Search the wanted list to make sure they are not on it.
private _index = [_uid,life_wanted_list] call OEC_fnc_index;
_addedCharge = false;
if !(_index isEqualTo -1) then {
	_data = life_wanted_list select _index;
	_crimes = _data select 2;

	{
		if ((_x select 0) isEqualTo (_type select 0)) then {
			_x set [1,(_x select 1) + 1];
			_addedCharge = true;
		};
	} forEach _crimes;

if !(_addedCharge) then {
	_crimes pushBack [_type select 0,1];
};
	life_wanted_list set [_index,[_name,_uid,_crimes,((_type select 1) + (_data select 3))]];
} else {
	life_wanted_list pushBack [_name,_uid,[[(_type select 0),1]],(_type select 1)];
};
