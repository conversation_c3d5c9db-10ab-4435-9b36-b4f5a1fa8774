-- ================================================================================================
-- Reforger Altis Life Database Schema
-- Converted and optimized from Olympus Altis Life database structure
-- Normalized design with improved performance and maintainability
-- ================================================================================================

-- Create database
CREATE DATABASE IF NOT EXISTS reforger_altis_life 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE reforger_altis_life;

-- ================================================================================================
-- CORE PLAYER SYSTEM
-- ================================================================================================

-- Players table (main player data)
CREATE TABLE players (
    id INT PRIMARY KEY AUTO_INCREMENT,
    steam_id VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(64) NOT NULL,
    cash INT DEFAULT 5000,
    bank_balance INT DEFAULT 50000,
    last_position_x FLOAT DEFAULT 0,
    last_position_y FLOAT DEFAULT 0,
    last_position_z FLOAT DEFAULT 0,
    last_world VARCHAR(32) DEFAULT 'Everon',
    play_time INT DEFAULT 0,
    total_arrests INT DEFAULT 0,
    total_crimes INT DEFAULT 0,
    admin_level INT DEFAULT 0,
    developer_level INT DEFAULT 0,
    designer_level INT DEFAULT 0,
    current_title VARCHAR(64) DEFAULT '',
    hex_icon VARCHAR(8) DEFAULT '',
    hex_icon_redemptions INT DEFAULT 0,
    war_kills INT DEFAULT 0,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_server VARCHAR(32) DEFAULT '',
    last_side VARCHAR(16) DEFAULT 'civilian',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_steam_id (steam_id),
    INDEX idx_last_active (last_active),
    INDEX idx_admin_level (admin_level)
);

-- Player statistics (normalized from Olympus 78-stat system)
CREATE TABLE player_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    player_id INT NOT NULL,
    stat_type_id INT NOT NULL,
    value INT DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    UNIQUE KEY unique_player_stat (player_id, stat_type_id),
    INDEX idx_player_stat (player_id, stat_type_id)
);

-- Statistic types definition
CREATE TABLE statistic_types (
    id INT PRIMARY KEY,
    name VARCHAR(64) NOT NULL,
    category VARCHAR(32) NOT NULL,
    description TEXT,
    
    UNIQUE KEY unique_name (name)
);

-- Player roles and permissions
CREATE TABLE player_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    player_id INT NOT NULL,
    role_type ENUM('civilian', 'police', 'medical', 'admin') NOT NULL,
    rank_level INT DEFAULT 0,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT NULL,
    active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES players(id) ON DELETE SET NULL,
    INDEX idx_player_role (player_id, role_type, active)
);

-- ================================================================================================
-- LICENSE SYSTEM
-- ================================================================================================

-- License types
CREATE TABLE license_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(64) NOT NULL,
    category VARCHAR(32) NOT NULL,
    cost INT NOT NULL,
    requires_test BOOLEAN DEFAULT FALSE,
    description TEXT,
    
    UNIQUE KEY unique_name (name),
    INDEX idx_category (category)
);

-- Player licenses
CREATE TABLE player_licenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    player_id INT NOT NULL,
    license_type_id INT NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    issued_by INT NULL,
    expires_at TIMESTAMP NULL,
    revoked BOOLEAN DEFAULT FALSE,
    revoked_at TIMESTAMP NULL,
    revoked_by INT NULL,
    revoke_reason TEXT NULL,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (license_type_id) REFERENCES license_types(id) ON DELETE CASCADE,
    FOREIGN KEY (issued_by) REFERENCES players(id) ON DELETE SET NULL,
    FOREIGN KEY (revoked_by) REFERENCES players(id) ON DELETE SET NULL,
    UNIQUE KEY unique_player_license (player_id, license_type_id),
    INDEX idx_player_licenses (player_id, revoked),
    INDEX idx_license_type (license_type_id)
);

-- ================================================================================================
-- VEHICLE SYSTEM
-- ================================================================================================

-- Vehicles
CREATE TABLE vehicles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    owner_id INT NOT NULL,
    vehicle_class VARCHAR(64) NOT NULL,
    vehicle_type VARCHAR(32) NOT NULL,
    position_x FLOAT DEFAULT 0,
    position_y FLOAT DEFAULT 0,
    position_z FLOAT DEFAULT 0,
    direction FLOAT DEFAULT 0,
    damage_level FLOAT DEFAULT 0,
    fuel_level FLOAT DEFAULT 1.0,
    impounded BOOLEAN DEFAULT FALSE,
    impounded_at TIMESTAMP NULL,
    impounded_by INT NULL,
    impound_reason TEXT NULL,
    purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (owner_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (impounded_by) REFERENCES players(id) ON DELETE SET NULL,
    INDEX idx_owner (owner_id, impounded),
    INDEX idx_vehicle_type (vehicle_type),
    INDEX idx_last_used (last_used)
);

-- Vehicle inventory
CREATE TABLE vehicle_inventory (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_id INT NOT NULL,
    item_class VARCHAR(64) NOT NULL,
    quantity INT DEFAULT 1,
    
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    UNIQUE KEY unique_vehicle_item (vehicle_id, item_class),
    INDEX idx_vehicle_inventory (vehicle_id)
);

-- ================================================================================================
-- HOUSING SYSTEM
-- ================================================================================================

-- Houses
CREATE TABLE houses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    owner_id INT NULL,
    position_x FLOAT NOT NULL,
    position_y FLOAT NOT NULL,
    position_z FLOAT NOT NULL,
    house_type VARCHAR(32) NOT NULL,
    price INT NOT NULL,
    owned BOOLEAN DEFAULT FALSE,
    purchased_at TIMESTAMP NULL,
    last_accessed TIMESTAMP NULL,
    
    FOREIGN KEY (owner_id) REFERENCES players(id) ON DELETE SET NULL,
    INDEX idx_owner (owner_id, owned),
    INDEX idx_position (position_x, position_y, position_z),
    INDEX idx_house_type (house_type)
);

-- House inventory
CREATE TABLE house_inventory (
    id INT PRIMARY KEY AUTO_INCREMENT,
    house_id INT NOT NULL,
    item_class VARCHAR(64) NOT NULL,
    quantity INT DEFAULT 1,
    
    FOREIGN KEY (house_id) REFERENCES houses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_house_item (house_id, item_class),
    INDEX idx_house_inventory (house_id)
);

-- ================================================================================================
-- GANG SYSTEM
-- ================================================================================================

-- Gangs
CREATE TABLE gangs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(64) UNIQUE NOT NULL,
    tag VARCHAR(8) UNIQUE NOT NULL,
    leader_id INT NOT NULL,
    bank_balance INT DEFAULT 0,
    level INT DEFAULT 1,
    experience INT DEFAULT 0,
    max_members INT DEFAULT 8,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (leader_id) REFERENCES players(id) ON DELETE CASCADE,
    INDEX idx_name (name, active),
    INDEX idx_leader (leader_id),
    INDEX idx_level (level)
);

-- Gang members
CREATE TABLE gang_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    gang_id INT NOT NULL,
    player_id INT NOT NULL,
    rank ENUM('member', 'officer', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (gang_id) REFERENCES gangs(id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    UNIQUE KEY unique_gang_member (gang_id, player_id),
    INDEX idx_gang_members (gang_id, rank),
    INDEX idx_player_gang (player_id)
);

-- Gang territories
CREATE TABLE gang_territories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    gang_id INT NOT NULL,
    name VARCHAR(64) NOT NULL,
    center_x FLOAT NOT NULL,
    center_y FLOAT NOT NULL,
    center_z FLOAT NOT NULL,
    radius FLOAT NOT NULL,
    captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    income_rate INT DEFAULT 0,
    
    FOREIGN KEY (gang_id) REFERENCES gangs(id) ON DELETE CASCADE,
    INDEX idx_gang_territory (gang_id),
    INDEX idx_position (center_x, center_y, center_z)
);

-- ================================================================================================
-- LAW ENFORCEMENT SYSTEM
-- ================================================================================================

-- Wanted system
CREATE TABLE wanted_list (
    id INT PRIMARY KEY AUTO_INCREMENT,
    player_id INT NOT NULL,
    crime_type VARCHAR(64) NOT NULL,
    bounty_amount INT DEFAULT 0,
    issued_by INT NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    active BOOLEAN DEFAULT TRUE,
    captured_at TIMESTAMP NULL,
    captured_by INT NULL,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (issued_by) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (captured_by) REFERENCES players(id) ON DELETE SET NULL,
    INDEX idx_wanted_player (player_id, active),
    INDEX idx_bounty (bounty_amount DESC),
    INDEX idx_issued_at (issued_at)
);

-- Arrest records
CREATE TABLE arrest_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    arrested_player_id INT NOT NULL,
    arresting_officer_id INT NOT NULL,
    crime_charges TEXT NOT NULL,
    jail_time INT NOT NULL,
    fine_amount INT DEFAULT 0,
    arrested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    released_at TIMESTAMP NULL,
    
    FOREIGN KEY (arrested_player_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (arresting_officer_id) REFERENCES players(id) ON DELETE CASCADE,
    INDEX idx_arrested_player (arrested_player_id),
    INDEX idx_arresting_officer (arresting_officer_id),
    INDEX idx_arrest_date (arrested_at)
);

-- Traffic tickets
CREATE TABLE traffic_tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    player_id INT NOT NULL,
    issued_by INT NOT NULL,
    violation_type VARCHAR(64) NOT NULL,
    fine_amount INT NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid BOOLEAN DEFAULT FALSE,
    paid_at TIMESTAMP NULL,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (issued_by) REFERENCES players(id) ON DELETE CASCADE,
    INDEX idx_player_tickets (player_id, paid),
    INDEX idx_issued_by (issued_by),
    INDEX idx_issued_at (issued_at)
);

-- ================================================================================================
-- ECONOMY SYSTEM
-- ================================================================================================

-- Market data
CREATE TABLE market_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_class VARCHAR(64) NOT NULL,
    buy_price INT DEFAULT 0,
    sell_price INT DEFAULT 0,
    stock_level INT DEFAULT 0,
    max_stock INT DEFAULT 1000,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_item (item_class),
    INDEX idx_item_class (item_class),
    INDEX idx_last_updated (last_updated)
);

-- Transaction log
CREATE TABLE transaction_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    player_id INT NOT NULL,
    transaction_type ENUM('purchase', 'sale', 'transfer', 'fine', 'paycheck') NOT NULL,
    item_class VARCHAR(64) NULL,
    quantity INT DEFAULT 1,
    amount INT NOT NULL,
    balance_after INT NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    INDEX idx_player_transactions (player_id, created_at),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at)
);

-- ================================================================================================
-- Insert default data
-- ================================================================================================

-- Insert default statistic types (converted from Olympus 78-stat system)
INSERT INTO statistic_types (id, name, category, description) VALUES
(1, 'Apples Picked', 'jobs', 'Total apples picked'),
(2, 'Peaches Picked', 'jobs', 'Total peaches picked'),
(3, 'Iron Mined', 'jobs', 'Total iron ore mined'),
(4, 'Copper Mined', 'jobs', 'Total copper ore mined'),
(5, 'Salt Mined', 'jobs', 'Total salt mined'),
(6, 'Sand Mined', 'jobs', 'Total sand mined'),
(7, 'Oil Processed', 'jobs', 'Total oil processed'),
(8, 'Diamonds Mined', 'jobs', 'Total diamonds mined'),
(9, 'Fish Caught', 'jobs', 'Total fish caught'),
(10, 'Turtles Caught', 'jobs', 'Total turtles caught');

-- Insert default license types
INSERT INTO license_types (name, category, cost, requires_test, description) VALUES
('Driver License', 'civilian', 500, TRUE, 'Required to operate civilian vehicles'),
('Pilot License', 'civilian', 25000, TRUE, 'Required to operate aircraft'),
('Weapon License', 'civilian', 10000, FALSE, 'Required to carry weapons legally'),
('Mining License', 'civilian', 2500, FALSE, 'Required for mining operations'),
('Fishing License', 'civilian', 1500, FALSE, 'Required for commercial fishing'),
('Hunting License', 'civilian', 3000, FALSE, 'Required for hunting activities');

-- Insert default market data
INSERT INTO market_data (item_class, buy_price, sell_price, stock_level, max_stock) VALUES
('apple', 0, 65, 1000, 1000),
('peach', 0, 75, 1000, 1000),
('iron_ore', 0, 120, 1000, 1000),
('copper_ore', 0, 150, 1000, 1000),
('salt', 0, 180, 1000, 1000),
('oil_unprocessed', 0, 200, 1000, 1000),
('diamond_uncut', 0, 750, 1000, 1000);

COMMIT;
