//	File: fn_updateRequest.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description:
//	Ain't got time to describe it, READ THE FILE NAME!

private["_query","_thread"];
params [
	["_uid", "", [""]],
	["_name", "", [""]],
	["_side", sideUnknown, [civilian]],
	["_cash", 0, [0]],
	["_bank", 5000, [0]],
	["_coordinates", [], [[]]]
];

//Get to those error checks.
if((_uid == "") || (_name == "")) exitWith {};

private _check = (_uid find "'" != -1);
if (_check) exitWith {};

format["Player %1(%2) sync request. Side: %3, Cash: $%4, Bank: $%5, Position: %6",_name,_uid,_side,[_cash] call OEC_fnc_numberText,[_bank] call OEC_fnc_numberText,_coordinates] call HC_fnc_diagLog;

//Parse and setup some data.
_name = [_name] call HC_fnc_mresString;
_cash = [_cash] call HC_fnc_numberSafe;
_bank = [_bank] call HC_fnc_numberSafe;
_coordinates = [_coordinates] call HC_fnc_mresArray;

switch (_side) do {
	case west: {_query = format["UPDATE players SET name='%1', cash='%2', bankacc='%3' WHERE playerid='%4'",_name,_cash,_bank,_uid];};
	case civilian: {_query = format["UPDATE players SET name='%1', cash='%2', bankacc='%3', coordinates='%4' WHERE playerid='%5'",_name,_cash,_bank,_coordinates,_uid];};
	case independent: {_query = format["UPDATE players SET name='%1', cash='%2', bankacc='%3' WHERE playerid='%4'",_name,_cash,_bank,_uid];};
};

_queryResult = [_query,1] call HC_fnc_asyncCall;
