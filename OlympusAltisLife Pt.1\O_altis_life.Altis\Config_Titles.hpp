#define true 1
#define false 0

class CfgTitleCiv {
	class Novice {
        title = "Novice";
        desc = "Kill 10 people.";
        value = 10;
        perk = "";
    };
    class Thug {
        title = "Thug";
        desc = "Kill 100 people.";
        value = 100;
        perk = "";
    };
    class Capo {
        title = "Capo";
        desc = "Kill 250 people.";
        value = 250;
        perk = "";
    };
    class Psychopath {
        title = "Psychopath";
        desc = "Kill 500 people.";
        value = 500;
        perk = "";
    };
    class Stormtrooper {
        title = "Stormtrooper";
        desc = "Kill 1,000 people.";
        value = 1000;
        perk = "2.5% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Hitman {
        title = "Hitman";
        desc = "Kill 2,500 people.";
        value = 2500;
        perk = "5% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Don {
        title = "Don";
        desc = "Kill 5,000 people.";
        value = 5000;
        perk = "5% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Terminator {
        title = "Thanos";
        desc = "Kill 7,500 people.";
        value = 7500;
        perk = "5% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Killtacular {
        title = "Killtacular";
        desc = "Kill 10,000 people.";
        value = 10000;
        perk = "5% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Boss {
        title = "Boss";
        desc = "Kill 15,000 people.";
        value = 15000;
        perk = "";
    };
    class Bot {
        title = "Bot";
        desc = "Kill 10 enemy gang members.";
        value = 10;
        perk = "";
    };
    class Fighter {
        title = "Fighter";
        desc = "Kill 100 enemy gang members.";
        value = 100;
        perk = "";
    };
    class Terrorist {
        title = "Terrorist";
        desc = "Kill 250 enemy gang members.";
        value = 250;
        perk = "2% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Warmonger {
        title = "Warmonger";
        desc = "Kill 500 enemy gang members.";
        value = 500;
        perk = "5% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Warlord {
        title = "Warlord";
        desc = "Kill 1,000 enemy gang members.";
        value = 1000;
        perk = "10% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class War_Chief {
        title = "Master Chief";
        desc = "Kill 2,500 enemy gang members.";
        value = 2500;
        perk = "15% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Genocidal {
        title = "Genocidal";
        desc = "Kill 5,000 enemy gang members.";
        value = 5000;
        perk = "15% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Titan {
        title = "Titan";
        desc = "Kill 10,000 enemy gang members.";
        value = 10000;
        perk = "15% discount at rebel gun store (rifles only) and rebel vehicle shop";
    };
    class Cop_Killer {
        title = "Cop Killer";
        desc = "Kill 10 cops.";
        value = 10;
        perk = "";
    };
    class Danger_to_Society {
        title = "Danger to Society";
        desc = "Kill 50 cops.";
        value = 50;
        perk = "Pay 5% less when sent to jail";
    };
    class Bandit {
        title = "Bandit";
        desc = "Kill 100 cops.";
        value = 100;
        perk = "Pay 10% less when sent to jail";
    };
    class Outlaw {
        title = "Outlaw";
        desc = "Kill 500 cops.";
        value = 500;
        perk = "Pay 15% less when sent to jail";
    };
    class Most_Wanted {
        title = "Most Wanted";
        desc = "Kill 1,000 cops.";
        value = 1000;
        perk = "Pay 20% less when sent to jail";
    };
    class Blacklisted {
        title = "Blacklisted";
        desc = "Kill 5,000 cops.";
        value = 5000;
        perk = "Pay 25% less when sent to jail";
    };
		class Ghille_Warrior {
				title = "Ghille Warrior";
				desc = "Kill 1 person from a distance further than 1km";
				value = 1;
				perk = "";
		};
		class Sharpsh00ter {
				title = "Sharpsh00ter";
				desc = "Kill 5 people from a distance further than 1km";
				value = 5;
				perk= "";
		};
		class Chris_Kyle {
				title = "Chris Kyle";
				desc = "Kill 10 people from a distance further than 1km";
				value = 10;
				perk = "";
		};
    class Good_Samaritan {
        title = "Good Samaritan";
        desc = "Use 5 epipens.";
        value = 5;
        perk = "";
    };
    class Medicine_Man {
        title = "Medicine Man";
        desc = "Use 20 epipens.";
        value = 20;
        perk = "";
    };
    class CPR_Certified {
        title = "Unwhitelisted Medic";
        desc = "Use 50 epipens.";
        value = 50;
        perk = "5% chance not to require dopamine when you are epi'd";
    };
    class Hijacker {
        title = "Hijacker";
        desc = "Lockpick 5 vehicles.";
        value = 5;
        perk = "";
    };
    class Joyrider {
        title = "Joyrider";
        desc = "Lockpick 100 vehicles.";
        value = 100;
        perk = "Chances to get keys are increased by 5%";
    };
    class Kleptomaniac {
        title = "Kleptomaniac";
        desc = "Lockpick 250 vehicles.";
        value = 250;
        perk = "Chances to get keys are increased by 10%, 30% chance of no car alarms tripping";
    };
    class Pickpocket {
        title = "Pickpocket";
        desc = "Rob 10 other civilians of their gear.";
        value = 10;
        perk = "";
    };
    class Mugger {
        title = "Mugger";
        desc = "Rob 25 other civilians of their gear.";
        value = 25;
        perk = "";
    };
    class Crook {
        title = "Crook";
        desc = "Rob 100 other civilians of their gear.";
        value = 100;
        perk = "";
    };
    class Master_Thief {
        title = "Master Thief";
        desc = "Rob 250 other civilians of their gear.";
        value = 250;
        perk = "";
    };
    class Misdemeanor {
        title = "Misdemeanor";
        desc = "Spend 5 hours in prison.";
        value = 5;
        perk = "5% less jail time when sent";
    };
    class Felon {
        title = "Felon";
        desc = "Spend 25 hours in prison.";
        value = 25;
        perk = "10%% less jail time when sent";
    };
    class Death_Row {
        title = "Death Row";
        desc = "Spend 150 hours in prison.";
        value = 150;
        perk = "15% less jail time when sent";
    };
    class Martyr {
        title = "Martyr";
        desc = "Set off 5 suicide vests.";
        value = 5;
        perk = "Suicide Vests are 10% cheaper";
    };
    class Jihadist {
        title = "Jihadist";
        desc = "Set off 25 suicide vests.";
        value = 25;
        perk = "Suicide Vests are 20% cheaper";
    };
    class Dog_Fighter {
        title = "Dog Fighter";
        desc = "Get 10 armed plane kills.";
        value = 10;
        perk = "";
    };
    class Ace {
        title = "Ace";
        desc = "Get 50 armed plane kills.";
        value = 50;
        perk = "";
    };
    class Red_Baron {
        title = "Red Baron";
        desc = "Get 100 armed plane kills.";
        value = 100;
        perk = "";
    };
    class Cartel_Roach {
        title = "Cartel Roach";
        desc = "Get 250 armed plane kills.";
        value = 250;
        perk = "";
    };
    class Ass_Clapper {
        title = "Ass Clapper";
        desc = "Get 500 armed plane kills.";
        value = 500;
        perk = "";
    };
    class Dealer {
        title = "Dealer";
        desc = "Sell 1,000 drugs to a drug dealer.";
        value = 1000;
        perk = "";
    };
    class Distributor {
        title = "Distributor";
        desc = "Sell 10,000 drugs to a drug dealer.";
        value = 10000;
        perk = "";
    };
    class Narco {
        title = "Pablo Escobar";
        desc = "Sell 100,000 drugs to a drug dealer.";
        value = 100000;
        perk = "";
    };
    class Firestarter {
        title = "The Detanator";
        desc = "Plant 5 blasting charges.";
        value = 5;
        perk = "5% faster boltcutting at the Federal Reserve";
    };
    class Demo_Man {
        title = "Demo Expert";
        desc = "Plant 25 blasting charges.";
        value = 25;
        perk = "10% faster boltcutting at the Federal Reserve";
    };
    class Bomber_Man {
        title = "Heist Expert";
        desc = "Plant 50 blasting charges.";
        value = 50;
        perk = "25% faster boltcutting at the Federal Reserve";
    };
    class Script_Kiddie {
        title = "Script Kiddie";
        desc = "Hack the anti-air 5 times.";
        value = 5;
        perk = "5% faster at hacking Anti-Air systems";
    };
    class Hacker {
        title = "Hacker";
        desc = "Hack the anti-air 25 times.";
        value = 25;
        perk = "10% faster at hacking Anti-Air systems";
    };
    class Cyber_Terrorist {
        title = "Anonymous";
        desc = "Hack the anti-air 50 times.";
        value = 50;
        perk = "25% faster at hacking Anti-Air systems";
    };
    class A_whole_new_world {
        title = "A Whole New World";
        desc = "Record 1,500 minutes on civilian.";
        value = 1500;
        perk = "";
    };
    class Invested {
        title = "Invested";
        desc = "Record 6,000 minutes on civilian.";
        value = 6000;
        perk = "";
    };
    class Dedicated {
        title = "Dedicated";
        desc = "Record 15,000 minutes on civilian.";
        value = 15000;
        perk = "$500 increase in paychecks";
    };
    class Committed {
        title = "Committed";
        desc = "Record 30,000 minutes on civilian.";
        value = 30000;
        perk = "$1,000 increase in paychecks";
    };
    class Addicted {
        title = "Addicted";
        desc = "Record 60,000 minutes on civilian.";
        value = 60000;
        perk = "$1,500 increase in paychecks";
    };
    class Explorer {
        title = "Explorer";
        desc = "Travel 1km on foot.";
        value = 1;
        perk = "";
    };
    class Hiker {
        title = "Hiker";
        desc = "Travel 10km on foot.";
        value = 10;
        perk = "";
    };
    class Journeyor {
        title = "Journeyor";
        desc = "Travel 100km on foot.";
        value = 100;
        perk = "Redgull lasts 10% longer";
    };
    class World_Travellor {
        title = "World Traveler";
        desc = "Travel 1000km on foot.";
        value = 1000;
        perk = "Redgull lasts 25% longer";
    };
    class Blooded {
        title = "Blooded";
        desc = "Accumulate a sum of 1 death.";
        value = 1;
        perk = "";
    };
    class Death_Finds_Me_Often {
        title = "Death Finds Me Often";
        desc = "Accumulate a sum of 25 deaths.";
        value = 25;
        perk = "";
    };
    class Crippling_Depression {
        title = "Crippling Depression";
        desc = "Accumulate a sum of 100 deaths.";
        value = 100;
        perk = "";
    };
    class Masochist {
        title = "Masochist";
        desc = "Accumulate a sum of 250 deaths.";
        value = 250;
        perk = "";
    };
    class Aims_With_Steering_Wheel {
        title = "Aims With Steering Wheel";
        desc = "Accumulate a sum of 500 deaths.";
        value = 500;
        perk = "Mysterious messages will apear in sidechat upon death";
    };
    class Suicide_Artist {
        title = "Suicide Artist";
        desc = "Accumulate a sum of 1,000 deaths.";
        value = 1000;
        perk = "";
    };
    class Concerned_Citizen {
        title = "Concerned Citizen";
        desc = "Arrest 5 people as a vigilante.";
        value = 5;
        perk = "";
    };
    class Citizen_Police {
        title = "Citizen Police";
        desc = "Arrest 15 people as a vigilante.";
        value = 15;
        perk = "";
    };
    class Bail_Bondsman {
        title = "Bail Bondsman";
        desc = "Arrest 35 people as a vigilante.";
        value = 35;
        perk = "";
    };
    class Fugitive_Recovery_Agent {
        title = "Fugitive Recovery Agent";
        desc = "Arrest 50 people as a vigilante.";
        value = 50;
        perk = "";
    };
    class Federal_Marshall {
        title = "Federal Marshall";
        desc = "Arrest 100 people as a vigilante.";
        value = 100;
        perk = "5% off vigilante shop items";
    };
		class Rebel_Camper {
	    	title = "Rebel Camper";
	    	desc = "Arrest 250 people as a vigilante.";
	    	value = 250;
				perk = "";
		};
		class Vigi_Scum {
				title = "Vigi Scum";
				desc = "Arrest 300 people as a vigilante.";
				value = 300;
				perk = "";
		};
		 class Secret_Service {
				title = "Secret Service";
				desc = "Arrest 400 people as a vigilante.";
				value = 400;
				perk = "";
		};
		class Black_Red_Sweaty {
				title = "Black, Red, and Sweaty";
				desc = "Arrest 500 people as a vigilante.";
				value = 500;
				perk = "";
	    };
	 	class Boba_Fett {
				title = "Boba Fett";
				desc = "Arrest 600 people as a vigilante.";
				value = 600;
				perk = "";
		};
		class Still_Not_A_Cop {
				title = "Still not a Cop";
				desc = "Arrest 750 people as a vigilante.";
				value = 750;
				perk = "";
		};
		class Amateur_Driver {
				title = "Amateur Driver";
				desc = "Finish the go-kart time trial in under 35s.";
				value = 35;
				perk = "";
		};
    class Tough_Competition {
        title = "Tough Competition";
        desc = "Finish the go-kart time trial in under 30s.";
        value = 30;
        perk = "";
    };
    class Speed_Demon {
        title = "Speed Demon";
        desc = "Finish the go-kart time trial in under 25s.";
        value = 25;
        perk = "";
    };
     class Anti_Air {
        title = "Anti Air";
        desc = "10 successful Titan hits.";
        value = 10;
        perk = "";
    };
     class Personal_AA {
        title = "Personal AA";
        desc = "25 successful Titan hits.";
        value = 25;
        perk = "";
    };
     class Balloon_Popper {
        title = "Balloon Popper";
        desc = "50 successful Titan hits.";
        value = 50;
        perk = "";
    };
     class The_Boom_Stick {
        title = "The Boom Stick";
        desc = "100 successful Titan hits.";
        value = 100;
        perk = "";
    };
     class Fly_Swatter {
        title = "Fly Swatter";
        desc = "250 successful Titan hits.";
        value = 250;
        perk = "";
    };
     class The_Titan {
        title = "The Titan";
        desc = "500 successful Titan hits.";
        value = 500;
        perk = "";
    };
    class Assassin {
        title = "Assassin";
        desc = "1 Hit claimed.";
        value = 1;
        perk = "";
    };
    class Hired_Gun {
        title = "Hired Gun";
        desc = "5 Hits claimed.";
        value = 5;
        perk = "";
    };
     class Gangster {
        title = "Gangster";
        desc = "25 Hits claimed.";
        value = 25;
        perk = "";
    };
    class Contracted_Killer {
        title = "Contracted Killer";
        desc = "50 Hits claimed.";
        value = 50;
        perk = "";
    };
    class John_Wick {
        title = "John Wick";
        desc = "100 Hits claimed.";
        value = 100;
        perk = "";
    };
    class Hunted {
        title = "Hunted";
        desc = "5 Hits placed on you.";
        value = 5;
        perk = "";
    };
    class Hated {
        title = "Hated";
        desc = "10 Hits placed on you.";
        value = 10;
        perk = "";
    };
		class Vein_Popper {
				title = "Vein Popper";
				desc = "Use 10 lethal injections.";
				value = 10;
				perk = "0.1% faster injection into the buttocks";
		};
		class Planned_Parenthood {
				title = "Planned Parenthood";
				desc = "Use 25 lethal injections.";
				value = 25;
				perk = "0.2% faster injection into the buttocks";
		};
		class Harmacist  {
				title = "Harmacist";
				desc = "Use 50 lethal injections.";
				value = 50;
				perk = "0.3% faster injection into the buttocks";
		};
		class Touch_of_Death {
				title = "Touch of Death";
				desc = "Use 100 lethal injections.";
				value = 100;
				perk = "0.8% faster injection into the buttocks";
		};
    class Lucky {
        title = "Lucky";
        desc = "Win 5 bets.";
        value = 5;
        perk = "";
    };
    class High_roller {
        title = "High Roller";
        desc = "Win 10 bets.";
        value = 10;
        perk = "";
    };
    class On_the_Heater {
        title = "On the Heater";
        desc = " Win 25 bets.";
        value = 25;
        perk = "";
    };
    class What_are_runs {
        title = "What are Runs";
        desc = "Win 50 bets.";
        value = 50;
        perk = "";
    };
    class Iam_Casino {
        title = "I am the Casino";
        desc = "Win 100 bets.";
        value = 100;
        perk = "";
    };
    class Card_Counter {
        title = "Card Counter";
        desc = "Win 150 bets.";
        value = 150;
        perk = "";
    };
    class poker_Champ {
        title = "World Poker Champ";
        desc = "Win 250 bets.";
        value = 250;
        perk = "";
    };
    class Rip_Money {
        title = "Rip My Money";
        desc = "Lose 1 bet.";
        value = 1;
        perk = "";
    };
    class Lets_go_again {
        title = "Lets Go Again";
        desc = "Lose 5 bets.";
        value = 5;
        perk = "";
    };
    class My_money {
        title = "Can I Have My Money Back";
        desc = "Lose 10 bets.";
        value = 10;
        perk = "";
    };
    class Get_loan {
        title = "Need a 2nd Loan";
        desc = "Lose 25 bets.";
        value = 25;
        perk = "";
    };
    class Cant_count {
        title = "I Cant Count Cards";
        desc = "Lose 50 bets.";
        value = 50;
        perk = "";
    };
    class Gambling_Addict {
        title = "Gambling Addict";
        desc = "Lose 100 Bets.";
        value = 100;
        perk = "";
    };
    class Need_help {
        title = "I Need Help";
        desc = "Lose 150 bets.";
        value = 150;
        perk = "";
    };
    class Better_Anonymous {
        title = "Better Anonymous";
        desc = "Lose 250 bets.";
        value = 250;
        perk = "";
    };
		class Winner {
			title = "Winner";
			desc = "Win $1,000,000 at the casino.";
			value = 1000000;
			perk = "";
		};
		class Rolling_In_Dough {
			title = "Rolling In Dough";
			desc = "Win $5,000,000 at the casino.";
			value = 5000000;
			perk = "";
		};
		class Kenny_Rogers {
			title = "Kenny Rogers";
			desc = "Win $20,000,000 at the casino.";
			value = 20000000;
			perk = "";
		};
		class Maximum_Dopamine {
			title = "Maximum Dopamine";
			desc = "Win $50,000,000 at the casino.";
			value = 50000000;
			perk = "";
		};
		class Casino_Scripter {
			title = "Casino Scripter";
			desc = "Win $100,000,000 at the casino.";
			value = 100000000;
			perk = "";
		};
		class Loser {
			title = "Loser";
			desc = "Lose $1,000,000 at the casino.";
			value = 1000000;
			perk = "";
		};
		class Double_Or_Nothing {
			title = "Double Or Nothing";
			desc = "Lose $5,000,000 at the casino.";
			value = 5000000;
			perk = "";
		};
		class Cracked {
			title = "Cracked";
			desc = "Lose $20,000,000 at the casino.";
			value = 20000000;
			perk = "";
		};
		class RIGGED {
			title = "RIGGED!";
			desc = "Lose $50,000,000 at the casino.";
			value = 50000000;
			perk = "";
		};
		class o7 {
			title = "o7";
			desc = "Lose $100,000,000 at the casino.";
			value = 100000000;
			perk = "";
		};
		class Dice_Roller {
			title = "Dice Roller";
			desc = "Use the casino 5 times";
			value = 5;
			perk = "";
		};
		class Mildly_Addicted {
			title = "Mildly Addicted";
			desc = "Use the casino 25 times.";
			value = 25;
			perk = "";
		};
		class Loves_Losing_Money {
			title = "Loves Losing Money";
			desc = "Use the casino 50 times.";
			value = 50;
			perk = "";
		};
		class Retard {
			title = "Retard";
			desc = "Use the casino 100 times.";
			value = 100;
			perk = "";
		};
		class Dent_Head {
			title = "Dent Head";
			desc = "Use the casino 500 times.";
			value = 500;
			perk = "";
		};
};
class CfgTitleCop {
    class Deputy {
        title = "Deputy";
        desc = "Maintain the rank of Deputy.";
        value = 1;
        perk = "$300 paycheck increase";
    };
    class Patrol_Officer {
        title = "Patrol Officer";
        desc = "Maintain the rank of Patrol Officer.";
        value = 2;
        perk = "$600 paycheck increase";
    };
    class Corporal {
        title = "Corporal";
        desc = "Maintain the rank of Corporal.";
        value = 3;
        perk = "$900 paycheck increase";
    };
    class Sergeant {
        title = "Sergeant";
        desc = "Maintain the rank of Sergeant.";
        value = 6;
        perk = "$1,200 paycheck increase";
    };
    class Lieutenant {
        title = "Lieutenant";
        desc = "Maintain the rank of Lieutenant.";
        value = 7;
        perk = "$1,500 paycheck increase";
    };
    class Deputy_Chief_of_Police {
        title = "Deputy Chief of Police";
        desc = "Maintain the rank of Deputy Chief.";
        value = 8;
        perk = "$1,800 paycheck increase";
    };
    class Chief_of_Police {
        title = "Chief of Police";
        desc = "Maintain the rank of Chief of Police.";
        value = 10;
        perk = "$1,800 paycheck increase";
    };
    class Mall_Cop {
        title = "Mall Cop";
        desc = "Seize $100,000 in drugs.";
        value = 100000;
        perk = "";
    };
    class Drug_Dog {
        title = "Drug Dog";
        desc = "Seize $500,000 in drugs.";
        value = 500000;
        perk = "2% increase in profits of drug seizures";
    };
    class DEA_Agent {
        title = "DEA Agent";
        desc = "Seize $2,000,000 in drugs.";
        value = 2000000;
        perk = "5% increase in profits of drug seizures";
    };
    class Narcotics_Ninja {
        title = "Narcotics Ninja";
        desc = "Seize $7,500,000 in drugs.";
        value = 7500000;
        perk = "8% increase in profits of drug seizures";
    };
    class The_Epitome_of_Escobar {
        title = "The Epitome of Escobar";
        desc = "Seize $15,000,000 in drugs.";
        value = 15000000;
        perk = "10% increase in profits of drug seizures";
    };
    class Defender {
        title = "Defender";
        desc = "Get 25 lethals as a cop.";
        value = 25;
        perk = "";
    };
    class Enforcer {
        title = "Enforcer";
        desc = "Get 100 lethals as a cop.";
        value = 100;
        perk = "";
    };
    class Justicar {
        title = "Justicar";
        desc = "Get 250 lethals as a cop.";
        value = 250;
        perk = "";
    };
    class Sheriff_of_Slaying {
        title = "Sheriff of Slaying";
        desc = "Get 500 lethals as a cop.";
        value = 500;
        perk = "";
    };
    class The_Bandit_in_Blue {
        title = "The Bandit in Blue";
        desc = "Get 1,000 lethals as a cop.";
        value = 1000;
        perk = "";
    };
    class The_Executioner {
        title = "The Executioner";
        desc = "Get 2,000 lethals as a cop.";
        value = 2000;
        perk = "";
    };
    class Forgiving_Fuzz {
        title = "Forgiving Fuzz";
        desc = "Pardon 10 civilians of their crimes.";
        value = 10;
        perk = "";
    };
    class Merciful_Marshal {
        title = "Merciful Marshal";
        desc = "Pardon 25 civilians of their crimes.";
        value = 25;
        perk = "";
    };
    class Graceful_Gendarme {
        title = "Graceful Gendarme";
        desc = "Pardon 50 civilians of their crimes.";
        value = 50;
        perk = "3% discount at APD weapon shop";
    };
    class Amnesty_Agent {
        title = "Amnesty Agent";
        desc = "Pardon 100 civilians of their crimes.";
        value = 100;
        perk = "5% discount at APD weapon shop";
    };
    class Great_Man_Bad_Cop {
        title = "Great Man Bad Cop";
        desc = "Pardon 250 civilians of their crimes.";
        value = 250;
        perk = "10% discount at APD weapon shop";
    };
    class Rookie {
        title = "Rookie";
        desc = "Arrest 50 civilians.";
        value = 50;
        perk = "";
    };
    class Experienced_Officer {
        title = "Experienced Officer";
        desc = "Arrest 100 civilians.";
        value = 100;
        perk = "";
    };
    class Veteran_Officer {
        title = "Veteran Officer";
        desc = "Arrest 150 civilians.";
        value = 150;
        perk = "";
    };
    class Peacekeeper {
        title = "Peacekeeper";
        desc = "Arrest 200 civilians.";
        value = 200;
        perk = "";
    };
    class Altis_Finest {
        title = "Altis Finest";
        desc = "Arrest 300 civilians.";
        value = 300;
        perk = "";
    };
    class Corrections_Constable {
        title = "Corrections Constable";
        desc = "Arrest 400 civilians.";
        value = 400;
        perk = "";
    };
    class Robocop {
        title = "Robocop";
        desc = "Arrest 750 civilians.";
        value = 750;
        perk = "";
    };
    class Did_I_Do_That {
        title = "Did I Do That";
        desc = "Defuse 1 blasting charge.";
        value = 1;
        perk = "";
    };
    class Vault_Guardian {
        title = "Vault Guardian";
        desc = "Defuse 5 blasting charges.";
        value = 5;
        perk = "2.5% faster defusing of blasting charges";
    };
    class EOD_Specialist {
        title = "EOD Specialist";
        desc = "Defuse 10 blasting charges.";
        value = 10;
        perk = "5% faster defusing of blasting charges";
    };
    class The_Hurt_Locker {
        title = "The Hurt Locker";
        desc = "Defuse 20 blasting charges.";
        value = 20;
        perk = "10% faster defusing of blasting charges";
    };
    class Actual_Ninja {
        title = "Actual Ninja";
        desc = "Defuse 50 blasting charges.";
        value = 50;
        perk = "12.5% faster defusing of blasting charges";
    };
    class Cadet {
        title = "Cadet";
        desc = "Record 3,000 minutes on cop.";
        value = 3000;
        perk = "";
    };
    class Qualified {
        title = "Qualified";
        desc = "Record 9,000 minutes on cop.";
        value = 9000;
        perk = "2% discount on all APD vehicles";
    };
    class Seasoned_Veteran {
        title = "Seasoned Veteran";
        desc = "Record 15,000 minutes on cop.";
        value = 15000;
        perk = "4% discount on all APD vehicles";
    };
    class Legendary_Tenure {
        title = "Legendary Tenure";
        desc = "Record 45,000 minutes on cop.";
        value = 45000;
        perk = "6% discount on all APD vehicles";
    };
    class Career_Cop {
        title = "Career Cop";
        desc = "Record 60,000 minutes on cop.";
        value = 60000;
        perk = "8% discount on all APD vehicles";
    };
    class Should_Be_Retired {
        title = "Should Be Retired";
        desc = "Record 80,000 minutes on cop.";
        value = 80000;
        perk = "12% discount on all APD vehicles";
    };
    class Meter_Maid {
        title = "Meter Maid";
        desc = "Get 1 civilian to pay their ticket.";
        value = 1;
        perk = "";
    };
    class Debt_Collector {
        title = "Debt Collector";
        desc = "Get 50 civilians to pay their ticket.";
        value = 50;
        perk = "";
    };
    class Budget_Balancer {
        title = "Budget Balancer";
        desc = "Get 100 civilians to pay their ticket.";
        value = 100;
        perk = "";
    };
    class Federal_Salesman {
        title = "Federal Salesman";
        desc = "Get 200 civilians to pay their ticket.";
        value = 200;
        perk = "";
    };
    class Walking_Courthouse {
        title = "Walking Courthouse";
        desc = "Get 500 civilians to pay their ticket.";
        value = 500;
        perk = "";
    };
    class Pig {
        title = "Pig";
        desc = "Eat 1 donut.";
        value = 1;
        perk = "";
    };
    class Paul_Blart {
        title = "Paul Blart";
        desc = "Eat 50 donuts.";
        value = 50;
        perk = "";
    };
    class Sir_Peter_Long {
        title = "Sir Peter Long";
        desc = "Eat 500 donuts.";
        value = 500;
        perk = "";
    };
	  class Geek_Squad {
        title = "Geek Squad";
        desc = "Repair the AA 5 times.";
        value = 5;
        perk = "";
    };
    class Computer_Nerd {
        title = "Computer Nerd";
        desc = "Repair the AA 10 times.";
        value = 10;
        perk = "";
    };
    class NSA_Agent {
        title = "NSA Agent";
        desc = "Repair the AA 20 times.";
        value = 20;
        perk = "";
    };
    class Ghosthawk_Online {
        title = "Ghosthawk Online";
        desc = "Repair the AA 30 times.";
        value = 30;
        perk = "";
    };
    class Elite_Haxor {
        title = "Elite Haxor";
        desc = "Repair the AA 50 times.";
        value = 50;
        perk = "";
    };
};
class CfgTitleMedic {
    class Intern {
        title = "Blueberry";
        desc = "Record 1,000 minutes on medic.";
        value = 1000;
        perk = "";
    };
    class Resident {
        title = "Walking Medkit";
        desc = "Record 2,000 minutes on medic.";
        value = 2000;
        perk = "";
    };
    class Technician {
        title = "How Do I Windows Key";
        desc = "Record 4,000 minutes on medic.";
        value = 4000;
        perk = "";
    };
    class Aviator {
        title = "Corpsman";
        desc = "Record 7,000 minutes on medic.";
        value = 7000;
        perk = "";
    };
    class Greasemonkey {
        title = "Air Responder";
        desc = "Record 10,000 minutes on medic.";
        value = 10000;
        perk = "";
    };
    class Surgeon {
        title = "Dr. Feelgood";
        desc = "Record 20,000 minutes on medic.";
        value = 20000;
        perk = "";
    };
    class Superintendant {
        title = "Windows Key Bandit";
        desc = "Record 30,000 minutes on medic.";
        value = 30000;
        perk = "";
    };
    class Extraordinaire {
        title = "Medic Extraordinaire";
        desc = "Record 40,000 minutes on medic.";
        value = 40000;
        perk = "";
    };
    class Favorite {
        title = "The Director’s Favorite";
        desc = "Record 60,000 minutes on medic.";
        value = 60000;
        perk = "";
    };
    class Nurse {
        title = "Kavala Camper";
        desc = "Revive 1,000 players.";
        value = 1000;
        perk = "2% faster revive process";
    };
    class Doctor {
        title = "Pocket Medic";
        desc = "Revive 1,500 players.";
        value = 1500;
        perk = "2% faster revive process";
    };
    class Corpsman {
        title = "Revive Snake";
        desc = "Revive 2,500 players.";
        value = 2500;
        perk = "5% faster revive process";
    };
    class Necromancer {
        title = "Necromancer";
        desc = "Revive 5,500 players.";
        value = 5500;
        perk = "5% faster revive process";
    };
    class GoldWindows {
        title = "Golden Windows Key";
        desc = "Revive 7,000 players.";
        value = 7000;
        perk = "15% faster revive process";
    };
    class Angel {
        title = "Career Medic";
        desc = "Revive 8,500 players.";
        value = 8500;
        perk = "15% faster revive process";
    };
    class Savior {
        title = "Savior";
        desc = "Revive 10,000 players.";
        value = 10000;
        perk = "20% faster revive process";
    };
    class EMT {
        title = "Rookie Medic";
        desc = "Maintain the rank of EMT.";
        value = 1;
        perk = "";
    };
    class Basic_Paramedic {
        title = "Wingless Medic";
        desc = "Maintain the rank of Basic Paramedic.";
        value = 2;
        perk = "10% discount on Medical Expenses";
    };
    class Advanced_Paramedic {
        title = "Auto-hover Medic";
        desc = "Maintain the rank of Adv. Paramedic.";
        value = 3;
        perk = "20% discount on Medical Expenses";
    };
    class SNR {
        title = "Meritorious Medic";
        desc = "Maintain the rank of Search and Rescue.";
        value = 4;
        perk = "30% discount on Medical Expenses";
    };
    class Supervisor {
        title = "Kneepads at the Ready";
        desc = "Maintain the rank of Supervisor.";
        value = 6;
        perk = "40% discount on Medical Expenses";
    };
    class Coordinator {
        title = "The Golden Kneepads";
        desc = "Maintain the rank of Coord.";
        value = 7;
        perk = "50% discount on Medical Expenses";
    };
    class Director {
        title = "Dr. Squatch";
        desc = "Maintain the rank of RnR Director";
        value = 7;
        perk = "50% discount on Medical Expenses";
    };
    class Spark_Plug_Licker {
        title = "Spark Plug Licker";
        desc = "Repair 50 vehicles with a toolkit.";
        value = 50;
        perk = "Repair process is 5% faster";
    };
    class Grease_Monkey {
        title = "Grease Monkey";
        desc = "Repair 100 vehicles with a toolkit.";
        value = 100;
        perk = "Repair process is 10% faster";
    };
    class Gear_Head {
        title = "Gear Head";
        desc = "Repair 200 vehicles with a toolkit.";
        value = 200;
        perk = "Repair process is 15% faster";
    };
    class Master_Technician {
        title = "Master Technician";
        desc = "Repair 350 vehicles with a toolkit.";
        value = 350;
        perk = "Repair process is 20% faster";
    };
    class Human_toolkit {
        title = "Human Toolkit";
        desc = "Repair 500 vehicles with a toolkit.";
        value = 500;
        perk = "Repair process is 25% faster";
    };
    class Meter_Maids {
        title = "Meter Maids";
        desc = "Tow and Impound 25 vehicles.";
        value = 25;
        perk = "Gain an additional 2% in profits at the impound yard";
    };
    class Street_Cleaner {
        title = "Street_Cleaner";
        desc = "Tow and Impound 50 vehicles.";
        value = 50;
        perk = "Gain an additional 4% in profits at the impound yard";
    };
    class Repo_Man {
        title = "Repo Man";
        desc = "Tow and Impound 100 vehicles.";
        value = 100;
        perk = "Gain an additional 6% in profits at the impound yard";
    };
    class Automobile_Magician{
        title = "Automobile Magician";
        desc = "Tow and Impound 175 vehicles.";
        value = 175;
        perk = "Gain an additional 8% in profits at the impound yard";
    };
    class Kavala_Cleanup_Crew {
        title = "Kavala Kleanup Krew";
        desc = "Tow and Impound 250 vehicles.";
        value = 250;
        perk = "Gain an additional 10% in profits at the impound yard";
	};
};
class CfgTitleSpecial {
	class NitroDonators {
		title = "Discord Nitro Booster";
		desc = "Boosted the discord server.";
		value = 0;
		perk = "";
	};
	class Supporter {
		title = "Olympus Shareholder";
		desc = "Donated $15 to the server.";
		value = 0;
		perk = "Donation Rewards can be found at olympus-entertainment.com/topic/24915-olympus-donation-rewards/";
	};
	class MVP {
		title = "Most Valuable Piggybank";
		desc = "Donated $30 to the server.";
		value = 0;
		perk = "Donation Rewards can be found at olympus-entertainment.com/topic/24915-olympus-donation-rewards/";
	};
	class VIP {
		title = "Very Important Pockets";
		desc = "Donated $50 to the server.";
		value = 0;
		perk = "Donation Rewards can be found at olympus-entertainment.com/topic/24915-olympus-donation-rewards/";
	};
	class Elite {
		title = "Rich Bitch";
		desc = "Donated $100 to the server.";
		value = 0;
		perk = "Donation Rewards can be found at olympus-entertainment.com/topic/24915-olympus-donation-rewards/";
	};
	class Champion {
		title = "Sugar Daddy";
		desc = "Donated $250 to the server.";
		value = 0;
		perk = "Donation Rewards can be found at olympus-entertainment.com/topic/24915-olympus-donation-rewards/";
	};
	class Legendary {
		title = "Dishwasher Donator";
		desc = "Donated $500 to the server.";
		value = 0;
		perk = "Donation Rewards can be found at olympus-entertainment.com/topic/24915-olympus-donation-rewards/";
	};
		class Gang_Wars_Winner {
			title = "Gang Wars Winner";
			desc = "Win Gang Wars!";
			value = 0;
			perk = "";
		};
		class Support_Team {
			title = "Support Team";
			desc = "Support Team Member";
			value = 0;
			perk = "Support Team Hatchback";
		};
		class Ticket_Whore {
			title = "Ticket Whore";
			desc = "Support Team Member";
			value = 0;
			perk = "";
		};
		class Sr_Support_Team {
			title = "Senior Support Team";
			desc = "Sr. Support Team Member";
			value = 0;
			perk = "";
		};
		class Lead_Support {
			title = "Support Team Lead";
			desc = "Support Team Lead.";
			value = 0;
			perk = "";
		};
	class Civ_Council {
		title = "Civilian Council";
		desc = "Civilian Council Member.";
		value = 0;
		perk = "";
	};
	class The_Scat {
		title = "The Scat";
		desc = "Exclusive to DashTonic.";
		value = 0;
		perk = "";
	};
	class Tree_Wins_Again {
		title = "Norsk Statsborger";
		desc = "Exclusive to SPBojo.";
		value = 0;
		perk = "";
	};
	class Hail_Tree {
		title = "All Hail the Great Tree";
		desc = "Exclusive to R@XXY.";
		value = 0;
		perk = "";
	};
	class Raise_Hell {
		title = "Raise Hell Praise Dale";
		desc = "Exclusive to Lex Yo.";
		value = 0;
		perk = "";
	};
	class Norsk_Drittunge {
		title = "Reign of Darkness";
		desc = "Exclusive to Strikke.";
		value = 0;
		perk = "";
	};
	class The_Shadow_Director {
		title = "Has erectile dysfunction";
		desc = "Exclusive to Kyle Lake.";
		value = 0;
		perk = "";
	};
	class ModeratorOne {
		title = "Dollar Store Moderator";
		desc = "Probationary Moderator.";
		value = 0;
		perk = "";
	};
	class ModeratorOneLeader {
		title = "Dollar Store Mod Leader";
		desc = "Wannabe Admin";
		value = 0;
		perk = "";
	};
	class Liaison {
		title = "honestly a qt pie (;";
		desc = "What even is this anymore?";
		value = 0;
		perk = "";
	};
	class ModeratorTwo {
		title = "Moderator";
		desc = "Achieved the staff rank of Moderator.";
		value = 0;
		perk = "";
	};
	class Administrator {
		title = "Administrator";
		desc = "Achieved the staff rank of Admin.";
		value = 0;
		perk = "";
	};
	class Senior_Admin {
		title = "Senior Administrator";
		desc = "Achieved the staff rank of Sr Admin.";
		value = 0;
		perk = "";
	};
	class Designer {
		title = "Designer";
		desc = "Achieved the staff rank of Designer.";
		value = 0;
		perk = "";
	};
	class Developer {
		title = "Developer";
		desc = "Achieved the staff rank of Developer.";
		value = 0;
		perk = "";
	};
	class Senior_Designer {
		title = "Senior Designer";
		desc = "Achieved the staff rank of Sr Designer.";
		value = 0;
		perk = "";
	};
	class Senior_Developer {
		title = "Senior Developer";
		desc = "Achieved the staff rank of Sr Developer.";
		value = 0;
		perk = "";
	};
	class Lead_Designer {
		title = "Lead Designer";
		desc = "Either smoking or eating at Chick-fil-a.";
		value = 0;
		perk = "";
	};
	class Lead_Developer {
		title = "Lead Developer";
		desc = "AKA Shadow Owner.";
		value = 0;
		perk = "";
	};
};
class CfgTitleServerBest {
	class Big_Daddy_Warbucks {
		title = "Big Daddy Warbucks";
		desc = "Most money on the server.";
		value = 0;
		perk = "";
	};
	class Merchant_of_War {
		title = "Merchant of War";
		desc = "Most war points on the server.";
		value = 0;
		perk = "";
	};
	class Merciless_Anarchist {
		title = "Merciless Anarchist";
		desc = "Most cop kills on the server.";
		value = 0;
		perk = "";
	};
	class Hammer_Of_Justice {
		title = "Hammer of Justice";
		desc = "Most vigi. arrests on the server.";
		value = 0;
		perk = "";
	};
	class Bullet_Bill {
		title = "Bullet Bill";
		desc = "Fastest time at the go-kart time trial.";
		value = 0;
		perk = "";
	};
  class God_of_War {
    title = "God of War";
    desc = "Most War Kills";
    value = 0;
    perk = "";
  };
	class Cop_of_War {
    title = "Cop of War";
    desc = "Most Cop lethals";
    value = 0;
    perk = "";
  };
	class God_of_Casino {
    title = "God of Casino";
    desc = "Most money won at Casino";
    value = 0;
    perk = "";
  };
};
