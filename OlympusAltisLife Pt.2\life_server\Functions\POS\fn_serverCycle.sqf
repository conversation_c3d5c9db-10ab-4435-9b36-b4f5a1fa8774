//	Author: Poseidon
//	Description: Handles things for restarting server and stuff

private["_cycleLength","_startTime","_notificationTimes","_notificationServerTime","_time"];
sleep 1;
waitUntil{uiSleep 0.5; serverTime > 0 && serverTime < 259200};//Checks to make sure serverTime variable isnt still fucked
//_cycleLength = ((4 * 60) * 60) - 360; //Length of time in seconds the servers up before it restarts ---- adjust in init as well for mArma
_offset = getNumber(configFile >> "ServerCycle" >> (format ["server%1",olympus_server]) >> "offset");
_query = format["select 240-FLOOR(TIME_TO_SEC(CONVERT_TZ(NOW(), 'UTC', 'US/Eastern'))/60 + %1) %2 240 as time_remaining",_offset,"%"];
_cycleLength = ([_query,2] call OES_fnc_asyncCall select 0) * 60; // Get time until server restart in seconds
_startTime = serverTime; //Current serverTime, since serverTime is not reset after reloading missions
serv_mArmaTime = _startTime;  //---- adjust in init as well for mArma
serv_timeFucked = false;
life_martialLaw_active = false; //found a loop with an existing serverTime wait and it went here.. :D
life_martialLaw_time = serverTime; //same as above.
_notificationTimes = [3600,1800,900,300,120]; //Time in seconds before restart a notification is shown.

// server 3 gay shit for restarting at specific times
/*if (profileName == "OlympusServer3") then {
	_time = ((["SELECT TIME_TO_SEC(CURTIME())",2] call OES_fnc_asyncCall) select 0);
	if (_time <= 7200) then {
		if ((_time + _cycleLength) > 7200) then {
			_cycleLength = (_cycleLength - ((_time + _cycleLength) - 7200));
		};
	};

	if ((_time > 7200) && (_time < 50400)) then {
		if ((_time + _cycleLength) > 50400) then {
			_cycleLength = (_cycleLength - ((_time + _cycleLength) - 50400));
		};
	};
};*/

serverStartTime = _startTime;
publicVariable "serverStartTime";//Broadcast servers start time for Y-menu time till restart calculations
serverCycleLength = _cycleLength;
publicVariable "serverCycleLength";//Broadcast length of time till next restart
serverHardReboot = false;
serverUpdate = false;

_hour = getNumber(configFile >> "ServerCycle" >> (format ["server%1",olympus_server]) >> "hour");
_query = format["SELECT HOUR(CONVERT_TZ(DATE_ADD(NOW(), INTERVAL %1 MINUTE), 'UTC', 'US/Eastern'))=%2 as is_hard;",_cycleLength,_hour];
_isHard = [_query,2] call OES_fnc_asyncCall select 0;

_isLock = 0;
if(olympus_server isEqualTo 2) then {
	_query = format["SELECT TIME(CONVERT_TZ(NOW(), 'UTC', 'US/Eastern')) BETWEEN '%1:30:00' AND '%2:30:00'",_hour,_hour+12];
	_isLock = [_query,2] call OES_fnc_asyncCall select 0;
	if(_isLock isEqualTo 0) then {
		"fdFPXGkYrxwdaxf5kiE" serverCommand "#lock";
		[] spawn{
			while {true} do {
				uiSleep random(30);
				[3,"<t color='#ff2222'><t size='2.2'><t align='center'>WARNING!<br/><t color='#FFC966'><t align='center'><t size='1.2'>You are on server 2 during non-peak hours. The server will be kicking you in 1 minute. Please move to server 1 at this time. Thank you.",false,[]] remoteExec ["OEC_fnc_broadcast",-2,false];
				uiSleep 90;
				[3,"<t color='#ff2222'><t size='2.2'><t align='center'>WARNING!<br/><t color='#FFC966'><t align='center'><t size='1.2'>You are on server 2 during non-peak hours. The server will be kicking now. Please move to server 1 at this time. Thank you.",false,[]] remoteExec ["OEC_fnc_broadcast",-2,false];
				uiSleep random(15);

				{
					"fdFPXGkYrxwdaxf5kiE" serverCommand format ["#kick %1", getPlayerUID _x];
					uiSleep 0.2;
				} forEach (allPlayers - entities "HeadlessClient_F");
				uiSleep (2 * 60);
			};
		};
	};
};


if(_isHard isEqualTo 1) then {
	serverHardReboot = true;
	serv_mArmaReboot = 1;
}else{
	serverHardReboot = false;
};

[] spawn{
	waitUntil{!isNil "serverStartTime" && !isNil "serverCycleLength"};
	waitUntil{uiSleep 10; serverTime >= ((serverStartTime + serverCycleLength) - 150)};//Wait till 150 seconds before restart
	[] spawn OES_fnc_saveAllHouses;
};

[] spawn{
	waitUntil{uiSleep 10; serverTime >= (serverStartTime + serverCycleLength)};//Wait for server time to meet the cycleLength requirement
	waitUntil{olympusVehiclesSaved};
	waitUntil{olympusGangVehiclesSaved};
	if(serverHardReboot) then {
		sleep 5;
		"fdFPXGkYrxwdaxf5kiE" serverCommand "#shutdown";
	}else{
		"fdFPXGkYrxwdaxf5kiE" serverCommand "#restart";
	};
};

[] spawn{
	waitUntil{uiSleep 10; ((serverStartTime + serverCycleLength) - servertime) < -120};//Fail safe incase server dont restart for some reason, if time is negative 2 minutes restart
	if(serverHardReboot) then {
		sleep 5;
		"fdFPXGkYrxwdaxf5kiE" serverCommand "#shutdown";
	}else{
		"fdFPXGkYrxwdaxf5kiE" serverCommand "#restart";
	};
};


while{true} do {
	{
		_notificationServerTime = (serverStartTime + serverCycleLength) - _x;

		if(serverTime < _notificationServerTime) then {
			waitUntil{uiSleep 10; serverTime > _notificationServerTime || serverCycleLength != _cycleLength};
			if(serverCycleLength != _cycleLength) exitWith {};//Cycle changed, exit so new notification loop can be started

			if(serverHardReboot) then {
				if(serverUpdate) then {
					[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A server restart will occur in %1 minutes. <br /><br /><t color='#bbbbff'><t size='1.1'>An Altis Life update will also be rolling out on restart.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
					sleep 0.3;
					[[3,format["<t color='#ff8000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A server restart will occur in %1 minutes. <br /><br /><t color='#bbbbff'><t size='1.1'>An Altis Life update will also be rolling out on restart.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
					sleep 0.3;
					[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A server restart will occur in %1 minutes. <br /><br /><t color='#bbbbff'><t size='1.1'>An Altis Life update will also be rolling out on restart.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
				}else{
					[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A server restart will occur in %1 minutes.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
					sleep 0.3;
					[[3,format["<t color='#ff8000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A server restart will occur in %1 minutes.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
					sleep 0.3;
					[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A server restart will occur in %1 minutes.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
				};
			}else{
				[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A soft server restart will occur in %1 minutes. <br /><br /><t color='#bbffbb'><t size='1.1'>Soft restarts return players to lobby as the mission reloads.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
				sleep 0.3;
				[[3,format["<t color='#ff8000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A soft server restart will occur in %1 minutes. <br /><br /><t color='#bbffbb'><t size='1.1'>Soft restarts return players to lobby as the mission reloads.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
				sleep 0.3;
				[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>A soft server restart will occur in %1 minutes. <br /><br /><t color='#bbffbb'><t size='1.1'>Soft restarts return players to lobby as the mission reloads.", (_x / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
			};
		};

		if(serverCycleLength != _cycleLength) exitWith {};
	}foreach _notificationTimes;

	if(serverCycleLength == _cycleLength) exitWith {};//Server cycle has not been modified since the last time it was set, server restart is ready to happen
	if(serverCycleLength != _cycleLength) then {_cycleLength = serverCycleLength;};//Server cycle was modified, the notification loop was exitied, start it over again.
};
