# Database Schema Conversion Guide

## Overview
This document outlines the conversion of the Olympus Altis Life MySQL database schema to a Reforger-compatible structure, including data migration strategies and optimization recommendations.

## Core Tables Analysis and Conversion

### 1. Players Table Conversion

**Original Olympus Schema:**
```sql
CREATE TABLE `players` (
  `uid` int(12) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL,
  `playerid` varchar(50) NOT NULL,
  `cash` int(100) NOT NULL DEFAULT 0,
  `bankacc` int(100) NOT NULL DEFAULT ********,
  `coplevel` enum('0','1','2','3','4','5','6','7','8','9','10') NOT NULL DEFAULT '0',
  `mediclevel` enum('0','1','2','3','4','5','6','7') NOT NULL DEFAULT '7',
  `adminlevel` enum('0','1','2','3','4') NOT NULL DEFAULT '4',
  `cop_licenses` text DEFAULT NULL,
  `civ_licenses` text DEFAULT NULL,
  `med_licenses` text DEFAULT NULL,
  `cop_gear` text NOT NULL,
  `med_gear` text NOT NULL,
  `arrested` text NOT NULL,
  `aliases` text NOT NULL,
  PRIMARY KEY (`uid`),
  UNIQUE KEY `playerid` (`playerid`)
);
```

**Reforger Optimized Schema:**
```sql
CREATE TABLE `players` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `steam_id` varchar(17) NOT NULL,
  `name` varchar(64) NOT NULL,
  `cash` bigint(20) NOT NULL DEFAULT 0,
  `bank_balance` bigint(20) NOT NULL DEFAULT 50000,
  `last_position_x` float DEFAULT NULL,
  `last_position_y` float DEFAULT NULL,
  `last_position_z` float DEFAULT NULL,
  `last_world` varchar(32) DEFAULT 'Everon',
  `play_time` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `steam_id` (`steam_id`),
  INDEX `idx_last_seen` (`last_seen`),
  INDEX `idx_name` (`name`)
);
```

### 2. Player Roles and Permissions

**New Normalized Structure:**
```sql
CREATE TABLE `player_roles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `player_id` bigint(20) NOT NULL,
  `role_type` enum('civilian','police','medical','admin') NOT NULL,
  `rank_level` tinyint(3) NOT NULL DEFAULT 0,
  `permissions` json DEFAULT NULL,
  `active` boolean NOT NULL DEFAULT true,
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `assigned_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`player_id`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assigned_by`) REFERENCES `players`(`id`) ON DELETE SET NULL,
  UNIQUE KEY `unique_player_role` (`player_id`, `role_type`)
);
```

### 3. Licenses System

**Original Olympus (Text Fields):**
- `cop_licenses`, `civ_licenses`, `med_licenses` as TEXT fields

**Reforger Normalized Structure:**
```sql
CREATE TABLE `license_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `category` enum('civilian','police','medical','business') NOT NULL,
  `cost` int(11) NOT NULL DEFAULT 0,
  `required_level` tinyint(3) NOT NULL DEFAULT 0,
  `description` text,
  `active` boolean NOT NULL DEFAULT true,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
);

CREATE TABLE `player_licenses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `player_id` bigint(20) NOT NULL,
  `license_type_id` int(11) NOT NULL,
  `issued_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `issued_by` bigint(20) DEFAULT NULL,
  `revoked` boolean NOT NULL DEFAULT false,
  `revoked_at` timestamp NULL DEFAULT NULL,
  `revoked_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`player_id`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`license_type_id`) REFERENCES `license_types`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`issued_by`) REFERENCES `players`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`revoked_by`) REFERENCES `players`(`id`) ON DELETE SET NULL,
  UNIQUE KEY `unique_player_license` (`player_id`, `license_type_id`)
);
```

### 4. Vehicle System Conversion

**Original Olympus Schema:**
```sql
CREATE TABLE `vehicles` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `side` varchar(15) NOT NULL,
  `classname` varchar(32) NOT NULL,
  `type` varchar(12) NOT NULL,
  `pid` varchar(50) NOT NULL,
  `alive` tinyint(1) NOT NULL DEFAULT 1,
  `blacklist` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
);
```

**Reforger Enhanced Schema:**
```sql
CREATE TABLE `vehicles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `owner_id` bigint(20) NOT NULL,
  `vehicle_class` varchar(64) NOT NULL,
  `vehicle_type` enum('car','truck','boat','helicopter','plane') NOT NULL,
  `faction` enum('civilian','police','medical','gang') NOT NULL,
  `position_x` float DEFAULT NULL,
  `position_y` float DEFAULT NULL,
  `position_z` float DEFAULT NULL,
  `rotation_x` float DEFAULT 0,
  `rotation_y` float DEFAULT 0,
  `rotation_z` float DEFAULT 0,
  `world` varchar(32) DEFAULT 'Everon',
  `damage_level` float NOT NULL DEFAULT 0,
  `fuel_level` float NOT NULL DEFAULT 1,
  `inventory_data` json DEFAULT NULL,
  `modifications` json DEFAULT NULL,
  `insurance_active` boolean NOT NULL DEFAULT false,
  `insurance_expires` timestamp NULL DEFAULT NULL,
  `impounded` boolean NOT NULL DEFAULT false,
  `impounded_at` timestamp NULL DEFAULT NULL,
  `impounded_by` bigint(20) DEFAULT NULL,
  `impound_reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`owner_id`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`impounded_by`) REFERENCES `players`(`id`) ON DELETE SET NULL,
  INDEX `idx_owner` (`owner_id`),
  INDEX `idx_faction` (`faction`),
  INDEX `idx_world_position` (`world`, `position_x`, `position_y`)
);
```

### 5. Gang System Conversion

**Original Olympus Schema:**
```sql
CREATE TABLE `gangs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) DEFAULT NULL,
  `bank` int(100) DEFAULT 0,
  `active` tinyint(4) DEFAULT 1,
  `kills` int(11) NOT NULL DEFAULT 0,
  `deaths` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
);
```

**Reforger Enhanced Schema:**
```sql
CREATE TABLE `gangs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `tag` varchar(8) DEFAULT NULL,
  `leader_id` bigint(20) NOT NULL,
  `bank_balance` bigint(20) NOT NULL DEFAULT 0,
  `max_members` tinyint(3) NOT NULL DEFAULT 10,
  `level` tinyint(3) NOT NULL DEFAULT 1,
  `experience` int(11) NOT NULL DEFAULT 0,
  `reputation` int(11) NOT NULL DEFAULT 0,
  `territory_count` tinyint(3) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `active` boolean NOT NULL DEFAULT true,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`leader_id`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `tag` (`tag`)
);

CREATE TABLE `gang_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gang_id` bigint(20) NOT NULL,
  `player_id` bigint(20) NOT NULL,
  `rank` enum('member','lieutenant','captain','leader') NOT NULL DEFAULT 'member',
  `permissions` json DEFAULT NULL,
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `invited_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`gang_id`) REFERENCES `gangs`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`player_id`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`invited_by`) REFERENCES `players`(`id`) ON DELETE SET NULL,
  UNIQUE KEY `unique_gang_member` (`gang_id`, `player_id`)
);
```

### 6. Housing System Conversion

**Original Olympus Schema:**
```sql
CREATE TABLE `houses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` varchar(50) NOT NULL,
  `pos` varchar(64) DEFAULT NULL,
  `owned` tinyint(4) DEFAULT 0,
  PRIMARY KEY (`id`)
);
```

**Reforger Enhanced Schema:**
```sql
CREATE TABLE `properties` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `owner_id` bigint(20) DEFAULT NULL,
  `property_type` enum('house','apartment','garage','warehouse','shop') NOT NULL,
  `position_x` float NOT NULL,
  `position_y` float NOT NULL,
  `position_z` float NOT NULL,
  `world` varchar(32) NOT NULL DEFAULT 'Everon',
  `price` int(11) NOT NULL,
  `rent_price` int(11) DEFAULT NULL,
  `storage_capacity` int(11) NOT NULL DEFAULT 100,
  `inventory_data` json DEFAULT NULL,
  `locked` boolean NOT NULL DEFAULT true,
  `for_sale` boolean NOT NULL DEFAULT false,
  `purchased_at` timestamp NULL DEFAULT NULL,
  `last_accessed` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`owner_id`) REFERENCES `players`(`id`) ON DELETE SET NULL,
  INDEX `idx_owner` (`owner_id`),
  INDEX `idx_world_position` (`world`, `position_x`, `position_y`),
  INDEX `idx_for_sale` (`for_sale`)
);

CREATE TABLE `property_keys` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `property_id` bigint(20) NOT NULL,
  `player_id` bigint(20) NOT NULL,
  `access_level` enum('guest','resident','owner') NOT NULL DEFAULT 'guest',
  `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `granted_by` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`property_id`) REFERENCES `properties`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`player_id`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`granted_by`) REFERENCES `players`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_property_access` (`property_id`, `player_id`)
);
```

## Data Migration Strategy

### 1. Migration Scripts

**Player Data Migration:**
```sql
-- Migrate basic player data
INSERT INTO players (steam_id, name, cash, bank_balance, created_at)
SELECT 
    playerid,
    name,
    GREATEST(cash, 0),
    GREATEST(bankacc, 0),
    NOW()
FROM olympus_players
WHERE playerid IS NOT NULL AND playerid != '';

-- Migrate player roles
INSERT INTO player_roles (player_id, role_type, rank_level)
SELECT 
    p.id,
    'police',
    CAST(op.coplevel AS UNSIGNED)
FROM players p
JOIN olympus_players op ON p.steam_id = op.playerid
WHERE op.coplevel > 0;
```

### 2. Data Validation and Cleanup

**Validation Queries:**
```sql
-- Check for duplicate steam IDs
SELECT steam_id, COUNT(*) as count
FROM players
GROUP BY steam_id
HAVING count > 1;

-- Validate cash/bank balances
SELECT id, name, cash, bank_balance
FROM players
WHERE cash < 0 OR bank_balance < 0;

-- Check orphaned records
SELECT COUNT(*) as orphaned_vehicles
FROM vehicles v
LEFT JOIN players p ON v.owner_id = p.id
WHERE p.id IS NULL;
```

## Performance Optimization

### 1. Indexing Strategy

**Essential Indexes:**
```sql
-- Player lookups
CREATE INDEX idx_players_steam_id ON players(steam_id);
CREATE INDEX idx_players_name ON players(name);
CREATE INDEX idx_players_last_seen ON players(last_seen);

-- Vehicle queries
CREATE INDEX idx_vehicles_owner ON vehicles(owner_id);
CREATE INDEX idx_vehicles_faction ON vehicles(faction);
CREATE INDEX idx_vehicles_world_pos ON vehicles(world, position_x, position_y);

-- Gang system
CREATE INDEX idx_gang_members_gang ON gang_members(gang_id);
CREATE INDEX idx_gang_members_player ON gang_members(player_id);

-- Property system
CREATE INDEX idx_properties_owner ON properties(owner_id);
CREATE INDEX idx_properties_world_pos ON properties(world, position_x, position_y);
CREATE INDEX idx_properties_for_sale ON properties(for_sale);
```

### 2. Connection Pooling Configuration

**Recommended Settings:**
```ini
[mysql]
max_connections = 100
connection_timeout = 30
read_timeout = 30
write_timeout = 30
pool_size = 20
pool_timeout = 5
```

## Backup and Recovery Strategy

### 1. Automated Backups

**Daily Backup Script:**
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u backup_user -p reforger_altis_life > /backups/altis_life_$DATE.sql
gzip /backups/altis_life_$DATE.sql

# Keep only last 30 days
find /backups -name "altis_life_*.sql.gz" -mtime +30 -delete
```

### 2. Point-in-Time Recovery

**Binary Log Configuration:**
```ini
[mysqld]
log-bin = mysql-bin
binlog-format = ROW
expire_logs_days = 7
max_binlog_size = 100M
```

## Next Steps

1. **Set up new database schema** on development server
2. **Create migration scripts** for each table
3. **Test data migration** with subset of production data
4. **Implement database access layer** in Reforger
5. **Create backup and monitoring systems**
6. **Performance test** with expected load
