//------------------------------------------------------------------------------------------------
// Reforger Altis Life Server Configuration
// Converted from Olympus Altis Life configuration.sqf
//------------------------------------------------------------------------------------------------

ServerConfig {
 // Basic server settings
 ServerName "Reforger Altis Life Development Server"
 ServerDescription "Altis Life roleplay server converted from Arma 3 to Reforger"
 MaxPlayers 60
 Password ""
 AdminPassword "admin123"
 
 // Economy settings (converted from Olympus oev_ variables)
 EconomySettings {
  PaycheckAmount 450
  StartingCash 5000
  StartingBankBalance 50000
  MaxCashAmount *********
  MaxBankBalance *********
  EconomyMultiplier 1.0
  TotalCrimesThreshold 75
 }
 
 // Database settings
 DatabaseSettings {
  Host "localhost"
  Port 3306
  Database "reforger_altis_life"
  Username "altis_user"
  Password "altis_dev_pass"
  MaxConnections 10
  ConnectionTimeout 30
  QueryTimeout 10
  AutoSaveInterval 30
 }
 
 // Anti-cheat settings
 AntiCheatSettings {
  Enabled true
  SpyGlassIntegration true
  ValidationLevel "strict"
  LogSuspiciousActivity true
  AutoBanThreshold 5
 }
 
 // Job system settings
 JobSettings {
  // Gathering jobs
  ApplePickingReward 65
  ApplePickingTime 3.0
  PeachPickingReward 75
  PeachPickingTime 3.5
  
  // Mining jobs
  IronMiningReward 120
  IronMiningTime 5.0
  CopperMiningReward 150
  CopperMiningTime 5.5
  SaltMiningReward 180
  SaltMiningTime 6.0
  
  // Processing jobs
  OilProcessingReward 200
  OilProcessingTime 8.0
  DiamondCuttingReward 750
  DiamondCuttingTime 10.0
 }
 
 // License system settings
 LicenseSettings {
  DriverLicenseCost 500
  PilotLicenseCost 25000
  WeaponLicenseCost 10000
  MiningLicenseCost 2500
  FishingLicenseCost 1500
  HuntingLicenseCost 3000
  
  RequireDriverTest true
  RequirePilotTest true
  LicenseExpirationDays 30
 }
 
 // Law enforcement settings
 LawEnforcementSettings {
  MaxJailTime 45
  MinJailTime 5
  BountyMultiplier 1.5
  TicketFineMultiplier 1.0
  ArrestReward 2500
  
  // Wanted system
  WantedDecayTime 1800  // 30 minutes
  MaxWantedLevel 5
  WantedLevelThresholds {
   Level1 1000
   Level2 5000
   Level3 15000
   Level4 35000
   Level5 75000
  }
 }
 
 // Gang system settings
 GangSettings {
  MaxGangMembers 8
  GangCreationCost 75000
  TerritoryCaptureCost 50000
  TerritoryIncomeInterval 600  // 10 minutes
  MaxTerritories 3
  
  // Gang levels and benefits
  GangLevelExperience {
   Level1 0
   Level2 10000
   Level3 25000
   Level4 50000
   Level5 100000
  }
 }
 
 // Vehicle system settings
 VehicleSettings {
  VehicleDecayTime 7200  // 2 hours
  ImpoundFee 5000
  VehicleInsuranceCost 2500
  MaxVehiclesPerPlayer 5
  
  // Vehicle categories
  CivilianVehicles {
   "C_Offroad_01_F"
   "C_Hatchback_01_F"
   "C_Hatchback_01_sport_F"
   "C_SUV_01_F"
   "C_Van_01_transport_F"
   "C_Van_01_box_F"
   "C_Truck_02_transport_F"
   "C_Truck_02_covered_F"
  }
  
  PoliceVehicles {
   "B_MRAP_01_F"
   "C_Offroad_01_F"
   "B_Heli_Light_01_F"
   "I_MRAP_03_F"
   "B_APC_Wheeled_01_cannon_F"
  }
  
  MedicalVehicles {
   "C_Van_01_box_F"
   "B_Heli_Light_01_stripped_F"
   "C_Offroad_01_F"
  }
 }
 
 // Housing system settings
 HousingSettings {
  MaxHousesPerPlayer 2
  HouseDecayTime 2592000  // 30 days
  HouseMaintenanceCost 5000
  HouseMaintenanceInterval 604800  // 7 days
 }
 
 // Medical system settings
 MedicalSettings {
  ReviveTime 10.0
  BleedoutTime 300.0  // 5 minutes
  HospitalRespawnCost 5000
  MedicalSupplyCost 500
  
  // Medical items
  MedicalItems {
   "medikit"
   "firstaidkit"
   "bloodbag_o_pos"
   "bloodbag_a_pos"
   "bloodbag_b_pos"
   "bloodbag_ab_pos"
   "defibrillator"
  }
 }
 
 // Illegal items and activities
 IllegalItems {
  "optic_tws"
  "optic_tws_mg"
  "optic_nightstalker"
  "optic_thermal"
  "srifle_dmr_02_camo_f"
  "srifle_dmr_02_f"
  "srifle_dmr_02_sniper_f"
  "srifle_dmr_03_f"
  "srifle_dmr_03_khaki_f"
  "srifle_dmr_03_tan_f"
  "srifle_dmr_03_multicam_f"
  "srifle_dmr_03_woodland_f"
 }
 
 // Taser weapons
 TaserWeapons {
  "hgun_p07_snds_f"
  "hgun_pistol_heavy_01_f"
  "hgun_pistol_heavy_02_f"
 }
 
 // Police weapons
 PoliceWeapons {
  "arifle_sdar_f"
  "arifle_mx_f"
  "arifle_mxc_f"
  "arifle_mx_sw_f"
  "smg_02_f"
  "hgun_p07_f"
  "hgun_pistol_heavy_01_f"
 }
 
 // Zone configurations
 Zones {
  // Drug processing zone
  DrugProcessing {
   Name "Drug Processing"
   Type "illegal_processing"
   Position "16019.5 17.5963 12722.1"
   Radius 150.0
   PoliceAlert true
  }
  
  // Police station safe zone
  PoliceStation {
   Name "Police Station"
   Type "safe_zone"
   Position "3020.99 5.3203 3031.04"
   Radius 100.0
   PoliceAlert false
  }
  
  // Hospital safe zone
  Hospital {
   Name "Hospital"
   Type "safe_zone"
   Position "3043.96 5.3203 3003.07"
   Radius 75.0
   PoliceAlert false
  }
 }
 
 // Debug and development settings
 DebugSettings {
  EnableDebugMode false
  LogLevel "info"
  LogDatabaseQueries false
  LogPlayerActions true
  LogEconomyTransactions true
  EnablePerformanceMetrics true
 }
}
