//  File: fn_openHouseMarket.sqf
//	Author: Kurt
//	Description: Opens the house market map

disableSerialization;

["life_market_location"] call OEC_fnc_createDialog;
((findDisplay 50600) displayCtrl 50681) ctrlSetStructuredText parseText format ["<t size='1.5'><t align='center'><t color='#ff0000'>How to Use</t></t></t><br/>Click anywhere on the map to view houses that are available for purchase.<br/><br/><t color='#0000b2'>Blue</t> markers are vacant houses.<br/><t color='#ffa500'>Orange</t> markers are vacant garages.<br/><t color='#66009a'>Purple</t> markers are vacant gang sheds.<br/><br/>Double-click on a marker to view more details about the house.<br/><br/><t size='1.5'><t align='center'><t color='#ff0000'>House Details</t></t></t><br/>Name: -<br/>Max Virtual Capacity: -<br/>Max Physical Capacity: -<br/>Asking Price: -"];
//,_storageCap,_crates,[_cost] call OEC_fnc_numberText
waitUntil {!dialog};
{deleteMarkerLocal _x;} foreach oev_houseMarketIcons;
oev_houseMarketIcons = [];
oev_houseSelectPosition = [0,0,0];
oev_selectedHouse = ["",""];