//[this,'SIT_SAD1','<PERSON><PERSON><PERSON>'] call NPC_fnc_copWeapon;
params ["_obj_main","_stance",["_city","",[""]]];

if (_obj_main isKindOf "Man") then {
	[_obj_main,_stance,"COP"] call OEC_fnc_ambientAnim;
};

_obj_main addAction ["Cop Weapon Shop",OEC_fnc_weaponShopMenu,"cop_basic",1.5,false,false,"",'playerSide isEqualTo west',6];
_obj_main addAction [format['%1 ($%2)',['license_cop_air'] call OEC_fnc_varToStr,[(['cair'] call OEC_fnc_licensePrice)] call OEC_fnc_numberText],OEC_fnc_buyLicense,"cair",1.5,false,false,"",'!license_cop_air && playerSide isEqualTo west && _this getVariable "rank" >= 2',6];

if !(_city isEqualTo "") then {
	_obj_main addAction ["<t color='#FF0000'>Start Martial Law</t>",OEC_fnc_martialLaw,[0,_city],1.5,false,false,"",'playerSide isEqualTo west && _this getVariable "rank" >= 4',6];
	_obj_main addAction ["<t color='#00FF00'>End Martial Law</t>",OEC_fnc_martialLaw,[1,_city],1.5,false,false,"",'playerSide isEqualTo west && _this getVariable "rank" >= 4',6];
};

_obj_main addAction ["<t color='#FF0000'>Authorize PO Lethals</t>",OEC_fnc_poLethals,[0],1.5,false,false,"",'playerSide isEqualTo west && _this getVariable "rank" >= 6',6];
_obj_main addAction ["<t color='#00FF00'>De-Authorize PO Lethals</t>",OEC_fnc_poLethals,[1],1.5,false,false,"",'playerSide isEqualTo west && _this getVariable "rank" >= 6',6];

_obj_main addAction ["Medical Assistance",OEC_fnc_healHospital,"",1.5,false,false,"",'',6];
_obj_main addAction [format['%1 ($%2)',['license_cop_cg'] call OEC_fnc_varToStr,[(['cg'] call OEC_fnc_licensePrice)] call OEC_fnc_numberText],OEC_fnc_buyLicense,"cg",1.5,false,false,"",'!license_cop_cg && playerSide isEqualTo west && _this getVariable "rank" >= 1',6];
