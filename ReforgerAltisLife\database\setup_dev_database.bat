@echo off
REM ================================================================================================
REM Development Database Setup Script
REM Sets up MySQL database for Reforger Altis Life development
REM ================================================================================================

echo Setting up Reforger Altis Life development database...
echo.

REM Check if MySQL is installed and accessible
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MySQL is not installed or not in PATH
    echo Please install MySQL and ensure it's accessible from command line
    pause
    exit /b 1
)

echo MySQL found, proceeding with database setup...
echo.

REM Prompt for MySQL root credentials
set /p MYSQL_HOST="Enter MySQL host (default: localhost): "
if "%MYSQL_HOST%"=="" set MYSQL_HOST=localhost

set /p MYSQL_PORT="Enter MySQL port (default: 3306): "
if "%MYSQL_PORT%"=="" set MYSQL_PORT=3306

set /p MYSQL_ROOT_USER="Enter MySQL root username (default: root): "
if "%MYSQL_ROOT_USER%"=="" set MYSQL_ROOT_USER=root

echo Enter MySQL root password:
set /p MYSQL_ROOT_PASS=

echo.
echo Creating database and user...

REM Create database and user
mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u %MYSQL_ROOT_USER% -p%MYSQL_ROOT_PASS% -e "CREATE DATABASE IF NOT EXISTS reforger_altis_life CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo ERROR: Failed to create database
    pause
    exit /b 1
)

mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u %MYSQL_ROOT_USER% -p%MYSQL_ROOT_PASS% -e "CREATE USER IF NOT EXISTS 'altis_user'@'localhost' IDENTIFIED BY 'altis_dev_pass';"
if %errorlevel% neq 0 (
    echo ERROR: Failed to create user
    pause
    exit /b 1
)

mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u %MYSQL_ROOT_USER% -p%MYSQL_ROOT_PASS% -e "GRANT ALL PRIVILEGES ON reforger_altis_life.* TO 'altis_user'@'localhost';"
if %errorlevel% neq 0 (
    echo ERROR: Failed to grant privileges
    pause
    exit /b 1
)

mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u %MYSQL_ROOT_USER% -p%MYSQL_ROOT_PASS% -e "FLUSH PRIVILEGES;"

echo Database and user created successfully!
echo.

REM Import schema
echo Importing database schema...
mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u altis_user -paltis_dev_pass reforger_altis_life < schema.sql
if %errorlevel% neq 0 (
    echo ERROR: Failed to import schema
    pause
    exit /b 1
)

echo Schema imported successfully!
echo.

REM Create configuration file
echo Creating database configuration file...
(
echo # Reforger Altis Life Database Configuration
echo # Generated by setup script
echo.
echo [database]
echo host = %MYSQL_HOST%
echo port = %MYSQL_PORT%
echo database = reforger_altis_life
echo username = altis_user
echo password = altis_dev_pass
echo.
echo [connection]
echo max_connections = 10
echo connection_timeout = 30
echo query_timeout = 10
echo.
echo [development]
echo debug_queries = true
echo log_slow_queries = true
echo slow_query_threshold = 1000
) > database_config.ini

echo Configuration file created: database_config.ini
echo.

REM Test connection
echo Testing database connection...
mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u altis_user -paltis_dev_pass -e "USE reforger_altis_life; SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'reforger_altis_life';"
if %errorlevel% neq 0 (
    echo ERROR: Database connection test failed
    pause
    exit /b 1
)

echo.
echo ================================================================================================
echo Database setup completed successfully!
echo.
echo Database Details:
echo   Host: %MYSQL_HOST%
echo   Port: %MYSQL_PORT%
echo   Database: reforger_altis_life
echo   Username: altis_user
echo   Password: altis_dev_pass
echo.
echo Configuration file: database_config.ini
echo.
echo You can now start the Reforger Altis Life server.
echo ================================================================================================
echo.

pause
