//[this,'STAND','Pyrgos_APD'] call NPC_fnc_copMarket;
params ["_obj_main","_stance","_dp"];

if (_obj_main isKindOf "Man") then {
	[_obj_main,_stance,"COP"] call OEC_fnc_ambientAnim;
};

_obj_main addAction ['Cop Item Shop',OEC_fnc_virt_menu,'cop',1.5,false,false,"",'playerSide isEqualTo west',6];
_obj_main addAction ['Cop Clothing Shop',OEC_fnc_clothingMenu,'cop',1.5,false,false,"",'playerSide isEqualTo west',6];
_obj_main addAction ['Medical Assistance',OEC_fnc_healHospital,"",1.5,false,false,"",'',6];
if !(isNil "_dp") then {
	_obj_main addAction ['Deliver Package',OEC_fnc_mdFinish,nil,1.5,false,false,"",'!isNil "oev_md_point" && oev_delivery_in_progress && oev_md_point == _target && playerSide isEqualTo independent',6];
	_obj_main addAction ['Get Delivery Mission',OEC_fnc_getMDMission,_dp,1.5,false,false,"",'isNil "oev_md_point" && !oev_delivery_in_progress && playerSide isEqualTo independent',6];
};