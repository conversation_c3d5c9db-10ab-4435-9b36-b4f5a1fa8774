//  File: fn_pardon.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Modified by: Fusah
//	Description: <PERSON>les pardoning of individual charges and players.

private["_display","_list","_uid","_crime","_pName"];
disableSerialization;

_display = findDisplay 39000;
_list = _display displayCtrl 39004;
_data = lbData[39003,(lbCurSel 39003)];
_data = call compile format["%1", _data];
if(isNil "_data") exitWith {};
if(typeName _data != "ARRAY") exitWith {};
if(count _data == 0) exitWith {};
_pName = _data select 0;
_uid = _data select 1;

if ((lbCurSel 39004) == -1) then { //Check if player has a specific crime selected or not to see if a full pardon is needed or just a single charge removal
	private _action = [
	format ["Are you sure you want to pardon %1 of all their charges?",(_data select 0)],
	"Confirm Pardon",
	"Yes",
	"No"
	] call BIS_fnc_guiMessage;
	if !(_action) exitWith {};
	[1,format["%1 pardoned %2", name player, (_data select 0)]] remoteExecCall ["OEC_fnc_broadcast",-2];
	[_uid] remoteExecCall ["OES_fnc_wantedPardon",2];
	["pardons",1] spawn OEC_fnc_statArrUp;
	[] spawn OEC_fnc_wantedMenu;
	[
		["event","Pardon"],
		["player_id",getPlayerUID player],
		["target_id",_uid],
		["position",getPosATL player]
	] call OEC_fnc_logIt;
} else {
	_crime = _list lbData (lbCurSel 39004);
	if (_crime == "") exitWith {}; //spooky
	private _action = [
	format ["Are you sure you want to pardon %1 from one count of %2?",(_data select 0),_crime],
	"Confirm Pardon",
	"Yes",
	"No"
	] call BIS_fnc_guiMessage;
	if !(_action) exitWith {};
	[1,format["%1 has been pardoned from one count of %2 by %3.",_pName,_crime,name player]] remoteExec ["OEC_fnc_broadcast",west];
	[_uid,_pName,_crime] remoteExec ["OES_fnc_wantedRemoveCharge",2];
	[] spawn OEC_fnc_wantedMenu;
	[
		["event","Pardon Charge"],
		["player_id",getPlayerUID player],
		["target_id",_uid],
		["charge",_crime],
		["position",getPosATL player]
	] call OEC_fnc_logIt;
};
