//  File: fn_manageCycle.sqf
//	Author: Posei<PERSON>
//	Description: Adjusts the servers restart cycle/method/message
params [
	["_mode",-1,[0]],
	["_player",objNull,[objNull]]
];
if(_mode isEqualTo -1 || isNull _player) exitWith {};
_time = "";

["666 -LOGGED- %1 (%2) Set server cycle mode to %3",name _player,getPlayerUID _player,_mode] call OES_fnc_diagLog;

switch(_mode) do {
	case 0: {//Hard reboot so that update rolls out
		serverHardReboot = true;
		serverUpdate = true;
		serv_mArmaReboot = 2;
		[format['{"event":"Adjust Restart Mode", "mode":"hard reboot w/ update", "player":"%1", "player_id":"%2"}',name _player,getPlayerUID _player]] call OES_fnc_logIt;
	};

	case 1: {//Hard reboot, no update
		serverUpdate = false;
		serverHardReboot = true;
		serv_mArmaReboot = 1;
		[format['{"event":"Adjust Restart Mode", "mode":"hard reboot no update", "player":"%1", "player_id":"%2"}',name _player,getPlayerUID _player]] call OES_fnc_logIt;
	};

	case 2: {//Soft reboot
		serverUpdate = false;
		serverHardReboot = false;
		[format['{"event":"Adjust Restart Mode", "mode":"soft reboot", "player":"%1", "player_id":"%2"}',name _player,getPlayerUID _player]] call OES_fnc_logIt;
	};

	case 3: {//Restarts server in 60 minutes
		serverCycleLength = (((1 * 60) * 60) + 15) + (serverTime - serverStartTime);
		[[3,format["<t color='#ff8000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Restart time has been adjusted by an Admin. New restart time is in 60 minutes.", round((serverCycleLength - serverStartTime) / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
		sleep 10;
		publicVariable "serverCycleLength";//Broadcast length of time till next restart
		serv_mArmaCycle = serverCycleLength;
		_time = "60";
	};

	case 4: {//Restarts server in 30 minutes
		serverCycleLength = (((0.5 * 60) * 60) + 15) + (serverTime - serverStartTime);
		[[3,format["<t color='#ff8000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Restart time has been adjusted by an Admin. New restart time is in 30 minutes.", round((serverCycleLength - serverStartTime) / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
		sleep 10;
		publicVariable "serverCycleLength";//Broadcast length of time till next restart
		serv_mArmaCycle = serverCycleLength;
		_time = "30";
	};

	case 5: {//Restarts server in 15 minutes
		serverCycleLength = (((0.25 * 60) * 60) + 15) + (serverTime - serverStartTime);
		[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Restart time has been adjusted by an Admin. New restart time is in 15 minutes.", round((serverCycleLength - serverStartTime) / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
		sleep 10;
		publicVariable "serverCycleLength";//Broadcast length of time till next restart
		serv_mArmaCycle = serverCycleLength;
		_time = "15";
	};

	case 6: {//Restarts server in 1 minute
		serverCycleLength = (((0.01666 * 60) * 60) + 15) + (serverTime - serverStartTime);
		[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Restart time has been adjusted by an Admin. New restart time is in 1 minute!", round((serverCycleLength - serverStartTime) / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
		sleep 10;
		publicVariable "serverCycleLength";//Broadcast length of time till next restart
		serv_mArmaCycle = serverCycleLength;
		_time = "1";
	};

	case 8: {//Restarts server in 120 minutes
		serverCycleLength = (((2 * 60) * 60) + 15) + (serverTime - serverStartTime);
		[[3,format["<t color='#ff0000'><t size='2'><t align='center'>Server Notice<br/><t color='#eeeeff'><t align='center'><t size='1.2'>Restart time has been adjusted by an Admin. New restart time is in 120 minutes!", round((serverCycleLength - serverStartTime) / 60)],false,[]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;
		sleep 10;
		publicVariable "serverCycleLength";//Broadcast length of time till next restart
		serv_mArmaCycle = serverCycleLength;
		_time = "120";
	};

	case 7: {//Force saves all vehicles
		[] call OES_fnc_persistentVehiclesSave;
		[] call OES_fnc_persistentGangVehiclesSave;
		[format['{"event":"Force persist all vehicles", "player":"%1", "player_id":"%2"}',name _player,getPlayerUID _player]] call OES_fnc_logIt;
	};
};
if(_time != "") then {
	[format['{"event":"Adjust Restart Time", "time":"%1", "player":"%2", "player_id":"%3"}',_time,name _player,getPlayerUID _player]] call OES_fnc_logIt;
};
